/**
 * Integration Tests for Audit Trail Functionality
 * Tests end-to-end scenarios for audit logging and tracking
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  usePathname: () => '/audit',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock fetch for API calls
global.fetch = jest.fn();

// Mock audit log data
const mockAuditLogs = [
  {
    id: '1',
    action: 'CREATE',
    entityType: 'ROLE',
    entityId: 'role-1',
    entityName: 'Admin',
    userId: 'user-1',
    userName: '<PERSON>',
    timestamp: new Date('2024-01-01T10:00:00Z'),
    changes: {
      name: { from: null, to: 'Admin' },
      description: { from: null, to: 'Administrator role' },
      hierarchy: { from: null, to: 1 },
    },
    metadata: {
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0...',
    },
  },
  {
    id: '2',
    action: 'UPDATE',
    entityType: 'ROLE',
    entityId: 'role-1',
    entityName: 'Admin',
    userId: 'user-1',
    userName: 'John Doe',
    timestamp: new Date('2024-01-01T11:00:00Z'),
    changes: {
      description: { from: 'Administrator role', to: 'Super Administrator role' },
    },
    metadata: {
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0...',
    },
  },
  {
    id: '3',
    action: 'ASSIGN_PERMISSION',
    entityType: 'ROLE',
    entityId: 'role-1',
    entityName: 'Admin',
    userId: 'user-1',
    userName: 'John Doe',
    timestamp: new Date('2024-01-01T12:00:00Z'),
    changes: {
      permissions: { 
        from: [], 
        to: ['users.read', 'users.write'] 
      },
    },
    metadata: {
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0...',
    },
  },
  {
    id: '4',
    action: 'DELETE',
    entityType: 'PERMISSION',
    entityId: 'perm-1',
    entityName: 'users.delete',
    userId: 'user-2',
    userName: 'Jane Smith',
    timestamp: new Date('2024-01-01T13:00:00Z'),
    changes: {
      isActive: { from: true, to: false },
    },
    metadata: {
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0...',
    },
  },
];

// Audit Trail Component for testing
const AuditTrailPage = () => {
  const [auditLogs, setAuditLogs] = React.useState(mockAuditLogs);
  const [loading, setLoading] = React.useState(false);
  const [filters, setFilters] = React.useState({
    action: '',
    entityType: '',
    userId: '',
    dateFrom: '',
    dateTo: '',
  });
  const [pagination, setPagination] = React.useState({
    page: 1,
    limit: 10,
    total: mockAuditLogs.length,
    totalPages: 1,
  });

  const fetchAuditLogs = async (filterParams = filters, pageParams = pagination) => {
    setLoading(true);
    try {
      const queryParams = new URLSearchParams({
        ...filterParams,
        page: pageParams.page.toString(),
        limit: pageParams.limit.toString(),
      });

      const response = await fetch(`/api/audit-logs?${queryParams}`);
      
      if (response.ok) {
        const data = await response.json();
        setAuditLogs(data.logs);
        setPagination(data.pagination);
      }
    } catch (error) {
      console.error('Error fetching audit logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field: string, value: string) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);
    fetchAuditLogs(newFilters, { ...pagination, page: 1 });
  };

  const handlePageChange = (newPage: number) => {
    const newPagination = { ...pagination, page: newPage };
    setPagination(newPagination);
    fetchAuditLogs(filters, newPagination);
  };

  const exportAuditLogs = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/audit-logs/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filters }),
      });
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'audit-logs.csv';
        a.click();
      }
    } catch (error) {
      console.error('Error exporting audit logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return new Intl.DateTimeFormat('id-ID', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }).format(new Date(timestamp));
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'CREATE': return 'text-green-600';
      case 'UPDATE': return 'text-blue-600';
      case 'DELETE': return 'text-red-600';
      case 'ASSIGN_PERMISSION': return 'text-purple-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div data-testid="audit-trail-page">
      <h1>Audit Trail</h1>
      
      {loading && <div data-testid="loading">Loading...</div>}
      
      {/* Filters */}
      <div data-testid="filters" className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h2>Filters</h2>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <select
            value={filters.action}
            onChange={(e) => handleFilterChange('action', e.target.value)}
            data-testid="action-filter"
          >
            <option value="">All Actions</option>
            <option value="CREATE">Create</option>
            <option value="UPDATE">Update</option>
            <option value="DELETE">Delete</option>
            <option value="ASSIGN_PERMISSION">Assign Permission</option>
          </select>
          
          <select
            value={filters.entityType}
            onChange={(e) => handleFilterChange('entityType', e.target.value)}
            data-testid="entity-type-filter"
          >
            <option value="">All Types</option>
            <option value="ROLE">Role</option>
            <option value="PERMISSION">Permission</option>
            <option value="USER">User</option>
          </select>
          
          <input
            type="text"
            placeholder="User ID"
            value={filters.userId}
            onChange={(e) => handleFilterChange('userId', e.target.value)}
            data-testid="user-id-filter"
          />
          
          <input
            type="date"
            value={filters.dateFrom}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            data-testid="date-from-filter"
          />
          
          <input
            type="date"
            value={filters.dateTo}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            data-testid="date-to-filter"
          />
        </div>
        
        <button
          onClick={exportAuditLogs}
          data-testid="export-btn"
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded"
        >
          Export CSV
        </button>
      </div>

      {/* Audit Logs Table */}
      <div data-testid="audit-logs-table" className="overflow-x-auto">
        <table className="min-w-full bg-white border">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-2 text-left">Timestamp</th>
              <th className="px-4 py-2 text-left">Action</th>
              <th className="px-4 py-2 text-left">Entity</th>
              <th className="px-4 py-2 text-left">User</th>
              <th className="px-4 py-2 text-left">Changes</th>
              <th className="px-4 py-2 text-left">Details</th>
            </tr>
          </thead>
          <tbody>
            {auditLogs.map((log) => (
              <tr key={log.id} data-testid={`audit-log-${log.id}`} className="border-t">
                <td className="px-4 py-2 text-sm">
                  {formatTimestamp(log.timestamp)}
                </td>
                <td className="px-4 py-2">
                  <span className={`font-medium ${getActionColor(log.action)}`}>
                    {log.action}
                  </span>
                </td>
                <td className="px-4 py-2">
                  <div>
                    <div className="font-medium">{log.entityName}</div>
                    <div className="text-sm text-gray-500">
                      {log.entityType} ({log.entityId})
                    </div>
                  </div>
                </td>
                <td className="px-4 py-2">
                  <div>
                    <div className="font-medium">{log.userName}</div>
                    <div className="text-sm text-gray-500">{log.userId}</div>
                  </div>
                </td>
                <td className="px-4 py-2">
                  <div className="text-sm">
                    {Object.entries(log.changes).map(([field, change]) => (
                      <div key={field} className="mb-1">
                        <strong>{field}:</strong>
                        <br />
                        <span className="text-red-600">
                          {JSON.stringify(change.from)}
                        </span>
                        {' → '}
                        <span className="text-green-600">
                          {JSON.stringify(change.to)}
                        </span>
                      </div>
                    ))}
                  </div>
                </td>
                <td className="px-4 py-2">
                  <button
                    onClick={() => {
                      // Show detailed view
                      console.log('Show details for:', log.id);
                    }}
                    data-testid={`details-${log.id}`}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    View Details
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div data-testid="pagination" className="mt-4 flex justify-between items-center">
        <div>
          Showing {auditLogs.length} of {pagination.total} entries
        </div>
        <div className="flex gap-2">
          <button
            onClick={() => handlePageChange(pagination.page - 1)}
            disabled={pagination.page === 1}
            data-testid="prev-page"
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Previous
          </button>
          <span>Page {pagination.page} of {pagination.totalPages}</span>
          <button
            onClick={() => handlePageChange(pagination.page + 1)}
            disabled={pagination.page === pagination.totalPages}
            data-testid="next-page"
            className="px-3 py-1 border rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

describe('Audit Trail Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  it('should render audit trail page', () => {
    render(<AuditTrailPage />);
    
    expect(screen.getByTestId('audit-trail-page')).toBeInTheDocument();
    expect(screen.getByText('Audit Trail')).toBeInTheDocument();
    expect(screen.getByTestId('filters')).toBeInTheDocument();
    expect(screen.getByTestId('audit-logs-table')).toBeInTheDocument();
  });

  it('should display audit logs', () => {
    render(<AuditTrailPage />);

    expect(screen.getByTestId('audit-log-1')).toBeInTheDocument();
    expect(screen.getByTestId('audit-log-2')).toBeInTheDocument();
    expect(screen.getByText('CREATE')).toBeInTheDocument();
    expect(screen.getByText('UPDATE')).toBeInTheDocument();
    expect(screen.getAllByText('Admin')).toHaveLength(2);
    expect(screen.getAllByText('John Doe')).toHaveLength(3);
  });

  it('should handle action filter', async () => {
    const user = userEvent.setup();

    // Mock API response for filtered data
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        logs: mockAuditLogs.filter(log => log.action === 'CREATE'),
        pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
      }),
    });

    render(<AuditTrailPage />);

    // Select CREATE action filter
    await user.selectOptions(screen.getByTestId('action-filter'), 'CREATE');

    // Verify API call with filter
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/audit-logs?action=CREATE')
    );
  });

  it('should handle entity type filter', async () => {
    const user = userEvent.setup();

    // Mock API response for filtered data
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        logs: mockAuditLogs.filter(log => log.entityType === 'ROLE'),
        pagination: { page: 1, limit: 10, total: 3, totalPages: 1 },
      }),
    });

    render(<AuditTrailPage />);

    // Select ROLE entity type filter
    await user.selectOptions(screen.getByTestId('entity-type-filter'), 'ROLE');

    // Verify API call with filter
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/audit-logs?entityType=ROLE')
    );
  });

  it('should handle user ID filter', async () => {
    const user = userEvent.setup();

    // Mock API response for filtered data
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        logs: mockAuditLogs.filter(log => log.userId === 'user-1'),
        pagination: { page: 1, limit: 10, total: 3, totalPages: 1 },
      }),
    });

    render(<AuditTrailPage />);

    // Type in user ID filter
    await user.type(screen.getByTestId('user-id-filter'), 'user-1');

    // Verify API call with filter
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('/api/audit-logs?userId=user-1')
    );
  });

  it('should handle date range filter', async () => {
    const user = userEvent.setup();

    // Mock API response for filtered data
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        logs: mockAuditLogs,
        pagination: { page: 1, limit: 10, total: 4, totalPages: 1 },
      }),
    });

    render(<AuditTrailPage />);

    // Set date range
    await user.type(screen.getByTestId('date-from-filter'), '2024-01-01');
    await user.type(screen.getByTestId('date-to-filter'), '2024-01-31');

    // Verify API call with date filters
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('dateFrom=2024-01-01&dateTo=2024-01-31')
    );
  });

  it('should handle pagination', async () => {
    const user = userEvent.setup();

    // Mock API response for page 2
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        logs: [],
        pagination: { page: 2, limit: 10, total: 20, totalPages: 2 },
      }),
    });

    render(<AuditTrailPage />);

    // Click next page
    await user.click(screen.getByTestId('next-page'));

    // Verify API call with page parameter
    expect(global.fetch).toHaveBeenCalledWith(
      expect.stringContaining('page=2')
    );
  });

  it('should handle export functionality', async () => {
    const user = userEvent.setup();

    // Mock successful export response
    const mockBlob = new Blob(['csv,data'], { type: 'text/csv' });
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      blob: async () => mockBlob,
    });

    // Mock URL.createObjectURL
    global.URL.createObjectURL = jest.fn(() => 'blob:mock-url');

    // Mock document.createElement with proper DOM element
    const mockAnchor = document.createElement('a');
    mockAnchor.click = jest.fn();
    jest.spyOn(document, 'createElement').mockReturnValue(mockAnchor);

    render(<AuditTrailPage />);

    // Click export button
    await user.click(screen.getByTestId('export-btn'));

    // Verify API call
    expect(global.fetch).toHaveBeenCalledWith('/api/audit-logs/export', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        filters: {
          action: '',
          entityType: '',
          userId: '',
          dateFrom: '',
          dateTo: '',
        }
      }),
    });

    // Verify download was triggered
    await waitFor(() => {
      expect(mockAnchor.click).toHaveBeenCalled();
    });
  });

  it('should display change details correctly', () => {
    render(<AuditTrailPage />);

    // Check that changes are displayed with from/to values
    expect(screen.getByText('name:')).toBeInTheDocument();
    expect(screen.getByText('null')).toBeInTheDocument(); // from value
    expect(screen.getByText('"Admin"')).toBeInTheDocument(); // to value

    // Check update changes
    expect(screen.getByText('description:')).toBeInTheDocument();
    expect(screen.getByText('"Administrator role"')).toBeInTheDocument();
    expect(screen.getByText('"Super Administrator role"')).toBeInTheDocument();
  });

  it('should show loading state during operations', async () => {
    const user = userEvent.setup();

    // Mock delayed API response
    (global.fetch as jest.Mock).mockImplementation(() =>
      new Promise(resolve => setTimeout(() => resolve({
        ok: true,
        json: async () => ({ logs: [], pagination: {} })
      }), 100))
    );

    render(<AuditTrailPage />);

    // Trigger filter change
    await user.selectOptions(screen.getByTestId('action-filter'), 'CREATE');

    // Verify loading state appears
    expect(screen.getByTestId('loading')).toBeInTheDocument();

    // Wait for operation to complete
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  it('should handle view details action', async () => {
    const user = userEvent.setup();
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    render(<AuditTrailPage />);

    // Click details button for first log
    await user.click(screen.getByTestId('details-1'));

    // Verify details action was triggered
    expect(consoleSpy).toHaveBeenCalledWith('Show details for:', '1');

    consoleSpy.mockRestore();
  });

  it('should format timestamps correctly', () => {
    render(<AuditTrailPage />);

    // Check that timestamps are formatted in Indonesian locale
    const timestampElements = screen.getAllByText(/01\/01\/2024/);
    expect(timestampElements.length).toBeGreaterThan(0);
  });

  it('should apply correct action colors', () => {
    render(<AuditTrailPage />);

    // Check that different actions have different colors
    const createAction = screen.getByText('CREATE');
    const updateAction = screen.getByText('UPDATE');
    const deleteAction = screen.getByText('DELETE');
    const assignAction = screen.getByText('ASSIGN_PERMISSION');

    expect(createAction).toHaveClass('text-green-600');
    expect(updateAction).toHaveClass('text-blue-600');
    expect(deleteAction).toHaveClass('text-red-600');
    expect(assignAction).toHaveClass('text-purple-600');
  });
});
