
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">10.17% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>34/334</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">7.18% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>11/153</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">4.83% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>3/62</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">10.93% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>34/311</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="permissionService.ts"><a href="permissionService.ts.html">permissionService.ts</a></td>
	<td data-value="12.35" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 12%"></div><div class="cover-empty" style="width: 88%"></div></div>
	</td>
	<td data-value="12.35" class="pct low">12.35%</td>
	<td data-value="170" class="abs low">21/170</td>
	<td data-value="6.81" class="pct low">6.81%</td>
	<td data-value="88" class="abs low">6/88</td>
	<td data-value="8.33" class="pct low">8.33%</td>
	<td data-value="24" class="abs low">2/24</td>
	<td data-value="13.2" class="pct low">13.2%</td>
	<td data-value="159" class="abs low">21/159</td>
	</tr>

<tr>
	<td class="file low" data-value="roleService.ts"><a href="roleService.ts.html">roleService.ts</a></td>
	<td data-value="7.92" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.92" class="pct low">7.92%</td>
	<td data-value="164" class="abs low">13/164</td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="65" class="abs low">5/65</td>
	<td data-value="2.63" class="pct low">2.63%</td>
	<td data-value="38" class="abs low">1/38</td>
	<td data-value="8.55" class="pct low">8.55%</td>
	<td data-value="152" class="abs low">13/152</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-04T00:02:47.328Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    