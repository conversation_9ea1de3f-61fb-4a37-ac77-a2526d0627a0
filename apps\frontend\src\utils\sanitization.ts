/**
 * Input sanitization utilities for XSS prevention
 */

// HTML entities to escape
const HTML_ENTITIES: Record<string, string> = {
  '&': '&amp;',
  '<': '&lt;',
  '>': '&gt;',
  '"': '&quot;',
  "'": '&#x27;',
  '/': '&#x2F;',
  '`': '&#96;',
  '=': '&#x3D;'
};

/**
 * Escape HTML entities to prevent XSS attacks
 */
export const escapeHtml = (str: string): string => {
  if (typeof str !== 'string') {
    return String(str);
  }
  
  return str.replace(/[&<>"'`=/]/g, (match) => HTML_ENTITIES[match] || match);
};

/**
 * Sanitize user input for safe display
 */
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') {
    return '';
  }
  
  // Remove null bytes
  let sanitized = input.replace(/\0/g, '');
  
  // Escape HTML entities
  sanitized = escapeHtml(sanitized);
  
  // Remove potentially dangerous patterns
  sanitized = sanitized.replace(/javascript:/gi, '');
  sanitized = sanitized.replace(/vbscript:/gi, '');
  sanitized = sanitized.replace(/data:/gi, '');
  sanitized = sanitized.replace(/on\w+=/gi, '');
  
  return sanitized.trim();
};

/**
 * Sanitize role name input
 */
export const sanitizeRoleName = (name: string): string => {
  if (typeof name !== 'string') {
    return '';
  }
  
  // Allow only alphanumeric, spaces, hyphens, and underscores
  let sanitized = name.replace(/[^a-zA-Z0-9\s\-_]/g, '');
  
  // Trim and normalize spaces
  sanitized = sanitized.trim().replace(/\s+/g, ' ');
  
  // Limit length
  return sanitized.substring(0, 100);
};

/**
 * Sanitize permission name input
 */
export const sanitizePermissionName = (name: string): string => {
  if (typeof name !== 'string') {
    return '';
  }
  
  // Allow only alphanumeric, dots, hyphens, and underscores (for module.feature.action.resource format)
  let sanitized = name.replace(/[^a-zA-Z0-9\.\-_]/g, '');
  
  // Trim
  sanitized = sanitized.trim();
  
  // Limit length
  return sanitized.substring(0, 200);
};

/**
 * Sanitize description input
 */
export const sanitizeDescription = (description: string): string => {
  if (typeof description !== 'string') {
    return '';
  }
  
  // Basic HTML escaping
  let sanitized = escapeHtml(description);
  
  // Trim and normalize whitespace
  sanitized = sanitized.trim().replace(/\s+/g, ' ');
  
  // Limit length
  return sanitized.substring(0, 500);
};

/**
 * Sanitize search query
 */
export const sanitizeSearchQuery = (query: string): string => {
  if (typeof query !== 'string') {
    return '';
  }
  
  // Remove special characters that could be used for injection
  let sanitized = query.replace(/[<>'"&]/g, '');
  
  // Remove SQL injection patterns
  sanitized = sanitized.replace(/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi, '');
  
  // Trim and limit length
  return sanitized.trim().substring(0, 100);
};

/**
 * Validate and sanitize email input
 */
export const sanitizeEmail = (email: string): string => {
  if (typeof email !== 'string') {
    return '';
  }
  
  // Basic email format validation and sanitization
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  const sanitized = email.trim().toLowerCase();
  
  if (emailRegex.test(sanitized)) {
    return sanitized;
  }
  
  return '';
};

/**
 * Sanitize numeric input
 */
export const sanitizeNumber = (value: any): number | null => {
  if (typeof value === 'number' && !isNaN(value)) {
    return value;
  }
  
  if (typeof value === 'string') {
    const parsed = parseInt(value, 10);
    if (!isNaN(parsed)) {
      return parsed;
    }
  }
  
  return null;
};

/**
 * Sanitize boolean input
 */
export const sanitizeBoolean = (value: any): boolean => {
  if (typeof value === 'boolean') {
    return value;
  }
  
  if (typeof value === 'string') {
    return value.toLowerCase() === 'true';
  }
  
  return false;
};

/**
 * Sanitize UUID input
 */
export const sanitizeUUID = (uuid: string): string => {
  if (typeof uuid !== 'string') {
    return '';
  }
  
  // UUID v4 format validation
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  const sanitized = uuid.trim().toLowerCase();
  
  if (uuidRegex.test(sanitized)) {
    return sanitized;
  }
  
  return '';
};

/**
 * Comprehensive input sanitization for API requests
 */
export const sanitizeApiInput = (data: Record<string, any>): Record<string, any> => {
  const sanitized: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (value === null || value === undefined) {
      sanitized[key] = value;
      continue;
    }
    
    switch (key) {
      case 'name':
        sanitized[key] = sanitizeRoleName(value);
        break;
      case 'description':
        sanitized[key] = sanitizeDescription(value);
        break;
      case 'email':
        sanitized[key] = sanitizeEmail(value);
        break;
      case 'search':
      case 'query':
        sanitized[key] = sanitizeSearchQuery(value);
        break;
      case 'id':
      case 'roleId':
      case 'permissionId':
      case 'userId':
        sanitized[key] = sanitizeUUID(value);
        break;
      case 'hierarchy':
      case 'page':
      case 'limit':
        sanitized[key] = sanitizeNumber(value);
        break;
      case 'isActive':
      case 'enabled':
        sanitized[key] = sanitizeBoolean(value);
        break;
      default:
        if (typeof value === 'string') {
          sanitized[key] = sanitizeInput(value);
        } else {
          sanitized[key] = value;
        }
    }
  }
  
  return sanitized;
};

export default {
  escapeHtml,
  sanitizeInput,
  sanitizeRoleName,
  sanitizePermissionName,
  sanitizeDescription,
  sanitizeSearchQuery,
  sanitizeEmail,
  sanitizeNumber,
  sanitizeBoolean,
  sanitizeUUID,
  sanitizeApiInput
};
