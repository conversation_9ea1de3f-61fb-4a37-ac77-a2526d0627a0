import React from 'react';
import { render, screen } from '@testing-library/react';
import RoleList from '../RoleList';

// Simple mock data
const mockRole = {
  id: '1',
  name: 'Admin',
  description: 'Administrator role',
  hierarchy: 1,
  isActive: true,
  permissions: [],
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

const mockPagination = {
  page: 1,
  limit: 10,
  total: 1,
  totalPages: 1,
  hasNext: false,
  hasPrev: false,
};

const defaultProps = {
  roles: [mockRole],
  loading: false,
  onEdit: jest.fn(),
  onDelete: jest.fn(),
  onView: jest.fn(),
  pagination: mockPagination,
  onPageChange: jest.fn(),
  onLimitChange: jest.fn(),
};

describe('RoleList Simple Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<RoleList {...defaultProps} />);
    expect(screen.getByText('Admin')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(<RoleList {...defaultProps} loading={true} />);
    expect(document.querySelector('.animate-pulse')).toBeInTheDocument();
  });

  it('shows empty state when no roles', () => {
    render(<RoleList {...defaultProps} roles={[]} />);
    expect(screen.getByText(/tidak ada role ditemukan/i)).toBeInTheDocument();
  });

  it('displays role information', () => {
    render(<RoleList {...defaultProps} />);
    
    expect(screen.getByText('Admin')).toBeInTheDocument();
    expect(screen.getByText('Administrator role')).toBeInTheDocument();
    expect(screen.getByText(/level 1/i)).toBeInTheDocument();
  });

  it('shows role count in header', () => {
    render(<RoleList {...defaultProps} />);
    expect(screen.getByText(/daftar role \(1\)/i)).toBeInTheDocument();
  });
});
