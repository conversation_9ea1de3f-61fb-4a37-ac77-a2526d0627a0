
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for components/permissions</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> components/permissions</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">26.12% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>64/245</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">20.09% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>42/209</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">31.46% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>28/89</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">25.87% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>59/228</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="PermissionForm.tsx"><a href="PermissionForm.tsx.html">PermissionForm.tsx</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="100" class="abs low">0/100</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="116" class="abs low">0/116</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="32" class="abs low">0/32</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="94" class="abs low">0/94</td>
	</tr>

<tr>
	<td class="file high" data-value="PermissionList.tsx"><a href="PermissionList.tsx.html">PermissionList.tsx</a></td>
	<td data-value="75.29" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 75%"></div><div class="cover-empty" style="width: 25%"></div></div>
	</td>
	<td data-value="75.29" class="pct high">75.29%</td>
	<td data-value="85" class="abs high">64/85</td>
	<td data-value="75" class="pct high">75%</td>
	<td data-value="56" class="abs high">42/56</td>
	<td data-value="71.79" class="pct high">71.79%</td>
	<td data-value="39" class="abs high">28/39</td>
	<td data-value="77.63" class="pct high">77.63%</td>
	<td data-value="76" class="abs high">59/76</td>
	</tr>

<tr>
	<td class="file low" data-value="PermissionMatrix.tsx"><a href="PermissionMatrix.tsx.html">PermissionMatrix.tsx</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="60" class="abs low">0/60</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="37" class="abs low">0/37</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="18" class="abs low">0/18</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="58" class="abs low">0/58</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-03T23:39:04.054Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    