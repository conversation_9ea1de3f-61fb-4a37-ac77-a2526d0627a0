import { renderHook, act } from '@testing-library/react';
import { useRoleStore } from '../roleStore';
import { roleService } from '@/services/roleService';
import type { Role, RoleFormData } from '@/types/role';

// Mock the role service
jest.mock('@/services/roleService');
const mockRoleService = roleService as jest.Mocked<typeof roleService>;

// Mock data
const mockRole: Role = {
  id: '1',
  name: 'Admin',
  description: 'Administrator role',
  hierarchy: 1,
  isActive: true,
  permissions: [],
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

const mockRoles: Role[] = [mockRole];

const mockPaginatedResponse = {
  data: mockRoles,
  page: 1,
  totalPages: 1,
  total: 1,
  pageSize: 10,
};

const mockHierarchy = [
  {
    id: '1',
    name: 'Admin',
    level: 1,
    children: [],
  },
];

const mockStatistics = {
  totalRoles: 1,
  activeRoles: 1,
  inactiveRoles: 0,
  rolesByLevel: { '1': 1 },
};

describe('roleStore', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset store state
    useRoleStore.getState().reset();
  });

  describe('fetchRoles', () => {
    it('fetches roles successfully', async () => {
      mockRoleService.getRoles.mockResolvedValue(mockPaginatedResponse);

      const { result } = renderHook(() => useRoleStore());

      await act(async () => {
        await result.current.fetchRoles();
      });

      expect(result.current.roles).toEqual(mockRoles);
      expect(result.current.currentPage).toBe(1);
      expect(result.current.totalPages).toBe(1);
      expect(result.current.totalItems).toBe(1);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles fetch roles error', async () => {
      const errorMessage = 'Failed to fetch roles';
      mockRoleService.getRoles.mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useRoleStore());

      await act(async () => {
        await result.current.fetchRoles();
      });

      expect(result.current.roles).toEqual([]);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
    });

    it('sets loading state during fetch', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise(resolve => {
        resolvePromise = resolve;
      });
      mockRoleService.getRoles.mockReturnValue(promise);

      const { result } = renderHook(() => useRoleStore());

      act(() => {
        result.current.fetchRoles();
      });

      expect(result.current.loading).toBe(true);

      await act(async () => {
        resolvePromise!(mockPaginatedResponse);
        await promise;
      });

      expect(result.current.loading).toBe(false);
    });
  });

  describe('fetchRoleById', () => {
    it('fetches role by id successfully', async () => {
      mockRoleService.getRoleById.mockResolvedValue(mockRole);

      const { result } = renderHook(() => useRoleStore());

      await act(async () => {
        await result.current.fetchRoleById('1');
      });

      expect(result.current.selectedRole).toEqual(mockRole);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles fetch role by id error', async () => {
      const errorMessage = 'Role not found';
      mockRoleService.getRoleById.mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useRoleStore());

      await act(async () => {
        await result.current.fetchRoleById('1');
      });

      expect(result.current.selectedRole).toBe(null);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
    });
  });

  describe('createRole', () => {
    const roleFormData: RoleFormData = {
      name: 'New Role',
      description: 'New role description',
      hierarchy: 2,
      isActive: true,
      permissionIds: [],
    };

    it('creates role successfully with optimistic update', async () => {
      const newRole = { ...mockRole, id: '2', name: 'New Role' };
      mockRoleService.createRole.mockResolvedValue(newRole);

      const { result } = renderHook(() => useRoleStore());

      // Set initial state
      act(() => {
        result.current.roles = mockRoles;
        result.current.totalItems = 1;
      });

      await act(async () => {
        const createdRole = await result.current.createRole(roleFormData);
        expect(createdRole).toEqual(newRole);
      });

      expect(result.current.roles).toHaveLength(2);
      expect(result.current.roles[0]).toEqual(newRole);
      expect(result.current.totalItems).toBe(2);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(null);
    });

    it('handles create role error', async () => {
      const errorMessage = 'Failed to create role';
      mockRoleService.createRole.mockRejectedValue(new Error(errorMessage));

      const { result } = renderHook(() => useRoleStore());

      await act(async () => {
        try {
          await result.current.createRole(roleFormData);
        } catch (error) {
          expect(error).toBeInstanceOf(Error);
        }
      });

      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
    });
  });

  describe('updateRole', () => {
    const roleFormData: RoleFormData = {
      name: 'Updated Role',
      description: 'Updated description',
      hierarchy: 1,
      isActive: true,
      permissionIds: [],
    };

    it('updates role successfully with optimistic update', async () => {
      const updatedRole = { ...mockRole, name: 'Updated Role' };
      mockRoleService.updateRole.mockResolvedValue(updatedRole);

      const { result } = renderHook(() => useRoleStore());

      // Set initial state
      act(() => {
        result.current.roles = mockRoles;
        result.current.selectedRole = mockRole;
      });

      await act(async () => {
        const result_role = await result.current.updateRole('1', roleFormData);
        expect(result_role).toEqual(updatedRole);
      });

      expect(result.current.roles[0]).toEqual(updatedRole);
      expect(result.current.selectedRole).toEqual(updatedRole);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(null);
    });
  });

  describe('deleteRole', () => {
    it('deletes role successfully with optimistic update', async () => {
      mockRoleService.deleteRole.mockResolvedValue(undefined);

      const { result } = renderHook(() => useRoleStore());

      // Set initial state
      act(() => {
        result.current.roles = mockRoles;
        result.current.selectedRole = mockRole;
        result.current.totalItems = 1;
      });

      await act(async () => {
        await result.current.deleteRole('1');
      });

      expect(result.current.roles).toHaveLength(0);
      expect(result.current.selectedRole).toBe(null);
      expect(result.current.totalItems).toBe(0);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(null);
    });
  });

  describe('fetchRoleHierarchy', () => {
    it('fetches role hierarchy successfully', async () => {
      mockRoleService.getRoleHierarchy.mockResolvedValue(mockHierarchy);

      const { result } = renderHook(() => useRoleStore());

      await act(async () => {
        await result.current.fetchRoleHierarchy();
      });

      expect(result.current.roleHierarchy).toEqual(mockHierarchy);
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(null);
    });
  });

  describe('fetchRoleStatistics', () => {
    it('fetches role statistics successfully', async () => {
      mockRoleService.getRoleStatistics.mockResolvedValue(mockStatistics);

      const { result } = renderHook(() => useRoleStore());

      await act(async () => {
        await result.current.fetchRoleStatistics();
      });

      expect(result.current.roleStatistics).toEqual(mockStatistics);
      expect(result.current.error).toBe(null);
    });
  });

  describe('UI actions', () => {
    it('sets selected role', () => {
      const { result } = renderHook(() => useRoleStore());

      act(() => {
        result.current.setSelectedRole(mockRole);
      });

      expect(result.current.selectedRole).toEqual(mockRole);
    });

    it('sets filters and resets page', () => {
      const { result } = renderHook(() => useRoleStore());

      // Set initial page
      act(() => {
        result.current.currentPage = 3;
      });

      act(() => {
        result.current.setFilters({ search: 'admin' });
      });

      expect(result.current.filters).toEqual({ search: 'admin' });
      expect(result.current.currentPage).toBe(1);
    });

    it('sets page', () => {
      const { result } = renderHook(() => useRoleStore());

      act(() => {
        result.current.setPage(2);
      });

      expect(result.current.currentPage).toBe(2);
    });

    it('sets page size and resets page', () => {
      const { result } = renderHook(() => useRoleStore());

      // Set initial page
      act(() => {
        result.current.currentPage = 3;
      });

      act(() => {
        result.current.setPageSize(25);
      });

      expect(result.current.pageSize).toBe(25);
      expect(result.current.currentPage).toBe(1);
    });

    it('clears error', () => {
      const { result } = renderHook(() => useRoleStore());

      // Set error
      act(() => {
        result.current.error = 'Some error';
      });

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBe(null);
    });

    it('resets store', () => {
      const { result } = renderHook(() => useRoleStore());

      // Set some state
      act(() => {
        result.current.roles = mockRoles;
        result.current.selectedRole = mockRole;
        result.current.error = 'Some error';
      });

      act(() => {
        result.current.reset();
      });

      expect(result.current.roles).toEqual([]);
      expect(result.current.selectedRole).toBe(null);
      expect(result.current.error).toBe(null);
    });
  });
});
