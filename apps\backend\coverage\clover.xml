<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1754265767476" clover="3.2.0">
  <project timestamp="1754265767477" name="All files">
    <metrics statements="499" coveredstatements="139" conditionals="266" coveredconditionals="89" methods="89" coveredmethods="23" elements="854" coveredelements="251" complexity="0" loc="499" ncloc="499" packages="3" files="5" classes="5"/>
    <package name="controllers">
      <metrics statements="164" coveredstatements="94" conditionals="104" coveredconditionals="73" methods="22" coveredmethods="18"/>
      <file name="permissionController.ts" path="C:\project\bmad-method\apps\backend\src\controllers\permissionController.ts">
        <metrics statements="91" coveredstatements="49" conditionals="62" coveredconditionals="46" methods="12" coveredmethods="9"/>
        <line num="2" count="2" type="stmt"/>
        <line num="13" count="2" type="stmt"/>
        <line num="15" count="22" type="stmt"/>
        <line num="19" count="21" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="59" count="2" type="stmt"/>
        <line num="60" count="2" type="stmt"/>
        <line num="62" count="2" type="cond" truecount="3" falsecount="0"/>
        <line num="63" count="2" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="85" count="2" type="stmt"/>
        <line num="86" count="2" type="stmt"/>
        <line num="87" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="90" count="2" type="cond" truecount="2" falsecount="3"/>
        <line num="91" count="2" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="115" count="1" type="stmt"/>
        <line num="116" count="1" type="stmt"/>
        <line num="117" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="119" count="1" type="cond" truecount="2" falsecount="1"/>
        <line num="120" count="1" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="143" count="1" type="stmt"/>
        <line num="144" count="1" type="stmt"/>
        <line num="145" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="147" count="1" type="cond" truecount="2" falsecount="1"/>
        <line num="148" count="1" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="204" count="4" type="stmt"/>
        <line num="205" count="4" type="stmt"/>
        <line num="208" count="4" type="cond" truecount="4" falsecount="0"/>
        <line num="209" count="2" type="stmt"/>
        <line num="215" count="2" type="cond" truecount="4" falsecount="0"/>
        <line num="216" count="2" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="238" count="5" type="stmt"/>
        <line num="239" count="5" type="stmt"/>
        <line num="240" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="243" count="5" type="cond" truecount="4" falsecount="0"/>
        <line num="244" count="2" type="stmt"/>
        <line num="250" count="3" type="cond" truecount="4" falsecount="0"/>
        <line num="251" count="1" type="stmt"/>
        <line num="257" count="2" type="cond" truecount="4" falsecount="0"/>
        <line num="258" count="1" type="stmt"/>
        <line num="264" count="1" type="cond" truecount="2" falsecount="2"/>
        <line num="265" count="1" type="stmt"/>
        <line num="271" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="294" count="2" type="stmt"/>
        <line num="295" count="2" type="stmt"/>
        <line num="297" count="2" type="cond" truecount="3" falsecount="0"/>
        <line num="298" count="2" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="306" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="321" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="322" count="0" type="stmt"/>
        <line num="329" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="330" count="0" type="stmt"/>
        <line num="337" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="338" count="0" type="stmt"/>
        <line num="345" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="346" count="0" type="stmt"/>
        <line num="353" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="354" count="0" type="stmt"/>
        <line num="362" count="0" type="stmt"/>
      </file>
      <file name="roleController.ts" path="C:\project\bmad-method\apps\backend\src\controllers\roleController.ts">
        <metrics statements="73" coveredstatements="45" conditionals="42" coveredconditionals="27" methods="10" coveredmethods="9"/>
        <line num="2" count="2" type="stmt"/>
        <line num="13" count="2" type="stmt"/>
        <line num="15" count="13" type="stmt"/>
        <line num="19" count="12" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="47" count="1" type="stmt"/>
        <line num="56" count="2" type="stmt"/>
        <line num="57" count="2" type="stmt"/>
        <line num="59" count="2" type="cond" truecount="3" falsecount="0"/>
        <line num="60" count="2" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="84" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="87" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="88" count="1" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="111" count="1" type="stmt"/>
        <line num="112" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="114" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="116" count="1" type="cond" truecount="2" falsecount="1"/>
        <line num="117" count="1" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="140" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="142" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="144" count="1" type="cond" truecount="2" falsecount="1"/>
        <line num="145" count="1" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="184" count="1" type="stmt"/>
        <line num="185" count="1" type="stmt"/>
        <line num="187" count="1" type="cond" truecount="2" falsecount="1"/>
        <line num="188" count="1" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="210" count="3" type="stmt"/>
        <line num="211" count="3" type="stmt"/>
        <line num="214" count="3" type="cond" truecount="4" falsecount="0"/>
        <line num="215" count="2" type="stmt"/>
        <line num="221" count="1" type="cond" truecount="2" falsecount="2"/>
        <line num="222" count="1" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="243" count="1" type="stmt"/>
        <line num="245" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="246" count="0" type="stmt"/>
        <line num="253" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="254" count="0" type="stmt"/>
        <line num="261" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="262" count="0" type="stmt"/>
        <line num="269" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="270" count="0" type="stmt"/>
        <line num="277" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="278" count="0" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
      </file>
    </package>
    <package name="services">
      <metrics statements="311" coveredstatements="34" conditionals="153" coveredconditionals="11" methods="62" coveredmethods="3"/>
      <file name="permissionService.ts" path="C:\project\bmad-method\apps\backend\src\services\permissionService.ts">
        <metrics statements="159" coveredstatements="21" conditionals="88" coveredconditionals="6" methods="24" coveredmethods="2"/>
        <line num="1" count="3" type="stmt"/>
        <line num="23" count="3" type="stmt"/>
        <line num="26" count="38" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="48" count="1" type="stmt"/>
        <line num="50" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="51" count="0" type="stmt"/>
        <line num="61" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="62" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="63" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="64" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="65" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="68" count="1" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="100" count="1" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="156" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="167" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="168" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
        <line num="249" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="250" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="255" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="258" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="259" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="260" count="0" type="stmt"/>
        <line num="264" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="265" count="0" type="stmt"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="270" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="354" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="355" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="360" count="0" type="stmt"/>
        <line num="366" count="0" type="stmt"/>
        <line num="391" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="405" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="406" count="0" type="stmt"/>
        <line num="410" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="411" count="0" type="stmt"/>
        <line num="415" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="416" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="439" count="0" type="stmt"/>
        <line num="468" count="0" type="stmt"/>
        <line num="470" count="0" type="stmt"/>
        <line num="472" count="0" type="stmt"/>
        <line num="473" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="474" count="0" type="stmt"/>
        <line num="478" count="0" type="stmt"/>
        <line num="482" count="0" type="stmt"/>
        <line num="483" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="484" count="0" type="stmt"/>
        <line num="488" count="0" type="stmt"/>
        <line num="492" count="0" type="stmt"/>
        <line num="493" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="494" count="0" type="stmt"/>
        <line num="498" count="0" type="stmt"/>
        <line num="502" count="0" type="stmt"/>
        <line num="509" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="521" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="522" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="524" count="0" type="stmt"/>
        <line num="528" count="0" type="stmt"/>
        <line num="532" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="533" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
        <line num="535" count="0" type="stmt"/>
        <line num="538" count="0" type="stmt"/>
        <line num="540" count="0" type="stmt"/>
        <line num="541" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="543" count="0" type="stmt"/>
        <line num="550" count="0" type="stmt"/>
        <line num="556" count="0" type="stmt"/>
        <line num="557" count="0" type="stmt"/>
        <line num="574" count="16" type="stmt"/>
        <line num="575" count="16" type="cond" truecount="1" falsecount="0"/>
        <line num="576" count="2" type="stmt"/>
        <line num="581" count="14" type="stmt"/>
        <line num="583" count="14" type="stmt"/>
        <line num="603" count="0" type="stmt"/>
        <line num="605" count="0" type="stmt"/>
        <line num="606" count="0" type="stmt"/>
        <line num="607" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="609" count="0" type="stmt"/>
        <line num="610" count="0" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
        <line num="617" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="618" count="0" type="stmt"/>
        <line num="648" count="0" type="stmt"/>
        <line num="651" count="0" type="stmt"/>
        <line num="674" count="0" type="stmt"/>
        <line num="684" count="0" type="stmt"/>
        <line num="685" count="0" type="stmt"/>
        <line num="688" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="689" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="690" count="0" type="stmt"/>
        <line num="695" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="696" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="697" count="0" type="stmt"/>
        <line num="702" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="703" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="704" count="0" type="stmt"/>
        <line num="709" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="710" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="711" count="0" type="stmt"/>
        <line num="716" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="717" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="718" count="0" type="stmt"/>
        <line num="723" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="724" count="0" type="stmt"/>
        <line num="725" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="726" count="0" type="stmt"/>
        <line num="730" count="0" type="stmt"/>
        <line num="744" count="0" type="stmt"/>
        <line num="747" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="748" count="0" type="stmt"/>
        <line num="758" count="0" type="stmt"/>
        <line num="760" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="761" count="0" type="stmt"/>
        <line num="770" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="771" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="772" count="0" type="stmt"/>
        <line num="783" count="0" type="stmt"/>
      </file>
      <file name="roleService.ts" path="C:\project\bmad-method\apps\backend\src\services\roleService.ts">
        <metrics statements="152" coveredstatements="13" conditionals="65" coveredconditionals="5" methods="38" coveredmethods="1"/>
        <line num="1" count="3" type="stmt"/>
        <line num="18" count="3" type="stmt"/>
        <line num="21" count="20" type="stmt"/>
        <line num="35" count="1" type="stmt"/>
        <line num="37" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="42" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="43" count="0" type="stmt"/>
        <line num="49" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="54" count="0" type="stmt"/>
        <line num="58" count="1" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="166" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="175" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="180" count="0" type="stmt"/>
        <line num="184" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="185" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="265" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="266" count="0" type="stmt"/>
        <line num="270" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="271" count="0" type="stmt"/>
        <line num="275" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="276" count="0" type="stmt"/>
        <line num="281" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="282" count="0" type="stmt"/>
        <line num="286" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="287" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="305" count="0" type="stmt"/>
        <line num="310" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="338" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="345" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="369" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="376" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="377" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="388" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="431" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="447" count="0" type="stmt"/>
        <line num="451" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="456" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="457" count="0" type="stmt"/>
        <line num="460" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="461" count="0" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="468" count="0" type="stmt"/>
        <line num="475" count="0" type="stmt"/>
        <line num="486" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="487" count="0" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="493" count="0" type="stmt"/>
        <line num="508" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="516" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="519" count="0" type="stmt"/>
        <line num="520" count="0" type="stmt"/>
        <line num="521" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="522" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="530" count="0" type="stmt"/>
        <line num="531" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="532" count="0" type="stmt"/>
        <line num="536" count="0" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="553" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="554" count="0" type="stmt"/>
        <line num="555" count="0" type="stmt"/>
        <line num="556" count="0" type="stmt"/>
        <line num="560" count="0" type="stmt"/>
        <line num="564" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="565" count="0" type="stmt"/>
        <line num="566" count="0" type="stmt"/>
        <line num="567" count="0" type="stmt"/>
        <line num="570" count="0" type="stmt"/>
        <line num="572" count="0" type="stmt"/>
        <line num="573" count="0" type="stmt"/>
        <line num="574" count="0" type="stmt"/>
        <line num="575" count="0" type="stmt"/>
        <line num="586" count="0" type="stmt"/>
        <line num="592" count="0" type="stmt"/>
        <line num="593" count="0" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
        <line num="614" count="0" type="stmt"/>
        <line num="617" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="618" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="619" count="0" type="stmt"/>
        <line num="621" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="622" count="0" type="stmt"/>
        <line num="624" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="625" count="0" type="stmt"/>
        <line num="630" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="631" count="0" type="stmt"/>
        <line num="635" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="636" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="637" count="0" type="stmt"/>
        <line num="641" count="0" type="stmt"/>
        <line num="653" count="0" type="stmt"/>
        <line num="661" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="662" count="0" type="stmt"/>
        <line num="672" count="0" type="stmt"/>
        <line num="676" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="677" count="0" type="stmt"/>
        <line num="678" count="0" type="stmt"/>
        <line num="679" count="0" type="stmt"/>
      </file>
    </package>
    <package name="utils">
      <metrics statements="24" coveredstatements="11" conditionals="9" coveredconditionals="5" methods="5" coveredmethods="2"/>
      <file name="database.ts" path="C:\project\bmad-method\apps\backend\src\utils\database.ts">
        <metrics statements="24" coveredstatements="11" conditionals="9" coveredconditionals="5" methods="5" coveredmethods="2"/>
        <line num="1" count="7" type="stmt"/>
        <line num="4" count="7" type="stmt"/>
        <line num="9" count="7" type="cond" truecount="2" falsecount="0"/>
        <line num="20" count="7" type="cond" truecount="1" falsecount="0"/>
        <line num="21" count="7" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="60" count="4" type="stmt"/>
        <line num="61" count="4" type="stmt"/>
        <line num="67" count="12" type="stmt"/>
        <line num="69" count="4" type="cond" truecount="1" falsecount="0"/>
        <line num="70" count="3" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="7" type="cond" truecount="0" falsecount="1"/>
        <line num="85" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
