<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1754264344224" clover="3.2.0">
  <project timestamp="1754264344224" name="All files">
    <metrics statements="1125" coveredstatements="164" conditionals="700" coveredconditionals="142" methods="389" coveredmethods="67" elements="2214" coveredelements="373" complexity="0" loc="1125" ncloc="1125" packages="11" files="21" classes="21"/>
    <package name="app">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="page.tsx" path="C:\project\bmad-method\apps\frontend\src\app\page.tsx">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.permissions">
      <metrics statements="87" coveredstatements="0" conditionals="50" coveredconditionals="0" methods="21" coveredmethods="0"/>
      <file name="page.tsx" path="C:\project\bmad-method\apps\frontend\src\app\permissions\page.tsx">
        <metrics statements="87" coveredstatements="0" conditionals="50" coveredconditionals="0" methods="21" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="52" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="66" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="321" count="0" type="stmt"/>
        <line num="334" count="0" type="stmt"/>
      </file>
    </package>
    <package name="app.roles">
      <metrics statements="67" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="17" coveredmethods="0"/>
      <file name="page.tsx" path="C:\project\bmad-method\apps\frontend\src\app\roles\page.tsx">
        <metrics statements="67" coveredstatements="0" conditionals="33" coveredconditionals="0" methods="17" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="50" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.common">
      <metrics statements="35" coveredstatements="0" conditionals="41" coveredconditionals="0" methods="9" coveredmethods="0"/>
      <file name="Button.tsx" path="C:\project\bmad-method\apps\frontend\src\components\common\Button.tsx">
        <metrics statements="9" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="45" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
      </file>
      <file name="LoadingSpinner.tsx" path="C:\project\bmad-method\apps\frontend\src\components\common\LoadingSpinner.tsx">
        <metrics statements="5" coveredstatements="0" conditionals="3" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
      </file>
      <file name="Modal.tsx" path="C:\project\bmad-method\apps\frontend\src\components\common\Modal.tsx">
        <metrics statements="21" coveredstatements="0" conditionals="22" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="30" count="0" type="stmt"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="32" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="43" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="65" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.layout">
      <metrics statements="19" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="7" coveredmethods="0"/>
      <file name="Navigation.tsx" path="C:\project\bmad-method\apps\frontend\src\components\layout\Navigation.tsx">
        <metrics statements="19" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="45" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.permissions">
      <metrics statements="228" coveredstatements="59" conditionals="209" coveredconditionals="42" methods="89" coveredmethods="28"/>
      <file name="PermissionForm.tsx" path="C:\project\bmad-method\apps\frontend\src\components\permissions\PermissionForm.tsx">
        <metrics statements="94" coveredstatements="0" conditionals="116" coveredconditionals="0" methods="32" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="cond" truecount="0" falsecount="6"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="7"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="80" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="86" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="103" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="135" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="181" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="338" count="0" type="stmt"/>
        <line num="358" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="359" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="385" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="450" count="0" type="stmt"/>
      </file>
      <file name="PermissionList.tsx" path="C:\project\bmad-method\apps\frontend\src\components\permissions\PermissionList.tsx">
        <metrics statements="76" coveredstatements="59" conditionals="56" coveredconditionals="42" methods="39" coveredmethods="28"/>
        <line num="3" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="21" count="25" type="stmt"/>
        <line num="22" count="25" type="stmt"/>
        <line num="23" count="25" type="stmt"/>
        <line num="24" count="25" type="stmt"/>
        <line num="25" count="25" type="stmt"/>
        <line num="26" count="25" type="stmt"/>
        <line num="29" count="25" type="stmt"/>
        <line num="30" count="57" type="stmt"/>
        <line num="33" count="25" type="stmt"/>
        <line num="34" count="57" type="stmt"/>
        <line num="37" count="25" type="stmt"/>
        <line num="38" count="57" type="stmt"/>
        <line num="42" count="25" type="stmt"/>
        <line num="43" count="22" type="cond" truecount="1" falsecount="1"/>
        <line num="45" count="22" type="stmt"/>
        <line num="46" count="63" type="stmt"/>
        <line num="53" count="22" type="stmt"/>
        <line num="54" count="80" type="stmt"/>
        <line num="55" count="80" type="stmt"/>
        <line num="57" count="80" type="cond" truecount="1" falsecount="1"/>
        <line num="59" count="80" type="cond" truecount="2" falsecount="0"/>
        <line num="60" count="80" type="cond" truecount="2" falsecount="0"/>
        <line num="64" count="25" type="stmt"/>
        <line num="65" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="66" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="25" type="stmt"/>
        <line num="74" count="2" type="stmt"/>
        <line num="75" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="2" type="stmt"/>
        <line num="80" count="2" type="stmt"/>
        <line num="83" count="25" type="stmt"/>
        <line num="84" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="3" type="stmt"/>
        <line num="91" count="25" type="stmt"/>
        <line num="92" count="144" type="cond" truecount="2" falsecount="0"/>
        <line num="93" count="120" type="stmt"/>
        <line num="95" count="24" type="cond" truecount="2" falsecount="0"/>
        <line num="98" count="25" type="stmt"/>
        <line num="99" count="68" type="cond" truecount="1" falsecount="1"/>
        <line num="100" count="68" type="cond" truecount="1" falsecount="1"/>
        <line num="101" count="68" type="stmt"/>
        <line num="104" count="25" type="stmt"/>
        <line num="105" count="68" type="stmt"/>
        <line num="115" count="68" type="cond" truecount="2" falsecount="0"/>
        <line num="118" count="25" type="cond" truecount="2" falsecount="0"/>
        <line num="127" count="5" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="174" count="1" type="stmt"/>
        <line num="179" count="46" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="194" count="46" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="209" count="46" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="307" count="68" type="stmt"/>
        <line num="317" count="2" type="stmt"/>
        <line num="372" count="1" type="stmt"/>
        <line num="378" count="1" type="stmt"/>
        <line num="384" count="1" type="stmt"/>
        <line num="423" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
        <line num="447" count="20" type="stmt"/>
      </file>
      <file name="PermissionMatrix.tsx" path="C:\project\bmad-method\apps\frontend\src\components\permissions\PermissionMatrix.tsx">
        <metrics statements="58" coveredstatements="0" conditionals="37" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="34" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="104" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="111" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="222" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="239" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="241" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
      </file>
    </package>
    <package name="components.roles">
      <metrics statements="172" coveredstatements="90" conditionals="170" coveredconditionals="97" methods="65" coveredmethods="38"/>
      <file name="RoleForm.tsx" path="C:\project\bmad-method\apps\frontend\src\components\roles\RoleForm.tsx">
        <metrics statements="83" coveredstatements="50" conditionals="91" coveredconditionals="64" methods="30" coveredmethods="21"/>
        <line num="3" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="22" count="89" type="stmt"/>
        <line num="30" count="89" type="stmt"/>
        <line num="31" count="89" type="stmt"/>
        <line num="32" count="89" type="stmt"/>
        <line num="35" count="89" type="stmt"/>
        <line num="36" count="16" type="cond" truecount="2" falsecount="0"/>
        <line num="37" count="3" type="stmt"/>
        <line num="42" count="6" type="cond" truecount="1" falsecount="1"/>
        <line num="48" count="89" type="stmt"/>
        <line num="49" count="6" type="cond" truecount="3" falsecount="1"/>
        <line num="51" count="4" type="cond" truecount="3" falsecount="1"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="58" count="0" type="stmt"/>
        <line num="60" count="4" type="stmt"/>
        <line num="63" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="1" type="stmt"/>
        <line num="69" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="73" count="1" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="89" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="89" type="stmt"/>
        <line num="101" count="61" type="stmt"/>
        <line num="107" count="61" type="cond" truecount="1" falsecount="1"/>
        <line num="108" count="0" type="stmt"/>
        <line num="115" count="61" type="stmt"/>
        <line num="121" count="89" type="stmt"/>
        <line num="122" count="6" type="stmt"/>
        <line num="127" count="6" type="stmt"/>
        <line num="128" count="6" type="stmt"/>
        <line num="134" count="89" type="stmt"/>
        <line num="135" count="3" type="stmt"/>
        <line num="136" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="137" count="1" type="stmt"/>
        <line num="139" count="2" type="stmt"/>
        <line num="142" count="3" type="stmt"/>
        <line num="148" count="89" type="stmt"/>
        <line num="149" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="150" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="162" count="89" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="165" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="191" count="89" type="stmt"/>
        <line num="192" count="536" type="cond" truecount="4" falsecount="0"/>
        <line num="195" count="89" type="cond" truecount="2" falsecount="0"/>
        <line num="217" count="41" type="stmt"/>
        <line num="218" count="4" type="stmt"/>
        <line num="243" count="4" type="cond" truecount="1" falsecount="1"/>
        <line num="244" count="1" type="stmt"/>
        <line num="270" count="16" type="stmt"/>
        <line num="271" count="1" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="328" count="178" type="stmt"/>
        <line num="335" count="3" type="stmt"/>
        <line num="393" count="16" type="stmt"/>
      </file>
      <file name="RoleHierarchy.tsx" path="C:\project\bmad-method\apps\frontend\src\components\roles\RoleHierarchy.tsx">
        <metrics statements="39" coveredstatements="0" conditionals="35" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="25" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="186" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="248" count="0" type="stmt"/>
      </file>
      <file name="RoleList.tsx" path="C:\project\bmad-method\apps\frontend\src\components\roles\RoleList.tsx">
        <metrics statements="50" coveredstatements="40" conditionals="44" coveredconditionals="33" methods="21" coveredmethods="17"/>
        <line num="3" count="2" type="stmt"/>
        <line num="10" count="2" type="stmt"/>
        <line num="21" count="25" type="stmt"/>
        <line num="22" count="25" type="stmt"/>
        <line num="23" count="25" type="stmt"/>
        <line num="26" count="25" type="stmt"/>
        <line num="27" count="22" type="cond" truecount="1" falsecount="1"/>
        <line num="29" count="22" type="stmt"/>
        <line num="30" count="64" type="stmt"/>
        <line num="31" count="64" type="stmt"/>
        <line num="33" count="64" type="cond" truecount="1" falsecount="1"/>
        <line num="35" count="64" type="cond" truecount="2" falsecount="0"/>
        <line num="36" count="64" type="cond" truecount="2" falsecount="0"/>
        <line num="40" count="25" type="stmt"/>
        <line num="41" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="42" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="49" count="25" type="stmt"/>
        <line num="50" count="2" type="stmt"/>
        <line num="51" count="2" type="cond" truecount="1" falsecount="1"/>
        <line num="52" count="0" type="stmt"/>
        <line num="54" count="2" type="stmt"/>
        <line num="56" count="2" type="stmt"/>
        <line num="59" count="25" type="stmt"/>
        <line num="60" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="3" type="stmt"/>
        <line num="67" count="25" type="stmt"/>
        <line num="68" count="69" type="cond" truecount="2" falsecount="0"/>
        <line num="69" count="46" type="stmt"/>
        <line num="71" count="23" type="cond" truecount="2" falsecount="0"/>
        <line num="74" count="25" type="stmt"/>
        <line num="75" count="57" type="cond" truecount="1" falsecount="1"/>
        <line num="76" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="25" type="cond" truecount="2" falsecount="0"/>
        <line num="89" count="10" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="182" count="57" type="stmt"/>
        <line num="192" count="2" type="stmt"/>
        <line num="239" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
        <line num="251" count="1" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="295" count="1" type="stmt"/>
        <line num="309" count="21" type="stmt"/>
      </file>
    </package>
    <package name="hooks">
      <metrics statements="177" coveredstatements="0" conditionals="17" coveredconditionals="0" methods="51" coveredmethods="0"/>
      <file name="usePermissions.ts" path="C:\project\bmad-method\apps\frontend\src\hooks\usePermissions.ts">
        <metrics statements="95" coveredstatements="0" conditionals="8" coveredconditionals="0" methods="28" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="59" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="200" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="213" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="297" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="320" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
      </file>
      <file name="useRoles.ts" path="C:\project\bmad-method\apps\frontend\src\hooks\useRoles.ts">
        <metrics statements="82" coveredstatements="0" conditionals="9" coveredconditionals="0" methods="23" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="8" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="53" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="119" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="132" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="270" count="0" type="stmt"/>
      </file>
    </package>
    <package name="services">
      <metrics statements="164" coveredstatements="15" conditionals="95" coveredconditionals="3" methods="68" coveredmethods="1"/>
      <file name="api.ts" path="C:\project\bmad-method\apps\frontend\src\services\api.ts">
        <metrics statements="59" coveredstatements="11" conditionals="44" coveredconditionals="3" methods="18" coveredmethods="1"/>
        <line num="2" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="4" count="1" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="70" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="71" count="0" type="stmt"/>
        <line num="79" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="80" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="115" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="156" count="1" type="stmt"/>
        <line num="159" count="1" type="stmt"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="176" count="1" type="stmt"/>
        <line num="179" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="182" count="1" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="193" count="1" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="198" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="205" count="1" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="217" count="1" type="stmt"/>
      </file>
      <file name="permissionService.ts" path="C:\project\bmad-method\apps\frontend\src\services\permissionService.ts">
        <metrics statements="64" coveredstatements="0" conditionals="29" coveredconditionals="0" methods="28" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="181" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="182" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="188" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="232" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="233" count="0" type="stmt"/>
        <line num="238" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="241" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="242" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="287" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="297" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="298" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="299" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="304" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="327" count="0" type="stmt"/>
      </file>
      <file name="roleService.ts" path="C:\project\bmad-method\apps\frontend\src\services\roleService.ts">
        <metrics statements="41" coveredstatements="4" conditionals="22" coveredconditionals="0" methods="22" coveredmethods="0"/>
        <line num="2" count="1" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="148" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="149" count="0" type="stmt"/>
        <line num="153" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="154" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="178" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="221" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="242" count="1" type="stmt"/>
        <line num="245" count="1" type="stmt"/>
      </file>
    </package>
    <package name="stores">
      <metrics statements="170" coveredstatements="0" conditionals="76" coveredconditionals="0" methods="61" coveredmethods="0"/>
      <file name="permissionStore.ts" path="C:\project\bmad-method\apps\frontend\src\stores\permissionStore.ts">
        <metrics statements="87" coveredstatements="0" conditionals="34" coveredconditionals="0" methods="32" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="226" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="235" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="256" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="258" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="313" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="349" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
      </file>
      <file name="roleStore.ts" path="C:\project\bmad-method\apps\frontend\src\stores\roleStore.ts">
        <metrics statements="83" coveredstatements="0" conditionals="42" coveredconditionals="0" methods="29" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="125" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="145" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="174" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="190" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="225" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="238" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="245" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="251" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="275" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="295" count="0" type="stmt"/>
        <line num="299" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="307" count="0" type="stmt"/>
      </file>
    </package>
    <package name="types">
      <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="permission.ts" path="C:\project\bmad-method\apps\frontend\src\types\permission.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="222" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="255" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
