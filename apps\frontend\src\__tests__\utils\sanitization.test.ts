import {
  escapeHtml,
  sanitizeInput,
  sanitizeRoleName,
  sanitizePermissionName,
  sanitizeDescription,
  sanitizeSearchQuery,
  sanitizeEmail,
  sanitizeNumber,
  sanitizeBoolean,
  sanitizeUUID,
  sanitizeApiInput
} from '../../utils/sanitization';

describe('Sanitization Utils', () => {
  describe('escapeHtml', () => {
    it('should escape HTML entities', () => {
      expect(escapeHtml('<script>alert("xss")</script>')).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;');
      expect(escapeHtml('Hello & World')).toBe('Hello &amp; World');
      expect(escapeHtml("It's a test")).toBe('It&#x27;s a test');
    });

    it('should handle non-string input', () => {
      expect(escapeHtml(123 as any)).toBe('123');
      expect(escapeHtml(null as any)).toBe('null');
    });
  });

  describe('sanitizeInput', () => {
    it('should sanitize dangerous input', () => {
      expect(sanitizeInput('<script>alert("xss")</script>')).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;');
      expect(sanitizeInput('javascript:alert("xss")')).toBe('alert(&quot;xss&quot;)');
      expect(sanitizeInput('onclick=alert("xss")')).toBe('alert(&quot;xss&quot;)');
    });

    it('should remove null bytes', () => {
      expect(sanitizeInput('test\0null')).toBe('testnull');
    });

    it('should handle non-string input', () => {
      expect(sanitizeInput(123 as any)).toBe('');
      expect(sanitizeInput(null as any)).toBe('');
    });
  });

  describe('sanitizeRoleName', () => {
    it('should allow valid role names', () => {
      expect(sanitizeRoleName('Admin Role')).toBe('Admin Role');
      expect(sanitizeRoleName('user_role-1')).toBe('user_role-1');
    });

    it('should remove invalid characters', () => {
      expect(sanitizeRoleName('Admin<script>Role')).toBe('AdminRole');
      expect(sanitizeRoleName('Role@#$%')).toBe('Role');
    });

    it('should limit length', () => {
      const longName = 'a'.repeat(150);
      expect(sanitizeRoleName(longName)).toHaveLength(100);
    });

    it('should normalize spaces', () => {
      expect(sanitizeRoleName('  Admin   Role  ')).toBe('Admin Role');
    });
  });

  describe('sanitizePermissionName', () => {
    it('should allow valid permission names', () => {
      expect(sanitizePermissionName('user.profile.read.own')).toBe('user.profile.read.own');
      expect(sanitizePermissionName('admin-panel_access')).toBe('admin-panel_access');
    });

    it('should remove invalid characters', () => {
      expect(sanitizePermissionName('user.profile@read')).toBe('user.profileread');
      expect(sanitizePermissionName('admin<script>access')).toBe('adminaccess');
    });

    it('should limit length', () => {
      const longName = 'a'.repeat(250);
      expect(sanitizePermissionName(longName)).toHaveLength(200);
    });
  });

  describe('sanitizeDescription', () => {
    it('should escape HTML in descriptions', () => {
      expect(sanitizeDescription('A <b>bold</b> description')).toBe('A &lt;b&gt;bold&lt;&#x2F;b&gt; description');
    });

    it('should normalize whitespace', () => {
      expect(sanitizeDescription('  Multiple   spaces  ')).toBe('Multiple spaces');
    });

    it('should limit length', () => {
      const longDesc = 'a'.repeat(600);
      expect(sanitizeDescription(longDesc)).toHaveLength(500);
    });
  });

  describe('sanitizeSearchQuery', () => {
    it('should remove dangerous characters', () => {
      expect(sanitizeSearchQuery('search<script>')).toBe('searchscript');
      expect(sanitizeSearchQuery('SELECT * FROM users')).toBe(' * FROM users');
    });

    it('should remove SQL injection patterns', () => {
      expect(sanitizeSearchQuery('DROP TABLE users')).toBe(' users');
      expect(sanitizeSearchQuery('UNION SELECT password')).toBe(' password');
    });

    it('should limit length', () => {
      const longQuery = 'a'.repeat(150);
      expect(sanitizeSearchQuery(longQuery)).toHaveLength(100);
    });
  });

  describe('sanitizeEmail', () => {
    it('should validate and sanitize valid emails', () => {
      expect(sanitizeEmail('<EMAIL>')).toBe('<EMAIL>');
      expect(sanitizeEmail('  <EMAIL>  ')).toBe('<EMAIL>');
    });

    it('should reject invalid emails', () => {
      expect(sanitizeEmail('invalid-email')).toBe('');
      expect(sanitizeEmail('user@')).toBe('');
      expect(sanitizeEmail('@example.com')).toBe('');
    });
  });

  describe('sanitizeNumber', () => {
    it('should handle valid numbers', () => {
      expect(sanitizeNumber(123)).toBe(123);
      expect(sanitizeNumber('456')).toBe(456);
      expect(sanitizeNumber('0')).toBe(0);
    });

    it('should reject invalid numbers', () => {
      expect(sanitizeNumber('abc')).toBe(null);
      expect(sanitizeNumber(NaN)).toBe(null);
      expect(sanitizeNumber(null)).toBe(null);
    });
  });

  describe('sanitizeBoolean', () => {
    it('should handle boolean values', () => {
      expect(sanitizeBoolean(true)).toBe(true);
      expect(sanitizeBoolean(false)).toBe(false);
      expect(sanitizeBoolean('true')).toBe(true);
      expect(sanitizeBoolean('false')).toBe(false);
    });

    it('should default to false for invalid values', () => {
      expect(sanitizeBoolean('invalid')).toBe(false);
      expect(sanitizeBoolean(123)).toBe(false);
      expect(sanitizeBoolean(null)).toBe(false);
    });
  });

  describe('sanitizeUUID', () => {
    it('should validate valid UUIDs', () => {
      const validUUID = '123e4567-e89b-12d3-a456-************';
      expect(sanitizeUUID(validUUID)).toBe(validUUID);
      expect(sanitizeUUID(validUUID.toUpperCase())).toBe(validUUID);
    });

    it('should reject invalid UUIDs', () => {
      expect(sanitizeUUID('invalid-uuid')).toBe('');
      expect(sanitizeUUID('123-456-789')).toBe('');
      expect(sanitizeUUID('')).toBe('');
    });
  });

  describe('sanitizeApiInput', () => {
    it('should sanitize object properties based on key names', () => {
      const input = {
        name: 'Admin<script>Role',
        description: 'A <b>description</b>',
        email: '  <EMAIL>  ',
        hierarchy: '5',
        isActive: 'true',
        id: '123e4567-e89b-12d3-a456-************',
        customField: '<script>alert("xss")</script>'
      };

      const result = sanitizeApiInput(input);

      expect(result.name).toBe('AdminRole');
      expect(result.description).toBe('A &lt;b&gt;description&lt;&#x2F;b&gt;');
      expect(result.email).toBe('<EMAIL>');
      expect(result.hierarchy).toBe(5);
      expect(result.isActive).toBe(true);
      expect(result.id).toBe('123e4567-e89b-12d3-a456-************');
      expect(result.customField).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;');
    });

    it('should handle null and undefined values', () => {
      const input = {
        name: null,
        description: undefined,
        email: '<EMAIL>'
      };

      const result = sanitizeApiInput(input);

      expect(result.name).toBe(null);
      expect(result.description).toBe(undefined);
      expect(result.email).toBe('<EMAIL>');
    });
  });
});
