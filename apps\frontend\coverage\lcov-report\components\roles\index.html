
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for components/roles</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> components/roles</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">52.38% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>99/189</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">57.05% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>97/170</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">58.46% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>38/65</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">52.32% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>90/172</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="RoleForm.tsx"><a href="RoleForm.tsx.html">RoleForm.tsx</a></td>
	<td data-value="60.86" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 60%"></div><div class="cover-empty" style="width: 40%"></div></div>
	</td>
	<td data-value="60.86" class="pct medium">60.86%</td>
	<td data-value="92" class="abs medium">56/92</td>
	<td data-value="70.32" class="pct high">70.32%</td>
	<td data-value="91" class="abs high">64/91</td>
	<td data-value="70" class="pct high">70%</td>
	<td data-value="30" class="abs high">21/30</td>
	<td data-value="60.24" class="pct medium">60.24%</td>
	<td data-value="83" class="abs medium">50/83</td>
	</tr>

<tr>
	<td class="file low" data-value="RoleHierarchy.tsx"><a href="RoleHierarchy.tsx.html">RoleHierarchy.tsx</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="41" class="abs low">0/41</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="35" class="abs low">0/35</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="14" class="abs low">0/14</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="39" class="abs low">0/39</td>
	</tr>

<tr>
	<td class="file high" data-value="RoleList.tsx"><a href="RoleList.tsx.html">RoleList.tsx</a></td>
	<td data-value="76.78" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 76%"></div><div class="cover-empty" style="width: 24%"></div></div>
	</td>
	<td data-value="76.78" class="pct high">76.78%</td>
	<td data-value="56" class="abs high">43/56</td>
	<td data-value="75" class="pct high">75%</td>
	<td data-value="44" class="abs high">33/44</td>
	<td data-value="80.95" class="pct high">80.95%</td>
	<td data-value="21" class="abs high">17/21</td>
	<td data-value="80" class="pct high">80%</td>
	<td data-value="50" class="abs high">40/50</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-03T23:39:04.054Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    