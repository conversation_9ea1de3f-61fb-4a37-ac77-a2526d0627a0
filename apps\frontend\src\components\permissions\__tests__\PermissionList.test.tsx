import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import PermissionList from '../PermissionList';
import type { Permission } from '@/types/permission';

// Mock data
const mockPermissions: Permission[] = [
  {
    id: '1',
    name: 'users.read',
    description: 'Read users',
    module: 'users',
    feature: 'read',
    action: 'read',
    resource: 'user',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'users.write',
    description: 'Write users',
    module: 'users',
    feature: 'write',
    action: 'write',
    resource: 'user',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '3',
    name: 'roles.read',
    description: 'Read roles',
    module: 'roles',
    feature: 'read',
    action: 'read',
    resource: 'role',
    isActive: false,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
  },
];

const defaultProps = {
  permissions: mockPermissions,
  loading: false,
  onEdit: jest.fn(),
  onDelete: jest.fn(),
  onView: jest.fn(),
  onBulkDelete: jest.fn(),
  onFiltersChange: jest.fn(),
};

describe('PermissionList Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders permission list correctly', () => {
    render(<PermissionList {...defaultProps} />);
    
    expect(screen.getByText('users.read')).toBeInTheDocument();
    expect(screen.getByText('users.write')).toBeInTheDocument();
    expect(screen.getByText('roles.read')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(<PermissionList {...defaultProps} loading={true} />);
    
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('shows empty state when no permissions', () => {
    render(<PermissionList {...defaultProps} permissions={[]} />);
    
    expect(screen.getByText(/tidak ada permission/i)).toBeInTheDocument();
  });

  it('displays permission details correctly', () => {
    render(<PermissionList {...defaultProps} />);
    
    // Check permission names
    expect(screen.getByText('users.read')).toBeInTheDocument();
    expect(screen.getByText('users.write')).toBeInTheDocument();
    
    // Check descriptions
    expect(screen.getByText('Read users')).toBeInTheDocument();
    expect(screen.getByText('Write users')).toBeInTheDocument();
    
    // Check modules
    expect(screen.getAllByText('users')).toHaveLength(2);
    expect(screen.getByText('roles')).toBeInTheDocument();
  });

  it('displays permission status correctly', () => {
    render(<PermissionList {...defaultProps} />);
    
    // Check active status
    const activeStatuses = screen.getAllByText(/aktif/i);
    expect(activeStatuses).toHaveLength(2); // users.read and users.write are active
    
    // Check inactive status
    expect(screen.getByText(/tidak aktif/i)).toBeInTheDocument();
  });

  it('calls onEdit when edit button is clicked', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    const editButtons = screen.getAllByText(/edit/i);
    await user.click(editButtons[0]);
    
    expect(defaultProps.onEdit).toHaveBeenCalledWith(mockPermissions[0]);
  });

  it('calls onDelete when delete button is clicked', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    const deleteButtons = screen.getAllByText(/hapus/i);
    await user.click(deleteButtons[0]);
    
    expect(defaultProps.onDelete).toHaveBeenCalledWith(mockPermissions[0].id);
  });

  it('calls onView when view button is clicked', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    const viewButtons = screen.getAllByText(/lihat/i);
    await user.click(viewButtons[0]);
    
    expect(defaultProps.onView).toHaveBeenCalledWith(mockPermissions[0]);
  });

  it('handles bulk selection', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    // Select first permission
    const checkboxes = screen.getAllByRole('checkbox');
    await user.click(checkboxes[1]); // Skip header checkbox
    
    // Check if bulk actions appear
    expect(screen.getByText(/terpilih/i)).toBeInTheDocument();
  });

  it('handles select all functionality', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    // Click select all checkbox
    const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
    await user.click(selectAllCheckbox);
    
    // Check if all permissions are selected
    expect(screen.getByText(/3.*terpilih/i)).toBeInTheDocument();
  });

  it('calls onBulkDelete when bulk delete is clicked', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    // Select first permission
    const checkboxes = screen.getAllByRole('checkbox');
    await user.click(checkboxes[1]);
    
    // Click bulk delete
    const bulkDeleteButton = screen.getByText(/hapus terpilih/i);
    await user.click(bulkDeleteButton);
    
    expect(defaultProps.onBulkDelete).toHaveBeenCalledWith([mockPermissions[0].id]);
  });

  it('handles search filter', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    // Type in search input
    const searchInput = screen.getByPlaceholderText(/cari permission/i);
    await user.type(searchInput, 'users');
    
    await waitFor(() => {
      expect(defaultProps.onFiltersChange).toHaveBeenCalledWith(
        expect.objectContaining({ search: 'users' })
      );
    });
  });

  it('handles module filter', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    // Change module filter
    const moduleSelect = screen.getByDisplayValue(/semua modul/i);
    await user.selectOptions(moduleSelect, 'users');
    
    expect(defaultProps.onFiltersChange).toHaveBeenCalledWith(
      expect.objectContaining({ module: 'users' })
    );
  });

  it('handles feature filter', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    // Change feature filter
    const featureSelect = screen.getByDisplayValue(/semua fitur/i);
    await user.selectOptions(featureSelect, 'read');
    
    expect(defaultProps.onFiltersChange).toHaveBeenCalledWith(
      expect.objectContaining({ feature: 'read' })
    );
  });

  it('handles action filter', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    // Change action filter
    const actionSelect = screen.getByDisplayValue(/semua aksi/i);
    await user.selectOptions(actionSelect, 'read');
    
    expect(defaultProps.onFiltersChange).toHaveBeenCalledWith(
      expect.objectContaining({ action: 'read' })
    );
  });

  it('handles status filter', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    // Change status filter
    const statusSelect = screen.getByDisplayValue(/semua status/i);
    await user.selectOptions(statusSelect, 'active');
    
    expect(defaultProps.onFiltersChange).toHaveBeenCalledWith(
      expect.objectContaining({ isActive: true })
    );
  });

  it('handles sorting', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    // Click on name column header to sort
    const nameHeader = screen.getByText(/nama permission/i);
    await user.click(nameHeader);
    
    expect(defaultProps.onFiltersChange).toHaveBeenCalledWith(
      expect.objectContaining({ 
        sortBy: 'name',
        sortOrder: expect.any(String)
      })
    );
  });

  it('groups permissions by module', () => {
    render(<PermissionList {...defaultProps} />);
    
    // Should show module groupings
    expect(screen.getByText(/modul: users/i)).toBeInTheDocument();
    expect(screen.getByText(/modul: roles/i)).toBeInTheDocument();
  });

  it('shows permission count per module', () => {
    render(<PermissionList {...defaultProps} />);
    
    // Should show count for each module
    expect(screen.getByText(/2 permission/i)).toBeInTheDocument(); // users module
    expect(screen.getByText(/1 permission/i)).toBeInTheDocument(); // roles module
  });

  it('allows expanding and collapsing module groups', async () => {
    const user = userEvent.setup();
    render(<PermissionList {...defaultProps} />);
    
    // Find expand/collapse button for users module
    const expandButton = screen.getByText(/modul: users/i).closest('button');
    
    if (expandButton) {
      await user.click(expandButton);
      
      // Check if permissions are hidden/shown
      // This would depend on the actual implementation
    }
  });
});
