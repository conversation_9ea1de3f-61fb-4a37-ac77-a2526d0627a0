import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import RoleList from '../RoleList';

// Mock data
interface Role {
  id: string;
  name: string;
  description: string;
  hierarchy: number;
  isActive: boolean;
  permissions: any[];
  createdAt: Date;
  updatedAt: Date;
}

const mockRoles: Role[] = [
  {
    id: '1',
    name: 'Admin',
    description: 'Administrator role',
    hierarchy: 1,
    isActive: true,
    permissions: [],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'User',
    description: 'Regular user role',
    hierarchy: 2,
    isActive: true,
    permissions: [],
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
  },
  {
    id: '3',
    name: 'Guest',
    description: 'Guest role',
    hierarchy: 3,
    isActive: false,
    permissions: [],
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03'),
  },
];

const mockPagination = {
  page: 1,
  limit: 10,
  total: 3,
  totalPages: 1,
  hasNext: false,
  hasPrev: false,
};

const defaultProps = {
  roles: mockRoles,
  loading: false,
  onEdit: jest.fn(),
  onDelete: jest.fn(),
  onView: jest.fn(),
  onBulkDelete: jest.fn(),
  pagination: mockPagination,
  onPageChange: jest.fn(),
  onLimitChange: jest.fn(),
  onFiltersChange: jest.fn(),
};

describe('RoleList Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders role list correctly', () => {
    render(<RoleList {...defaultProps} />);
    
    expect(screen.getByText('Admin')).toBeInTheDocument();
    expect(screen.getByText('User')).toBeInTheDocument();
    expect(screen.getByText('Guest')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(<RoleList {...defaultProps} loading={true} />);

    expect(document.querySelector('.animate-pulse')).toBeInTheDocument();
  });

  it('shows empty state when no roles', () => {
    render(<RoleList {...defaultProps} roles={[]} />);

    expect(screen.getByText(/tidak ada role ditemukan/i)).toBeInTheDocument();
  });

  it('calls onEdit when edit button is clicked', async () => {
    const user = userEvent.setup();
    render(<RoleList {...defaultProps} />);
    
    const editButtons = screen.getAllByText(/edit/i);
    await user.click(editButtons[0]);
    
    expect(defaultProps.onEdit).toHaveBeenCalledWith(mockRoles[0]);
  });

  it('calls onDelete when delete button is clicked', async () => {
    const user = userEvent.setup();
    render(<RoleList {...defaultProps} />);
    
    const deleteButtons = screen.getAllByText(/hapus/i);
    await user.click(deleteButtons[0]);
    
    expect(defaultProps.onDelete).toHaveBeenCalledWith(mockRoles[0].id);
  });

  it('calls onView when view button is clicked', async () => {
    const user = userEvent.setup();
    render(<RoleList {...defaultProps} />);
    
    const viewButtons = screen.getAllByText(/lihat/i);
    await user.click(viewButtons[0]);
    
    expect(defaultProps.onView).toHaveBeenCalledWith(mockRoles[0]);
  });

  it('handles bulk selection', async () => {
    const user = userEvent.setup();
    render(<RoleList {...defaultProps} />);
    
    // Select first role
    const checkboxes = screen.getAllByRole('checkbox');
    await user.click(checkboxes[1]); // Skip header checkbox
    
    // Check if bulk actions appear
    expect(screen.getByText(/terpilih/i)).toBeInTheDocument();
  });

  it('handles select all functionality', async () => {
    const user = userEvent.setup();
    render(<RoleList {...defaultProps} />);
    
    // Click select all checkbox
    const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
    await user.click(selectAllCheckbox);
    
    // Check if all roles are selected
    expect(screen.getByText(/3.*terpilih/i)).toBeInTheDocument();
  });

  it('calls onBulkDelete when bulk delete is clicked', async () => {
    const user = userEvent.setup();
    render(<RoleList {...defaultProps} />);
    
    // Select first role
    const checkboxes = screen.getAllByRole('checkbox');
    await user.click(checkboxes[1]);
    
    // Click bulk delete
    const bulkDeleteButton = screen.getByText(/hapus terpilih/i);
    await user.click(bulkDeleteButton);
    
    expect(defaultProps.onBulkDelete).toHaveBeenCalledWith([mockRoles[0].id]);
  });

  it('handles pagination', async () => {
    const user = userEvent.setup();
    const paginationProps = {
      ...defaultProps,
      pagination: {
        ...mockPagination,
        page: 1,
        totalPages: 3,
        hasNext: true,
      },
    };
    
    render(<RoleList {...paginationProps} />);
    
    // Click next page
    const nextButton = screen.getByText(/selanjutnya/i);
    await user.click(nextButton);
    
    expect(defaultProps.onPageChange).toHaveBeenCalledWith(2);
  });

  it('handles page size change', async () => {
    const user = userEvent.setup();
    render(<RoleList {...defaultProps} />);
    
    // Change page size
    const pageSizeSelect = screen.getByDisplayValue('10');
    await user.selectOptions(pageSizeSelect, '25');
    
    expect(defaultProps.onLimitChange).toHaveBeenCalledWith(25);
  });

  it('handles search filter', async () => {
    const user = userEvent.setup();
    render(<RoleList {...defaultProps} />);
    
    // Type in search input
    const searchInput = screen.getByPlaceholderText(/cari role/i);
    await user.type(searchInput, 'admin');
    
    await waitFor(() => {
      expect(defaultProps.onFiltersChange).toHaveBeenCalledWith(
        expect.objectContaining({ search: 'admin' })
      );
    });
  });

  it('handles status filter', async () => {
    const user = userEvent.setup();
    render(<RoleList {...defaultProps} />);
    
    // Change status filter
    const statusSelect = screen.getByDisplayValue(/semua status/i);
    await user.selectOptions(statusSelect, 'active');
    
    expect(defaultProps.onFiltersChange).toHaveBeenCalledWith(
      expect.objectContaining({ isActive: true })
    );
  });

  it('displays role status correctly', () => {
    render(<RoleList {...defaultProps} />);
    
    // Check active status
    const activeStatuses = screen.getAllByText(/aktif/i);
    expect(activeStatuses).toHaveLength(2); // Admin and User are active
    
    // Check inactive status
    expect(screen.getByText(/tidak aktif/i)).toBeInTheDocument();
  });

  it('displays hierarchy levels correctly', () => {
    render(<RoleList {...defaultProps} />);
    
    expect(screen.getByText('Level 1')).toBeInTheDocument();
    expect(screen.getByText('Level 2')).toBeInTheDocument();
    expect(screen.getByText('Level 3')).toBeInTheDocument();
  });

  it('handles sorting', async () => {
    const user = userEvent.setup();
    render(<RoleList {...defaultProps} />);
    
    // Click on name column header to sort
    const nameHeader = screen.getByText(/nama role/i);
    await user.click(nameHeader);
    
    expect(defaultProps.onFiltersChange).toHaveBeenCalledWith(
      expect.objectContaining({ 
        sortBy: 'name',
        sortOrder: expect.any(String)
      })
    );
  });
});
