{"C:\\project\\bmad-method\\apps\\frontend\\src\\app\\page.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\app\\page.tsx", "statementMap": {"0": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": null}}, "1": {"start": {"line": 3, "column": 17}, "end": {"line": 3, "column": null}}}, "fnMap": {"0": {"name": "Home", "decl": {"start": {"line": 5, "column": 24}, "end": {"line": 5, "column": null}}, "loc": {"start": {"line": 5, "column": 24}, "end": {"line": 174, "column": null}}}}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {"0": 0}, "b": {}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\app\\permissions\\page.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\app\\permissions\\page.tsx", "statementMap": {"0": {"start": {"line": 334, "column": 0}, "end": {"line": 334, "column": 15}}, "1": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 31}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 27}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 7, "column": 38}, "end": {"line": 7, "column": null}}, "6": {"start": {"line": 8, "column": 18}, "end": {"line": 8, "column": null}}, "7": {"start": {"line": 9, "column": 19}, "end": {"line": 9, "column": null}}, "8": {"start": {"line": 15, "column": 34}, "end": {"line": 332, "column": null}}, "9": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": null}}, "10": {"start": {"line": 26, "column": 36}, "end": {"line": 26, "column": null}}, "11": {"start": {"line": 27, "column": 34}, "end": {"line": 27, "column": null}}, "12": {"start": {"line": 30, "column": 26}, "end": {"line": 46, "column": null}}, "13": {"start": {"line": 31, "column": 4}, "end": {"line": 45, "column": null}}, "14": {"start": {"line": 32, "column": 6}, "end": {"line": 32, "column": null}}, "15": {"start": {"line": 33, "column": 23}, "end": {"line": 33, "column": null}}, "16": {"start": {"line": 35, "column": 6}, "end": {"line": 40, "column": null}}, "17": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": null}}, "18": {"start": {"line": 37, "column": 8}, "end": {"line": 39, "column": null}}, "19": {"start": {"line": 38, "column": 10}, "end": {"line": 38, "column": null}}, "20": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": null}}, "21": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": null}}, "22": {"start": {"line": 48, "column": 21}, "end": {"line": 57, "column": null}}, "23": {"start": {"line": 49, "column": 4}, "end": {"line": 56, "column": null}}, "24": {"start": {"line": 50, "column": 23}, "end": {"line": 50, "column": null}}, "25": {"start": {"line": 51, "column": 6}, "end": {"line": 53, "column": null}}, "26": {"start": {"line": 52, "column": 8}, "end": {"line": 52, "column": null}}, "27": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": null}}, "28": {"start": {"line": 60, "column": 2}, "end": {"line": 62, "column": null}}, "29": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": null}}, "30": {"start": {"line": 64, "column": 2}, "end": {"line": 68, "column": null}}, "31": {"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": null}}, "32": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": null}}, "33": {"start": {"line": 71, "column": 33}, "end": {"line": 85, "column": null}}, "34": {"start": {"line": 72, "column": 4}, "end": {"line": 84, "column": null}}, "35": {"start": {"line": 73, "column": 23}, "end": {"line": 73, "column": null}}, "36": {"start": {"line": 74, "column": 6}, "end": {"line": 81, "column": null}}, "37": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": null}}, "38": {"start": {"line": 76, "column": 8}, "end": {"line": 78, "column": null}}, "39": {"start": {"line": 77, "column": 10}, "end": {"line": 77, "column": null}}, "40": {"start": {"line": 79, "column": 8}, "end": {"line": 79, "column": null}}, "41": {"start": {"line": 80, "column": 8}, "end": {"line": 80, "column": null}}, "42": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": null}}, "43": {"start": {"line": 87, "column": 33}, "end": {"line": 103, "column": null}}, "44": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": null}}, "45": {"start": {"line": 88, "column": 29}, "end": {"line": 88, "column": null}}, "46": {"start": {"line": 90, "column": 4}, "end": {"line": 102, "column": null}}, "47": {"start": {"line": 91, "column": 23}, "end": {"line": 91, "column": null}}, "48": {"start": {"line": 92, "column": 6}, "end": {"line": 99, "column": null}}, "49": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": null}}, "50": {"start": {"line": 94, "column": 8}, "end": {"line": 96, "column": null}}, "51": {"start": {"line": 95, "column": 10}, "end": {"line": 95, "column": null}}, "52": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": null}}, "53": {"start": {"line": 98, "column": 8}, "end": {"line": 98, "column": null}}, "54": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": null}}, "55": {"start": {"line": 105, "column": 33}, "end": {"line": 119, "column": null}}, "56": {"start": {"line": 106, "column": 4}, "end": {"line": 108, "column": null}}, "57": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": null}}, "58": {"start": {"line": 110, "column": 4}, "end": {"line": 118, "column": null}}, "59": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": null}}, "60": {"start": {"line": 112, "column": 6}, "end": {"line": 112, "column": null}}, "61": {"start": {"line": 113, "column": 6}, "end": {"line": 115, "column": null}}, "62": {"start": {"line": 114, "column": 8}, "end": {"line": 114, "column": null}}, "63": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": null}}, "64": {"start": {"line": 121, "column": 31}, "end": {"line": 124, "column": null}}, "65": {"start": {"line": 122, "column": 4}, "end": {"line": 122, "column": null}}, "66": {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": null}}, "67": {"start": {"line": 126, "column": 31}, "end": {"line": 129, "column": null}}, "68": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": null}}, "69": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": null}}, "70": {"start": {"line": 131, "column": 27}, "end": {"line": 133, "column": null}}, "71": {"start": {"line": 132, "column": 4}, "end": {"line": 132, "column": null}}, "72": {"start": {"line": 132, "column": 24}, "end": {"line": 132, "column": null}}, "73": {"start": {"line": 135, "column": 28}, "end": {"line": 137, "column": null}}, "74": {"start": {"line": 136, "column": 4}, "end": {"line": 136, "column": null}}, "75": {"start": {"line": 136, "column": 24}, "end": {"line": 136, "column": null}}, "76": {"start": {"line": 139, "column": 27}, "end": {"line": 142, "column": null}}, "77": {"start": {"line": 140, "column": 4}, "end": {"line": 140, "column": null}}, "78": {"start": {"line": 141, "column": 4}, "end": {"line": 141, "column": null}}, "79": {"start": {"line": 144, "column": 24}, "end": {"line": 155, "column": null}}, "80": {"start": {"line": 145, "column": 4}, "end": {"line": 154, "column": null}}, "81": {"start": {"line": 147, "column": 8}, "end": {"line": 147, "column": null}}, "82": {"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": null}}, "83": {"start": {"line": 151, "column": 8}, "end": {"line": 151, "column": null}}, "84": {"start": {"line": 153, "column": 8}, "end": {"line": 153, "column": null}}, "85": {"start": {"line": 173, "column": 33}, "end": {"line": 173, "column": null}}, "86": {"start": {"line": 183, "column": 33}, "end": {"line": 183, "column": null}}, "87": {"start": {"line": 195, "column": 31}, "end": {"line": 195, "column": null}}, "88": {"start": {"line": 222, "column": 33}, "end": {"line": 222, "column": null}}, "89": {"start": {"line": 321, "column": 18}, "end": {"line": 321, "column": null}}, "90": {"start": {"line": 334, "column": 15}, "end": {"line": 334, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_5)", "decl": {"start": {"line": 15, "column": 34}, "end": {"line": 15, "column": null}}, "loc": {"start": {"line": 15, "column": 34}, "end": {"line": 332, "column": null}}}, "1": {"name": "(anonymous_6)", "decl": {"start": {"line": 30, "column": 26}, "end": {"line": 30, "column": null}}, "loc": {"start": {"line": 30, "column": 26}, "end": {"line": 46, "column": null}}}, "2": {"name": "(anonymous_7)", "decl": {"start": {"line": 48, "column": 21}, "end": {"line": 48, "column": null}}, "loc": {"start": {"line": 48, "column": 21}, "end": {"line": 57, "column": null}}}, "3": {"name": "(anonymous_8)", "decl": {"start": {"line": 60, "column": 12}, "end": {"line": 60, "column": null}}, "loc": {"start": {"line": 60, "column": 12}, "end": {"line": 62, "column": 5}}}, "4": {"name": "(anonymous_9)", "decl": {"start": {"line": 64, "column": 12}, "end": {"line": 64, "column": null}}, "loc": {"start": {"line": 64, "column": 12}, "end": {"line": 68, "column": 5}}}, "5": {"name": "(anonymous_10)", "decl": {"start": {"line": 71, "column": 33}, "end": {"line": 71, "column": 40}}, "loc": {"start": {"line": 71, "column": 40}, "end": {"line": 85, "column": null}}}, "6": {"name": "(anonymous_11)", "decl": {"start": {"line": 87, "column": 33}, "end": {"line": 87, "column": 40}}, "loc": {"start": {"line": 87, "column": 40}, "end": {"line": 103, "column": null}}}, "7": {"name": "(anonymous_12)", "decl": {"start": {"line": 105, "column": 33}, "end": {"line": 105, "column": 40}}, "loc": {"start": {"line": 105, "column": 40}, "end": {"line": 119, "column": null}}}, "8": {"name": "(anonymous_13)", "decl": {"start": {"line": 121, "column": 31}, "end": {"line": 121, "column": 32}}, "loc": {"start": {"line": 121, "column": 32}, "end": {"line": 124, "column": null}}}, "9": {"name": "(anonymous_14)", "decl": {"start": {"line": 126, "column": 31}, "end": {"line": 126, "column": 32}}, "loc": {"start": {"line": 126, "column": 32}, "end": {"line": 129, "column": null}}}, "10": {"name": "(anonymous_15)", "decl": {"start": {"line": 131, "column": 27}, "end": {"line": 131, "column": 28}}, "loc": {"start": {"line": 131, "column": 28}, "end": {"line": 133, "column": null}}}, "11": {"name": "(anonymous_16)", "decl": {"start": {"line": 132, "column": 15}, "end": {"line": 132, "column": 24}}, "loc": {"start": {"line": 132, "column": 24}, "end": {"line": 132, "column": null}}}, "12": {"name": "(anonymous_17)", "decl": {"start": {"line": 135, "column": 28}, "end": {"line": 135, "column": 29}}, "loc": {"start": {"line": 135, "column": 29}, "end": {"line": 137, "column": null}}}, "13": {"name": "(anonymous_18)", "decl": {"start": {"line": 136, "column": 15}, "end": {"line": 136, "column": 24}}, "loc": {"start": {"line": 136, "column": 24}, "end": {"line": 136, "column": null}}}, "14": {"name": "(anonymous_19)", "decl": {"start": {"line": 139, "column": 27}, "end": {"line": 139, "column": null}}, "loc": {"start": {"line": 139, "column": 27}, "end": {"line": 142, "column": null}}}, "15": {"name": "(anonymous_20)", "decl": {"start": {"line": 144, "column": 24}, "end": {"line": 144, "column": null}}, "loc": {"start": {"line": 144, "column": 24}, "end": {"line": 155, "column": null}}}, "16": {"name": "(anonymous_21)", "decl": {"start": {"line": 173, "column": 27}, "end": {"line": 173, "column": 33}}, "loc": {"start": {"line": 173, "column": 33}, "end": {"line": 173, "column": null}}}, "17": {"name": "(anonymous_22)", "decl": {"start": {"line": 183, "column": 27}, "end": {"line": 183, "column": 33}}, "loc": {"start": {"line": 183, "column": 33}, "end": {"line": 183, "column": null}}}, "18": {"name": "(anonymous_23)", "decl": {"start": {"line": 195, "column": 25}, "end": {"line": 195, "column": 31}}, "loc": {"start": {"line": 195, "column": 31}, "end": {"line": 195, "column": null}}}, "19": {"name": "(anonymous_24)", "decl": {"start": {"line": 222, "column": 27}, "end": {"line": 222, "column": 33}}, "loc": {"start": {"line": 222, "column": 33}, "end": {"line": 222, "column": null}}}, "20": {"name": "(anonymous_25)", "decl": {"start": {"line": 320, "column": 33}, "end": {"line": 320, "column": null}}, "loc": {"start": {"line": 320, "column": 33}, "end": {"line": 322, "column": 19}}}}, "branchMap": {"0": {"loc": {"start": {"line": 35, "column": 6}, "end": {"line": 40, "column": null}}, "type": "if", "locations": [{"start": {"line": 35, "column": 6}, "end": {"line": 40, "column": null}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 35, "column": 10}, "end": {"line": 35, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 35, "column": 10}, "end": {"line": 35, "column": 26}}, {"start": {"line": 35, "column": 30}, "end": {"line": 35, "column": 43}}]}, "2": {"loc": {"start": {"line": 37, "column": 8}, "end": {"line": 39, "column": null}}, "type": "if", "locations": [{"start": {"line": 37, "column": 8}, "end": {"line": 39, "column": null}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 51, "column": 6}, "end": {"line": 53, "column": null}}, "type": "if", "locations": [{"start": {"line": 51, "column": 6}, "end": {"line": 53, "column": null}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 51, "column": 10}, "end": {"line": 51, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 51, "column": 10}, "end": {"line": 51, "column": 26}}, {"start": {"line": 51, "column": 30}, "end": {"line": 51, "column": 43}}]}, "5": {"loc": {"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": null}}, "type": "if", "locations": [{"start": {"line": 65, "column": 4}, "end": {"line": 67, "column": null}}, {"start": {}, "end": {}}]}, "6": {"loc": {"start": {"line": 74, "column": 6}, "end": {"line": 81, "column": null}}, "type": "if", "locations": [{"start": {"line": 74, "column": 6}, "end": {"line": 81, "column": null}}, {"start": {}, "end": {}}]}, "7": {"loc": {"start": {"line": 76, "column": 8}, "end": {"line": 78, "column": null}}, "type": "if", "locations": [{"start": {"line": 76, "column": 8}, "end": {"line": 78, "column": null}}, {"start": {}, "end": {}}]}, "8": {"loc": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": null}}, "type": "if", "locations": [{"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": null}}, {"start": {}, "end": {}}]}, "9": {"loc": {"start": {"line": 92, "column": 6}, "end": {"line": 99, "column": null}}, "type": "if", "locations": [{"start": {"line": 92, "column": 6}, "end": {"line": 99, "column": null}}, {"start": {}, "end": {}}]}, "10": {"loc": {"start": {"line": 94, "column": 8}, "end": {"line": 96, "column": null}}, "type": "if", "locations": [{"start": {"line": 94, "column": 8}, "end": {"line": 96, "column": null}}, {"start": {}, "end": {}}]}, "11": {"loc": {"start": {"line": 106, "column": 4}, "end": {"line": 108, "column": null}}, "type": "if", "locations": [{"start": {"line": 106, "column": 4}, "end": {"line": 108, "column": null}}, {"start": {}, "end": {}}]}, "12": {"loc": {"start": {"line": 113, "column": 6}, "end": {"line": 115, "column": null}}, "type": "if", "locations": [{"start": {"line": 113, "column": 6}, "end": {"line": 115, "column": null}}, {"start": {}, "end": {}}]}, "13": {"loc": {"start": {"line": 145, "column": 4}, "end": {"line": 154, "column": null}}, "type": "switch", "locations": [{"start": {"line": 146, "column": 6}, "end": {"line": 147, "column": null}}, {"start": {"line": 148, "column": 6}, "end": {"line": 149, "column": null}}, {"start": {"line": 150, "column": 6}, "end": {"line": 151, "column": null}}, {"start": {"line": 152, "column": 6}, "end": {"line": 153, "column": null}}]}, "14": {"loc": {"start": {"line": 175, "column": 20}, "end": {"line": 177, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 176, "column": 24}, "end": {"line": 176, "column": null}}, {"start": {"line": 177, "column": 24}, "end": {"line": 177, "column": null}}]}, "15": {"loc": {"start": {"line": 185, "column": 20}, "end": {"line": 187, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 186, "column": 24}, "end": {"line": 186, "column": null}}, {"start": {"line": 187, "column": 24}, "end": {"line": 187, "column": null}}]}, "16": {"loc": {"start": {"line": 209, "column": 9}, "end": {"line": 209, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 209, "column": 9}, "end": {"line": 209, "column": null}}]}, "17": {"loc": {"start": {"line": 236, "column": 10}, "end": {"line": 247, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 236, "column": 10}, "end": {"line": 247, "column": null}}]}, "18": {"loc": {"start": {"line": 247, "column": 10}, "end": {"line": 247, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 247, "column": 10}, "end": {"line": 247, "column": null}}]}, "19": {"loc": {"start": {"line": 260, "column": 16}, "end": {"line": 260, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 260, "column": 39}, "end": {"line": 260, "column": 46}}, {"start": {"line": 260, "column": 46}, "end": {"line": 260, "column": null}}]}, "20": {"loc": {"start": {"line": 262, "column": 11}, "end": {"line": 262, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 262, "column": 11}, "end": {"line": 262, "column": null}}]}, "21": {"loc": {"start": {"line": 268, "column": 11}, "end": {"line": 268, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 268, "column": 11}, "end": {"line": 268, "column": 35}}, {"start": {"line": 268, "column": 35}, "end": {"line": 268, "column": null}}]}, "22": {"loc": {"start": {"line": 275, "column": 11}, "end": {"line": 275, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 275, "column": 11}, "end": {"line": 275, "column": 35}}, {"start": {"line": 275, "column": 35}, "end": {"line": 275, "column": null}}]}, "23": {"loc": {"start": {"line": 304, "column": 61}, "end": {"line": 304, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 304, "column": 61}, "end": {"line": 304, "column": 91}}, {"start": {"line": 304, "column": 95}, "end": {"line": 304, "column": null}}]}, "24": {"loc": {"start": {"line": 309, "column": 20}, "end": {"line": 309, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 309, "column": 50}, "end": {"line": 309, "column": 82}}, {"start": {"line": 309, "column": 82}, "end": {"line": 309, "column": null}}]}, "25": {"loc": {"start": {"line": 311, "column": 21}, "end": {"line": 311, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 311, "column": 51}, "end": {"line": 311, "column": 61}}, {"start": {"line": 311, "column": 61}, "end": {"line": 311, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0, 0, 0], "14": [0, 0], "15": [0, 0], "16": [0], "17": [0], "18": [0], "19": [0, 0], "20": [0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\app\\roles\\page.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\app\\roles\\page.tsx", "statementMap": {"0": {"start": {"line": 318, "column": 0}, "end": {"line": 318, "column": 15}}, "1": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 25}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 31}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 6, "column": 21}, "end": {"line": 6, "column": null}}, "5": {"start": {"line": 7, "column": 21}, "end": {"line": 7, "column": null}}, "6": {"start": {"line": 8, "column": 26}, "end": {"line": 8, "column": null}}, "7": {"start": {"line": 9, "column": 18}, "end": {"line": 9, "column": null}}, "8": {"start": {"line": 10, "column": 19}, "end": {"line": 10, "column": null}}, "9": {"start": {"line": 17, "column": 28}, "end": {"line": 316, "column": null}}, "10": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": null}}, "11": {"start": {"line": 41, "column": 26}, "end": {"line": 41, "column": null}}, "12": {"start": {"line": 44, "column": 36}, "end": {"line": 44, "column": null}}, "13": {"start": {"line": 45, "column": 34}, "end": {"line": 45, "column": null}}, "14": {"start": {"line": 48, "column": 2}, "end": {"line": 52, "column": null}}, "15": {"start": {"line": 49, "column": 4}, "end": {"line": 51, "column": null}}, "16": {"start": {"line": 50, "column": 6}, "end": {"line": 50, "column": null}}, "17": {"start": {"line": 55, "column": 27}, "end": {"line": 63, "column": null}}, "18": {"start": {"line": 56, "column": 4}, "end": {"line": 62, "column": null}}, "19": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": null}}, "20": {"start": {"line": 58, "column": 6}, "end": {"line": 58, "column": null}}, "21": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": null}}, "22": {"start": {"line": 65, "column": 27}, "end": {"line": 76, "column": null}}, "23": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": null}}, "24": {"start": {"line": 66, "column": 23}, "end": {"line": 66, "column": null}}, "25": {"start": {"line": 68, "column": 4}, "end": {"line": 75, "column": null}}, "26": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": null}}, "27": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": null}}, "28": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": null}}, "29": {"start": {"line": 72, "column": 6}, "end": {"line": 72, "column": null}}, "30": {"start": {"line": 78, "column": 27}, "end": {"line": 91, "column": null}}, "31": {"start": {"line": 79, "column": 4}, "end": {"line": 81, "column": null}}, "32": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": null}}, "33": {"start": {"line": 83, "column": 4}, "end": {"line": 90, "column": null}}, "34": {"start": {"line": 84, "column": 6}, "end": {"line": 84, "column": null}}, "35": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": null}}, "36": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": null}}, "37": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": null}}, "38": {"start": {"line": 93, "column": 27}, "end": {"line": 102, "column": null}}, "39": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": null}}, "40": {"start": {"line": 94, "column": 75}, "end": {"line": 94, "column": null}}, "41": {"start": {"line": 96, "column": 4}, "end": {"line": 101, "column": null}}, "42": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": null}}, "43": {"start": {"line": 98, "column": 6}, "end": {"line": 98, "column": null}}, "44": {"start": {"line": 104, "column": 25}, "end": {"line": 107, "column": null}}, "45": {"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": null}}, "46": {"start": {"line": 106, "column": 4}, "end": {"line": 106, "column": null}}, "47": {"start": {"line": 109, "column": 25}, "end": {"line": 112, "column": null}}, "48": {"start": {"line": 110, "column": 4}, "end": {"line": 110, "column": null}}, "49": {"start": {"line": 111, "column": 4}, "end": {"line": 111, "column": null}}, "50": {"start": {"line": 114, "column": 27}, "end": {"line": 116, "column": null}}, "51": {"start": {"line": 115, "column": 4}, "end": {"line": 115, "column": null}}, "52": {"start": {"line": 118, "column": 31}, "end": {"line": 120, "column": null}}, "53": {"start": {"line": 119, "column": 4}, "end": {"line": 119, "column": null}}, "54": {"start": {"line": 122, "column": 29}, "end": {"line": 124, "column": null}}, "55": {"start": {"line": 123, "column": 4}, "end": {"line": 123, "column": null}}, "56": {"start": {"line": 126, "column": 27}, "end": {"line": 129, "column": null}}, "57": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": null}}, "58": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": null}}, "59": {"start": {"line": 131, "column": 24}, "end": {"line": 142, "column": null}}, "60": {"start": {"line": 132, "column": 4}, "end": {"line": 141, "column": null}}, "61": {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": null}}, "62": {"start": {"line": 136, "column": 8}, "end": {"line": 136, "column": null}}, "63": {"start": {"line": 138, "column": 8}, "end": {"line": 138, "column": null}}, "64": {"start": {"line": 140, "column": 8}, "end": {"line": 140, "column": null}}, "65": {"start": {"line": 160, "column": 33}, "end": {"line": 160, "column": null}}, "66": {"start": {"line": 170, "column": 33}, "end": {"line": 170, "column": null}}, "67": {"start": {"line": 182, "column": 31}, "end": {"line": 182, "column": null}}, "68": {"start": {"line": 305, "column": 18}, "end": {"line": 305, "column": null}}, "69": {"start": {"line": 318, "column": 15}, "end": {"line": 318, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_5)", "decl": {"start": {"line": 17, "column": 28}, "end": {"line": 17, "column": null}}, "loc": {"start": {"line": 17, "column": 28}, "end": {"line": 316, "column": null}}}, "1": {"name": "(anonymous_6)", "decl": {"start": {"line": 48, "column": 18}, "end": {"line": 48, "column": null}}, "loc": {"start": {"line": 48, "column": 18}, "end": {"line": 52, "column": 5}}}, "2": {"name": "(anonymous_7)", "decl": {"start": {"line": 55, "column": 27}, "end": {"line": 55, "column": 34}}, "loc": {"start": {"line": 55, "column": 34}, "end": {"line": 63, "column": null}}}, "3": {"name": "(anonymous_8)", "decl": {"start": {"line": 65, "column": 27}, "end": {"line": 65, "column": 34}}, "loc": {"start": {"line": 65, "column": 34}, "end": {"line": 76, "column": null}}}, "4": {"name": "(anonymous_9)", "decl": {"start": {"line": 78, "column": 27}, "end": {"line": 78, "column": 34}}, "loc": {"start": {"line": 78, "column": 34}, "end": {"line": 91, "column": null}}}, "5": {"name": "(anonymous_10)", "decl": {"start": {"line": 93, "column": 27}, "end": {"line": 93, "column": 34}}, "loc": {"start": {"line": 93, "column": 34}, "end": {"line": 102, "column": null}}}, "6": {"name": "(anonymous_11)", "decl": {"start": {"line": 104, "column": 25}, "end": {"line": 104, "column": 26}}, "loc": {"start": {"line": 104, "column": 26}, "end": {"line": 107, "column": null}}}, "7": {"name": "(anonymous_12)", "decl": {"start": {"line": 109, "column": 25}, "end": {"line": 109, "column": 26}}, "loc": {"start": {"line": 109, "column": 26}, "end": {"line": 112, "column": null}}}, "8": {"name": "(anonymous_13)", "decl": {"start": {"line": 114, "column": 27}, "end": {"line": 114, "column": 28}}, "loc": {"start": {"line": 114, "column": 28}, "end": {"line": 116, "column": null}}}, "9": {"name": "(anonymous_14)", "decl": {"start": {"line": 118, "column": 31}, "end": {"line": 118, "column": 32}}, "loc": {"start": {"line": 118, "column": 32}, "end": {"line": 120, "column": null}}}, "10": {"name": "(anonymous_15)", "decl": {"start": {"line": 122, "column": 29}, "end": {"line": 122, "column": 30}}, "loc": {"start": {"line": 122, "column": 30}, "end": {"line": 124, "column": null}}}, "11": {"name": "(anonymous_16)", "decl": {"start": {"line": 126, "column": 27}, "end": {"line": 126, "column": null}}, "loc": {"start": {"line": 126, "column": 27}, "end": {"line": 129, "column": null}}}, "12": {"name": "(anonymous_17)", "decl": {"start": {"line": 131, "column": 24}, "end": {"line": 131, "column": null}}, "loc": {"start": {"line": 131, "column": 24}, "end": {"line": 142, "column": null}}}, "13": {"name": "(anonymous_18)", "decl": {"start": {"line": 160, "column": 27}, "end": {"line": 160, "column": 33}}, "loc": {"start": {"line": 160, "column": 33}, "end": {"line": 160, "column": null}}}, "14": {"name": "(anonymous_19)", "decl": {"start": {"line": 170, "column": 27}, "end": {"line": 170, "column": 33}}, "loc": {"start": {"line": 170, "column": 33}, "end": {"line": 170, "column": null}}}, "15": {"name": "(anonymous_20)", "decl": {"start": {"line": 182, "column": 25}, "end": {"line": 182, "column": 31}}, "loc": {"start": {"line": 182, "column": 31}, "end": {"line": 182, "column": null}}}, "16": {"name": "(anonymous_21)", "decl": {"start": {"line": 304, "column": 33}, "end": {"line": 304, "column": null}}, "loc": {"start": {"line": 304, "column": 33}, "end": {"line": 306, "column": 19}}}}, "branchMap": {"0": {"loc": {"start": {"line": 49, "column": 4}, "end": {"line": 51, "column": null}}, "type": "if", "locations": [{"start": {"line": 49, "column": 4}, "end": {"line": 51, "column": null}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": null}}, "type": "if", "locations": [{"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": null}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 79, "column": 4}, "end": {"line": 81, "column": null}}, "type": "if", "locations": [{"start": {"line": 79, "column": 4}, "end": {"line": 81, "column": null}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": null}}, "type": "if", "locations": [{"start": {"line": 94, "column": 4}, "end": {"line": 94, "column": null}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 132, "column": 4}, "end": {"line": 141, "column": null}}, "type": "switch", "locations": [{"start": {"line": 133, "column": 6}, "end": {"line": 134, "column": null}}, {"start": {"line": 135, "column": 6}, "end": {"line": 136, "column": null}}, {"start": {"line": 137, "column": 6}, "end": {"line": 138, "column": null}}, {"start": {"line": 139, "column": 6}, "end": {"line": 140, "column": null}}]}, "5": {"loc": {"start": {"line": 162, "column": 20}, "end": {"line": 164, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 163, "column": 24}, "end": {"line": 163, "column": null}}, {"start": {"line": 164, "column": 24}, "end": {"line": 164, "column": null}}]}, "6": {"loc": {"start": {"line": 172, "column": 20}, "end": {"line": 174, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 173, "column": 24}, "end": {"line": 173, "column": null}}, {"start": {"line": 174, "column": 24}, "end": {"line": 174, "column": null}}]}, "7": {"loc": {"start": {"line": 196, "column": 9}, "end": {"line": 196, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 196, "column": 9}, "end": {"line": 196, "column": null}}]}, "8": {"loc": {"start": {"line": 223, "column": 10}, "end": {"line": 243, "column": 11}}, "type": "cond-expr", "locations": [{"start": {"line": 223, "column": 10}, "end": {"line": 243, "column": 11}}]}, "9": {"loc": {"start": {"line": 254, "column": 16}, "end": {"line": 254, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 254, "column": 39}, "end": {"line": 254, "column": 46}}, {"start": {"line": 254, "column": 46}, "end": {"line": 254, "column": null}}]}, "10": {"loc": {"start": {"line": 256, "column": 11}, "end": {"line": 256, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 256, "column": 11}, "end": {"line": 256, "column": null}}]}, "11": {"loc": {"start": {"line": 263, "column": 11}, "end": {"line": 263, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 263, "column": 11}, "end": {"line": 263, "column": 35}}, {"start": {"line": 263, "column": 35}, "end": {"line": 263, "column": null}}]}, "12": {"loc": {"start": {"line": 271, "column": 11}, "end": {"line": 271, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 271, "column": 11}, "end": {"line": 271, "column": 35}}, {"start": {"line": 271, "column": 35}, "end": {"line": 271, "column": null}}]}, "13": {"loc": {"start": {"line": 284, "column": 61}, "end": {"line": 284, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 284, "column": 61}, "end": {"line": 284, "column": 85}}, {"start": {"line": 284, "column": 89}, "end": {"line": 284, "column": null}}]}, "14": {"loc": {"start": {"line": 289, "column": 20}, "end": {"line": 289, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 289, "column": 44}, "end": {"line": 289, "column": 76}}, {"start": {"line": 289, "column": 76}, "end": {"line": 289, "column": null}}]}, "15": {"loc": {"start": {"line": 291, "column": 21}, "end": {"line": 291, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 291, "column": 45}, "end": {"line": 291, "column": 55}}, {"start": {"line": 291, "column": 55}, "end": {"line": 291, "column": null}}]}, "16": {"loc": {"start": {"line": 296, "column": 61}, "end": {"line": 296, "column": 97}}, "type": "binary-expr", "locations": [{"start": {"line": 296, "column": 61}, "end": {"line": 296, "column": 95}}, {"start": {"line": 296, "column": 95}, "end": {"line": 296, "column": 97}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0, 0, 0], "5": [0, 0], "6": [0, 0], "7": [0], "8": [0], "9": [0, 0], "10": [0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\common\\Button.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\common\\Button.tsx", "statementMap": {"0": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 27}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 15, "column": 38}, "end": {"line": 69, "column": null}}, "4": {"start": {"line": 27, "column": 22}, "end": {"line": 27, "column": null}}, "5": {"start": {"line": 29, "column": 25}, "end": {"line": 35, "column": null}}, "6": {"start": {"line": 37, "column": 22}, "end": {"line": 41, "column": null}}, "7": {"start": {"line": 43, "column": 23}, "end": {"line": 43, "column": null}}, "8": {"start": {"line": 45, "column": 21}, "end": {"line": 45, "column": null}}, "9": {"start": {"line": 71, "column": 15}, "end": {"line": 71, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 15, "column": 38}, "end": {"line": 15, "column": 39}}, "loc": {"start": {"line": 26, "column": 1}, "end": {"line": 69, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 17, "column": 12}, "end": {"line": 17, "column": 21}}]}, "1": {"loc": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 18, "column": 9}, "end": {"line": 18, "column": 13}}]}, "2": {"loc": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 19, "column": 12}, "end": {"line": 19, "column": 17}}]}, "3": {"loc": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 22, "column": 14}, "end": {"line": 22, "column": 19}}]}, "4": {"loc": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 23, "column": 14}, "end": {"line": 23, "column": 16}}]}, "5": {"loc": {"start": {"line": 43, "column": 23}, "end": {"line": 43, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 43, "column": 35}, "end": {"line": 43, "column": 46}}, {"start": {"line": 43, "column": 46}, "end": {"line": 43, "column": null}}]}, "6": {"loc": {"start": {"line": 45, "column": 21}, "end": {"line": 45, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 45, "column": 21}, "end": {"line": 45, "column": 33}}, {"start": {"line": 45, "column": 33}, "end": {"line": 45, "column": null}}]}, "7": {"loc": {"start": {"line": 53, "column": 7}, "end": {"line": 53, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 7}, "end": {"line": 53, "column": null}}]}, "8": {"loc": {"start": {"line": 56, "column": 17}, "end": {"line": 56, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 56, "column": 41}, "end": {"line": 56, "column": 50}}, {"start": {"line": 56, "column": 50}, "end": {"line": 56, "column": null}}]}, "9": {"loc": {"start": {"line": 60, "column": 7}, "end": {"line": 60, "column": 19}}, "type": "binary-expr", "locations": [{"start": {"line": 60, "column": 7}, "end": {"line": 60, "column": 19}}, {"start": {"line": 60, "column": 19}, "end": {"line": 60, "column": null}}]}, "10": {"loc": {"start": {"line": 64, "column": 7}, "end": {"line": 64, "column": 19}}, "type": "binary-expr", "locations": [{"start": {"line": 64, "column": 7}, "end": {"line": 64, "column": 19}}, {"start": {"line": 64, "column": 19}, "end": {"line": 64, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0], "8": [0, 0], "9": [0, 0], "10": [0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\common\\LoadingSpinner.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\common\\LoadingSpinner.tsx", "statementMap": {"0": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 15}}, "1": {"start": {"line": 3, "column": 18}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 11, "column": 54}, "end": {"line": 40, "column": null}}, "3": {"start": {"line": 16, "column": 22}, "end": {"line": 21, "column": null}}, "4": {"start": {"line": 23, "column": 23}, "end": {"line": 29, "column": null}}, "5": {"start": {"line": 42, "column": 15}, "end": {"line": 42, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 11, "column": 54}, "end": {"line": 11, "column": 55}}, "loc": {"start": {"line": 15, "column": 1}, "end": {"line": 40, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 12, "column": 9}, "end": {"line": 12, "column": 13}}]}, "1": {"loc": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 13, "column": 10}, "end": {"line": 13, "column": 16}}]}, "2": {"loc": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 14, "column": 14}, "end": {"line": 14, "column": 16}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0}, "b": {"0": [0], "1": [0], "2": [0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\common\\Modal.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\common\\Modal.tsx", "statementMap": {"0": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 15}}, "1": {"start": {"line": 3, "column": 33}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 16, "column": 36}, "end": {"line": 119, "column": null}}, "3": {"start": {"line": 27, "column": 2}, "end": {"line": 38, "column": null}}, "4": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": null}}, "5": {"start": {"line": 28, "column": 35}, "end": {"line": 28, "column": null}}, "6": {"start": {"line": 30, "column": 25}, "end": {"line": 34, "column": null}}, "7": {"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": null}}, "8": {"start": {"line": 32, "column": 8}, "end": {"line": 32, "column": null}}, "9": {"start": {"line": 36, "column": 4}, "end": {"line": 36, "column": null}}, "10": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": null}}, "11": {"start": {"line": 37, "column": 17}, "end": {"line": 37, "column": null}}, "12": {"start": {"line": 41, "column": 2}, "end": {"line": 51, "column": null}}, "13": {"start": {"line": 42, "column": 4}, "end": {"line": 46, "column": null}}, "14": {"start": {"line": 43, "column": 6}, "end": {"line": 43, "column": null}}, "15": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": null}}, "16": {"start": {"line": 48, "column": 4}, "end": {"line": 50, "column": null}}, "17": {"start": {"line": 49, "column": 6}, "end": {"line": 49, "column": null}}, "18": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": null}}, "19": {"start": {"line": 53, "column": 15}, "end": {"line": 53, "column": null}}, "20": {"start": {"line": 55, "column": 22}, "end": {"line": 61, "column": null}}, "21": {"start": {"line": 63, "column": 29}, "end": {"line": 67, "column": null}}, "22": {"start": {"line": 64, "column": 4}, "end": {"line": 66, "column": null}}, "23": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": null}}, "24": {"start": {"line": 121, "column": 15}, "end": {"line": 121, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 16, "column": 36}, "end": {"line": 16, "column": 37}}, "loc": {"start": {"line": 25, "column": 1}, "end": {"line": 119, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 27, "column": 12}, "end": {"line": 27, "column": null}}, "loc": {"start": {"line": 27, "column": 12}, "end": {"line": 38, "column": 5}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 30, "column": 25}, "end": {"line": 30, "column": 26}}, "loc": {"start": {"line": 30, "column": 26}, "end": {"line": 34, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 37, "column": 11}, "end": {"line": 37, "column": 17}}, "loc": {"start": {"line": 37, "column": 17}, "end": {"line": 37, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 41, "column": 12}, "end": {"line": 41, "column": null}}, "loc": {"start": {"line": 41, "column": 12}, "end": {"line": 51, "column": 5}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 48, "column": 11}, "end": {"line": 48, "column": null}}, "loc": {"start": {"line": 48, "column": 11}, "end": {"line": 50, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 63, "column": 29}, "end": {"line": 63, "column": 30}}, "loc": {"start": {"line": 63, "column": 30}, "end": {"line": 67, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 13}}, "type": "default-arg", "locations": [{"start": {"line": 21, "column": 9}, "end": {"line": 21, "column": 13}}]}, "1": {"loc": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 24}}, "type": "default-arg", "locations": [{"start": {"line": 22, "column": 20}, "end": {"line": 22, "column": 24}}]}, "2": {"loc": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": 28}}, "type": "default-arg", "locations": [{"start": {"line": 23, "column": 24}, "end": {"line": 23, "column": 28}}]}, "3": {"loc": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 22}}, "type": "default-arg", "locations": [{"start": {"line": 24, "column": 18}, "end": {"line": 24, "column": 22}}]}, "4": {"loc": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": null}}, "type": "if", "locations": [{"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": null}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 28, "column": 8}, "end": {"line": 28, "column": 35}}, "type": "binary-expr", "locations": [{"start": {"line": 28, "column": 8}, "end": {"line": 28, "column": 26}}, {"start": {"line": 28, "column": 26}, "end": {"line": 28, "column": 35}}]}, "6": {"loc": {"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": null}}, "type": "if", "locations": [{"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": null}}, {"start": {}, "end": {}}]}, "7": {"loc": {"start": {"line": 42, "column": 4}, "end": {"line": 46, "column": null}}, "type": "if", "locations": [{"start": {"line": 42, "column": 4}, "end": {"line": 46, "column": null}}, {"start": {"line": 44, "column": 11}, "end": {"line": 46, "column": null}}]}, "8": {"loc": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": null}}, "type": "if", "locations": [{"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": null}}, {"start": {}, "end": {}}]}, "9": {"loc": {"start": {"line": 64, "column": 4}, "end": {"line": 66, "column": null}}, "type": "if", "locations": [{"start": {"line": 64, "column": 4}, "end": {"line": 66, "column": null}}, {"start": {}, "end": {}}]}, "10": {"loc": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 31}}, {"start": {"line": 64, "column": 31}, "end": {"line": 64, "column": 59}}]}, "11": {"loc": {"start": {"line": 83, "column": 12}, "end": {"line": 83, "column": 21}}, "type": "binary-expr", "locations": [{"start": {"line": 83, "column": 12}, "end": {"line": 83, "column": 21}}, {"start": {"line": 83, "column": 21}, "end": {"line": 83, "column": 35}}]}, "12": {"loc": {"start": {"line": 85, "column": 15}, "end": {"line": 85, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 85, "column": 15}, "end": {"line": 85, "column": null}}]}, "13": {"loc": {"start": {"line": 88, "column": 15}, "end": {"line": 88, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 88, "column": 15}, "end": {"line": 88, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0], "13": [0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\layout\\Navigation.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\layout\\Navigation.tsx", "statementMap": {"0": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 15}}, "1": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 4, "column": 17}, "end": {"line": 4, "column": null}}, "3": {"start": {"line": 5, "column": 28}, "end": {"line": 5, "column": null}}, "4": {"start": {"line": 7, "column": 29}, "end": {"line": 133, "column": null}}, "5": {"start": {"line": 8, "column": 30}, "end": {"line": 8, "column": null}}, "6": {"start": {"line": 9, "column": 19}, "end": {"line": 9, "column": null}}, "7": {"start": {"line": 11, "column": 21}, "end": {"line": 15, "column": null}}, "8": {"start": {"line": 17, "column": 19}, "end": {"line": 22, "column": null}}, "9": {"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": null}}, "10": {"start": {"line": 19, "column": 6}, "end": {"line": 19, "column": null}}, "11": {"start": {"line": 21, "column": 4}, "end": {"line": 21, "column": null}}, "12": {"start": {"line": 24, "column": 18}, "end": {"line": 47, "column": null}}, "13": {"start": {"line": 25, "column": 4}, "end": {"line": 46, "column": null}}, "14": {"start": {"line": 45, "column": 8}, "end": {"line": 45, "column": null}}, "15": {"start": {"line": 73, "column": 14}, "end": {"line": 74, "column": null}}, "16": {"start": {"line": 91, "column": 29}, "end": {"line": 91, "column": null}}, "17": {"start": {"line": 114, "column": 14}, "end": {"line": 115, "column": null}}, "18": {"start": {"line": 122, "column": 31}, "end": {"line": 122, "column": null}}, "19": {"start": {"line": 135, "column": 15}, "end": {"line": 135, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_5)", "decl": {"start": {"line": 7, "column": 29}, "end": {"line": 7, "column": null}}, "loc": {"start": {"line": 7, "column": 29}, "end": {"line": 133, "column": null}}}, "1": {"name": "(anonymous_6)", "decl": {"start": {"line": 17, "column": 19}, "end": {"line": 17, "column": 20}}, "loc": {"start": {"line": 17, "column": 20}, "end": {"line": 22, "column": null}}}, "2": {"name": "(anonymous_7)", "decl": {"start": {"line": 24, "column": 18}, "end": {"line": 24, "column": 19}}, "loc": {"start": {"line": 24, "column": 19}, "end": {"line": 47, "column": null}}}, "3": {"name": "(anonymous_8)", "decl": {"start": {"line": 72, "column": 28}, "end": {"line": 72, "column": 29}}, "loc": {"start": {"line": 73, "column": 14}, "end": {"line": 74, "column": null}}}, "4": {"name": "(anonymous_9)", "decl": {"start": {"line": 91, "column": 23}, "end": {"line": 91, "column": 29}}, "loc": {"start": {"line": 91, "column": 29}, "end": {"line": 91, "column": null}}}, "5": {"name": "(anonymous_10)", "decl": {"start": {"line": 113, "column": 28}, "end": {"line": 113, "column": 29}}, "loc": {"start": {"line": 114, "column": 14}, "end": {"line": 115, "column": null}}}, "6": {"name": "(anonymous_11)", "decl": {"start": {"line": 122, "column": 25}, "end": {"line": 122, "column": 31}}, "loc": {"start": {"line": 122, "column": 31}, "end": {"line": 122, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": null}}, "type": "if", "locations": [{"start": {"line": 18, "column": 4}, "end": {"line": 20, "column": null}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 25, "column": 4}, "end": {"line": 46, "column": null}}, "type": "switch", "locations": [{"start": {"line": 44, "column": 6}, "end": {"line": 45, "column": null}}]}, "2": {"loc": {"start": {"line": 77, "column": 18}, "end": {"line": 79, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 78, "column": 22}, "end": {"line": 78, "column": null}}, {"start": {"line": 79, "column": 22}, "end": {"line": 79, "column": null}}]}, "3": {"loc": {"start": {"line": 96, "column": 16}, "end": {"line": 100, "column": 17}}, "type": "cond-expr", "locations": [{"start": {"line": 96, "column": 16}, "end": {"line": 100, "column": 17}}]}, "4": {"loc": {"start": {"line": 110, "column": 7}, "end": {"line": 110, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 7}, "end": {"line": 110, "column": null}}]}, "5": {"loc": {"start": {"line": 118, "column": 18}, "end": {"line": 120, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 119, "column": 22}, "end": {"line": 119, "column": null}}, {"start": {"line": 120, "column": 22}, "end": {"line": 120, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {"0": [0, 0], "1": [0], "2": [0, 0], "3": [0], "4": [0], "5": [0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\permissions\\PermissionForm.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\permissions\\PermissionForm.tsx", "statementMap": {"0": {"start": {"line": 450, "column": 0}, "end": {"line": 450, "column": 15}}, "1": {"start": {"line": 3, "column": 43}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 13, "column": 7}, "end": {"line": 13, "column": null}}, "3": {"start": {"line": 25, "column": 54}, "end": {"line": 448, "column": null}}, "4": {"start": {"line": 35, "column": 36}, "end": {"line": 43, "column": null}}, "5": {"start": {"line": 45, "column": 30}, "end": {"line": 45, "column": null}}, "6": {"start": {"line": 46, "column": 32}, "end": {"line": 46, "column": null}}, "7": {"start": {"line": 47, "column": 42}, "end": {"line": 47, "column": null}}, "8": {"start": {"line": 48, "column": 44}, "end": {"line": 48, "column": null}}, "9": {"start": {"line": 51, "column": 2}, "end": {"line": 63, "column": null}}, "10": {"start": {"line": 52, "column": 4}, "end": {"line": 62, "column": null}}, "11": {"start": {"line": 53, "column": 6}, "end": {"line": 61, "column": null}}, "12": {"start": {"line": 66, "column": 2}, "end": {"line": 73, "column": null}}, "13": {"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": null}}, "14": {"start": {"line": 68, "column": 19}, "end": {"line": 68, "column": 103}}, "15": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": null}}, "16": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": null}}, "17": {"start": {"line": 76, "column": 24}, "end": {"line": 120, "column": null}}, "18": {"start": {"line": 77, "column": 4}, "end": {"line": 119, "column": null}}, "19": {"start": {"line": 79, "column": 8}, "end": {"line": 81, "column": null}}, "20": {"start": {"line": 80, "column": 10}, "end": {"line": 80, "column": null}}, "21": {"start": {"line": 82, "column": 8}, "end": {"line": 82, "column": null}}, "22": {"start": {"line": 85, "column": 8}, "end": {"line": 87, "column": null}}, "23": {"start": {"line": 86, "column": 10}, "end": {"line": 86, "column": null}}, "24": {"start": {"line": 88, "column": 8}, "end": {"line": 88, "column": null}}, "25": {"start": {"line": 91, "column": 8}, "end": {"line": 93, "column": null}}, "26": {"start": {"line": 92, "column": 10}, "end": {"line": 92, "column": null}}, "27": {"start": {"line": 94, "column": 8}, "end": {"line": 94, "column": null}}, "28": {"start": {"line": 97, "column": 8}, "end": {"line": 99, "column": null}}, "29": {"start": {"line": 98, "column": 10}, "end": {"line": 98, "column": null}}, "30": {"start": {"line": 100, "column": 8}, "end": {"line": 100, "column": null}}, "31": {"start": {"line": 103, "column": 8}, "end": {"line": 105, "column": null}}, "32": {"start": {"line": 104, "column": 10}, "end": {"line": 104, "column": null}}, "33": {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": null}}, "34": {"start": {"line": 109, "column": 8}, "end": {"line": 111, "column": null}}, "35": {"start": {"line": 110, "column": 10}, "end": {"line": 110, "column": null}}, "36": {"start": {"line": 112, "column": 8}, "end": {"line": 114, "column": null}}, "37": {"start": {"line": 113, "column": 10}, "end": {"line": 113, "column": null}}, "38": {"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": null}}, "39": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": null}}, "40": {"start": {"line": 122, "column": 23}, "end": {"line": 141, "column": null}}, "41": {"start": {"line": 123, "column": 46}, "end": {"line": 123, "column": null}}, "42": {"start": {"line": 125, "column": 4}, "end": {"line": 125, "column": null}}, "43": {"start": {"line": 126, "column": 4}, "end": {"line": 126, "column": null}}, "44": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": null}}, "45": {"start": {"line": 128, "column": 4}, "end": {"line": 128, "column": null}}, "46": {"start": {"line": 129, "column": 4}, "end": {"line": 129, "column": null}}, "47": {"start": {"line": 130, "column": 4}, "end": {"line": 130, "column": null}}, "48": {"start": {"line": 133, "column": 4}, "end": {"line": 137, "column": null}}, "49": {"start": {"line": 134, "column": 6}, "end": {"line": 136, "column": null}}, "50": {"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": null}}, "51": {"start": {"line": 139, "column": 4}, "end": {"line": 139, "column": null}}, "52": {"start": {"line": 140, "column": 4}, "end": {"line": 140, "column": null}}, "53": {"start": {"line": 143, "column": 28}, "end": {"line": 162, "column": null}}, "54": {"start": {"line": 144, "column": 4}, "end": {"line": 147, "column": null}}, "55": {"start": {"line": 144, "column": 26}, "end": {"line": 147, "column": null}}, "56": {"start": {"line": 150, "column": 4}, "end": {"line": 155, "column": null}}, "57": {"start": {"line": 151, "column": 6}, "end": {"line": 154, "column": null}}, "58": {"start": {"line": 151, "column": 25}, "end": {"line": 154, "column": null}}, "59": {"start": {"line": 158, "column": 4}, "end": {"line": 161, "column": null}}, "60": {"start": {"line": 158, "column": 24}, "end": {"line": 161, "column": null}}, "61": {"start": {"line": 164, "column": 21}, "end": {"line": 175, "column": null}}, "62": {"start": {"line": 165, "column": 4}, "end": {"line": 168, "column": null}}, "63": {"start": {"line": 165, "column": 24}, "end": {"line": 168, "column": null}}, "64": {"start": {"line": 170, "column": 18}, "end": {"line": 170, "column": null}}, "65": {"start": {"line": 171, "column": 4}, "end": {"line": 174, "column": null}}, "66": {"start": {"line": 171, "column": 23}, "end": {"line": 174, "column": null}}, "67": {"start": {"line": 177, "column": 23}, "end": {"line": 206, "column": null}}, "68": {"start": {"line": 178, "column": 4}, "end": {"line": 178, "column": null}}, "69": {"start": {"line": 180, "column": 4}, "end": {"line": 182, "column": null}}, "70": {"start": {"line": 181, "column": 6}, "end": {"line": 181, "column": null}}, "71": {"start": {"line": 184, "column": 4}, "end": {"line": 184, "column": null}}, "72": {"start": {"line": 186, "column": 4}, "end": {"line": 205, "column": null}}, "73": {"start": {"line": 187, "column": 70}, "end": {"line": 194, "column": null}}, "74": {"start": {"line": 196, "column": 6}, "end": {"line": 198, "column": null}}, "75": {"start": {"line": 197, "column": 9}, "end": {"line": 197, "column": null}}, "76": {"start": {"line": 200, "column": 6}, "end": {"line": 200, "column": null}}, "77": {"start": {"line": 202, "column": 6}, "end": {"line": 202, "column": null}}, "78": {"start": {"line": 204, "column": 6}, "end": {"line": 204, "column": null}}, "79": {"start": {"line": 208, "column": 24}, "end": {"line": 210, "column": null}}, "80": {"start": {"line": 209, "column": 4}, "end": {"line": 209, "column": null}}, "81": {"start": {"line": 212, "column": 22}, "end": {"line": 213, "column": 83}}, "82": {"start": {"line": 246, "column": 31}, "end": {"line": 246, "column": null}}, "83": {"start": {"line": 247, "column": 28}, "end": {"line": 247, "column": null}}, "84": {"start": {"line": 257, "column": 16}, "end": {"line": 257, "column": 37}}, "85": {"start": {"line": 273, "column": 31}, "end": {"line": 273, "column": null}}, "86": {"start": {"line": 274, "column": 28}, "end": {"line": 274, "column": null}}, "87": {"start": {"line": 284, "column": 16}, "end": {"line": 284, "column": 38}}, "88": {"start": {"line": 300, "column": 31}, "end": {"line": 300, "column": null}}, "89": {"start": {"line": 301, "column": 28}, "end": {"line": 301, "column": null}}, "90": {"start": {"line": 311, "column": 16}, "end": {"line": 311, "column": 37}}, "91": {"start": {"line": 327, "column": 31}, "end": {"line": 327, "column": null}}, "92": {"start": {"line": 328, "column": 28}, "end": {"line": 328, "column": null}}, "93": {"start": {"line": 338, "column": 16}, "end": {"line": 338, "column": 39}}, "94": {"start": {"line": 358, "column": 29}, "end": {"line": 358, "column": null}}, "95": {"start": {"line": 359, "column": 26}, "end": {"line": 359, "column": null}}, "96": {"start": {"line": 384, "column": 29}, "end": {"line": 384, "column": null}}, "97": {"start": {"line": 385, "column": 26}, "end": {"line": 385, "column": null}}, "98": {"start": {"line": 406, "column": 33}, "end": {"line": 406, "column": null}}, "99": {"start": {"line": 450, "column": 15}, "end": {"line": 450, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 25, "column": 54}, "end": {"line": 25, "column": 55}}, "loc": {"start": {"line": 34, "column": 1}, "end": {"line": 448, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 51, "column": 12}, "end": {"line": 51, "column": null}}, "loc": {"start": {"line": 51, "column": 12}, "end": {"line": 63, "column": 5}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 66, "column": 12}, "end": {"line": 66, "column": null}}, "loc": {"start": {"line": 66, "column": 12}, "end": {"line": 73, "column": 5}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 76, "column": 24}, "end": {"line": 76, "column": 25}}, "loc": {"start": {"line": 76, "column": 39}, "end": {"line": 120, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 122, "column": 23}, "end": {"line": 122, "column": null}}, "loc": {"start": {"line": 122, "column": 23}, "end": {"line": 141, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 133, "column": 35}, "end": {"line": 133, "column": null}}, "loc": {"start": {"line": 133, "column": 35}, "end": {"line": 137, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 143, "column": 28}, "end": {"line": 143, "column": 29}}, "loc": {"start": {"line": 143, "column": 43}, "end": {"line": 162, "column": null}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 144, "column": 17}, "end": {"line": 144, "column": 26}}, "loc": {"start": {"line": 144, "column": 26}, "end": {"line": 147, "column": null}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 151, "column": 16}, "end": {"line": 151, "column": 25}}, "loc": {"start": {"line": 151, "column": 25}, "end": {"line": 154, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 158, "column": 15}, "end": {"line": 158, "column": 24}}, "loc": {"start": {"line": 158, "column": 24}, "end": {"line": 161, "column": null}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 164, "column": 21}, "end": {"line": 164, "column": 22}}, "loc": {"start": {"line": 164, "column": 22}, "end": {"line": 175, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 165, "column": 15}, "end": {"line": 165, "column": 24}}, "loc": {"start": {"line": 165, "column": 24}, "end": {"line": 168, "column": null}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 171, "column": 14}, "end": {"line": 171, "column": 23}}, "loc": {"start": {"line": 171, "column": 23}, "end": {"line": 174, "column": null}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 177, "column": 23}, "end": {"line": 177, "column": 30}}, "loc": {"start": {"line": 177, "column": 30}, "end": {"line": 206, "column": null}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 208, "column": 24}, "end": {"line": 208, "column": 25}}, "loc": {"start": {"line": 208, "column": 25}, "end": {"line": 210, "column": null}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 246, "column": 24}, "end": {"line": 246, "column": 25}}, "loc": {"start": {"line": 246, "column": 31}, "end": {"line": 246, "column": null}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 247, "column": 22}, "end": {"line": 247, "column": 28}}, "loc": {"start": {"line": 247, "column": 28}, "end": {"line": 247, "column": null}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 256, "column": 36}, "end": {"line": 256, "column": null}}, "loc": {"start": {"line": 257, "column": 16}, "end": {"line": 257, "column": 37}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 273, "column": 24}, "end": {"line": 273, "column": 25}}, "loc": {"start": {"line": 273, "column": 31}, "end": {"line": 273, "column": null}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 274, "column": 22}, "end": {"line": 274, "column": 28}}, "loc": {"start": {"line": 274, "column": 28}, "end": {"line": 274, "column": null}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 283, "column": 37}, "end": {"line": 283, "column": null}}, "loc": {"start": {"line": 284, "column": 16}, "end": {"line": 284, "column": 38}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 300, "column": 24}, "end": {"line": 300, "column": 25}}, "loc": {"start": {"line": 300, "column": 31}, "end": {"line": 300, "column": null}}}, "22": {"name": "(anonymous_26)", "decl": {"start": {"line": 301, "column": 22}, "end": {"line": 301, "column": 28}}, "loc": {"start": {"line": 301, "column": 28}, "end": {"line": 301, "column": null}}}, "23": {"name": "(anonymous_27)", "decl": {"start": {"line": 310, "column": 36}, "end": {"line": 310, "column": null}}, "loc": {"start": {"line": 311, "column": 16}, "end": {"line": 311, "column": 37}}}, "24": {"name": "(anonymous_28)", "decl": {"start": {"line": 327, "column": 24}, "end": {"line": 327, "column": 25}}, "loc": {"start": {"line": 327, "column": 31}, "end": {"line": 327, "column": null}}}, "25": {"name": "(anonymous_29)", "decl": {"start": {"line": 328, "column": 22}, "end": {"line": 328, "column": 28}}, "loc": {"start": {"line": 328, "column": 28}, "end": {"line": 328, "column": null}}}, "26": {"name": "(anonymous_30)", "decl": {"start": {"line": 337, "column": 38}, "end": {"line": 337, "column": null}}, "loc": {"start": {"line": 338, "column": 16}, "end": {"line": 338, "column": 39}}}, "27": {"name": "(anonymous_31)", "decl": {"start": {"line": 358, "column": 22}, "end": {"line": 358, "column": 23}}, "loc": {"start": {"line": 358, "column": 29}, "end": {"line": 358, "column": null}}}, "28": {"name": "(anonymous_32)", "decl": {"start": {"line": 359, "column": 20}, "end": {"line": 359, "column": 26}}, "loc": {"start": {"line": 359, "column": 26}, "end": {"line": 359, "column": null}}}, "29": {"name": "(anonymous_33)", "decl": {"start": {"line": 384, "column": 22}, "end": {"line": 384, "column": 23}}, "loc": {"start": {"line": 384, "column": 29}, "end": {"line": 384, "column": null}}}, "30": {"name": "(anonymous_34)", "decl": {"start": {"line": 385, "column": 20}, "end": {"line": 385, "column": 26}}, "loc": {"start": {"line": 385, "column": 26}, "end": {"line": 385, "column": null}}}, "31": {"name": "(anonymous_35)", "decl": {"start": {"line": 406, "column": 26}, "end": {"line": 406, "column": 27}}, "loc": {"start": {"line": 406, "column": 33}, "end": {"line": 406, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 29, "column": 14}, "end": {"line": 29, "column": 19}}]}, "1": {"loc": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": null}}, "type": "default-arg", "locations": [{"start": {"line": 30, "column": 21}, "end": {"line": 30, "column": null}}]}, "2": {"loc": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": null}}, "type": "default-arg", "locations": [{"start": {"line": 31, "column": 22}, "end": {"line": 31, "column": null}}]}, "3": {"loc": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": null}}, "type": "default-arg", "locations": [{"start": {"line": 32, "column": 21}, "end": {"line": 32, "column": null}}]}, "4": {"loc": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": null}}, "type": "default-arg", "locations": [{"start": {"line": 33, "column": 23}, "end": {"line": 33, "column": null}}]}, "5": {"loc": {"start": {"line": 52, "column": 4}, "end": {"line": 62, "column": null}}, "type": "if", "locations": [{"start": {"line": 52, "column": 4}, "end": {"line": 62, "column": null}}, {"start": {}, "end": {}}]}, "6": {"loc": {"start": {"line": 58, "column": 21}, "end": {"line": 58, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 58, "column": 21}, "end": {"line": 58, "column": 43}}, {"start": {"line": 58, "column": 47}, "end": {"line": 58, "column": null}}]}, "7": {"loc": {"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": null}}, "type": "if", "locations": [{"start": {"line": 67, "column": 4}, "end": {"line": 72, "column": null}}, {"start": {"line": 70, "column": 11}, "end": {"line": 72, "column": null}}]}, "8": {"loc": {"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 87}}, "type": "binary-expr", "locations": [{"start": {"line": 67, "column": 8}, "end": {"line": 67, "column": 24}}, {"start": {"line": 67, "column": 28}, "end": {"line": 67, "column": 45}}, {"start": {"line": 67, "column": 49}, "end": {"line": 67, "column": 65}}, {"start": {"line": 67, "column": 69}, "end": {"line": 67, "column": 87}}]}, "9": {"loc": {"start": {"line": 77, "column": 4}, "end": {"line": 119, "column": null}}, "type": "switch", "locations": [{"start": {"line": 78, "column": 6}, "end": {"line": 82, "column": null}}, {"start": {"line": 84, "column": 6}, "end": {"line": 88, "column": null}}, {"start": {"line": 90, "column": 6}, "end": {"line": 94, "column": null}}, {"start": {"line": 96, "column": 6}, "end": {"line": 100, "column": null}}, {"start": {"line": 102, "column": 6}, "end": {"line": 106, "column": null}}, {"start": {"line": 108, "column": 6}, "end": {"line": 115, "column": null}}, {"start": {"line": 117, "column": 6}, "end": {"line": 118, "column": null}}]}, "10": {"loc": {"start": {"line": 79, "column": 8}, "end": {"line": 81, "column": null}}, "type": "if", "locations": [{"start": {"line": 79, "column": 8}, "end": {"line": 81, "column": null}}, {"start": {}, "end": {}}]}, "11": {"loc": {"start": {"line": 79, "column": 12}, "end": {"line": 79, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 79, "column": 12}, "end": {"line": 79, "column": 22}}, {"start": {"line": 79, "column": 22}, "end": {"line": 79, "column": 49}}]}, "12": {"loc": {"start": {"line": 85, "column": 8}, "end": {"line": 87, "column": null}}, "type": "if", "locations": [{"start": {"line": 85, "column": 8}, "end": {"line": 87, "column": null}}, {"start": {}, "end": {}}]}, "13": {"loc": {"start": {"line": 85, "column": 12}, "end": {"line": 85, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 85, "column": 12}, "end": {"line": 85, "column": 22}}, {"start": {"line": 85, "column": 22}, "end": {"line": 85, "column": 49}}]}, "14": {"loc": {"start": {"line": 91, "column": 8}, "end": {"line": 93, "column": null}}, "type": "if", "locations": [{"start": {"line": 91, "column": 8}, "end": {"line": 93, "column": null}}, {"start": {}, "end": {}}]}, "15": {"loc": {"start": {"line": 91, "column": 12}, "end": {"line": 91, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 91, "column": 12}, "end": {"line": 91, "column": 22}}, {"start": {"line": 91, "column": 22}, "end": {"line": 91, "column": 49}}]}, "16": {"loc": {"start": {"line": 97, "column": 8}, "end": {"line": 99, "column": null}}, "type": "if", "locations": [{"start": {"line": 97, "column": 8}, "end": {"line": 99, "column": null}}, {"start": {}, "end": {}}]}, "17": {"loc": {"start": {"line": 97, "column": 12}, "end": {"line": 97, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 97, "column": 12}, "end": {"line": 97, "column": 22}}, {"start": {"line": 97, "column": 22}, "end": {"line": 97, "column": 49}}]}, "18": {"loc": {"start": {"line": 103, "column": 8}, "end": {"line": 105, "column": null}}, "type": "if", "locations": [{"start": {"line": 103, "column": 8}, "end": {"line": 105, "column": null}}, {"start": {}, "end": {}}]}, "19": {"loc": {"start": {"line": 103, "column": 12}, "end": {"line": 103, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 103, "column": 12}, "end": {"line": 103, "column": 21}}, {"start": {"line": 103, "column": 21}, "end": {"line": 103, "column": 41}}]}, "20": {"loc": {"start": {"line": 109, "column": 8}, "end": {"line": 111, "column": null}}, "type": "if", "locations": [{"start": {"line": 109, "column": 8}, "end": {"line": 111, "column": null}}, {"start": {}, "end": {}}]}, "21": {"loc": {"start": {"line": 109, "column": 12}, "end": {"line": 109, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 109, "column": 12}, "end": {"line": 109, "column": 22}}, {"start": {"line": 109, "column": 22}, "end": {"line": 109, "column": 33}}]}, "22": {"loc": {"start": {"line": 112, "column": 8}, "end": {"line": 114, "column": null}}, "type": "if", "locations": [{"start": {"line": 112, "column": 8}, "end": {"line": 114, "column": null}}, {"start": {}, "end": {}}]}, "23": {"loc": {"start": {"line": 134, "column": 6}, "end": {"line": 136, "column": null}}, "type": "if", "locations": [{"start": {"line": 134, "column": 6}, "end": {"line": 136, "column": null}}, {"start": {}, "end": {}}]}, "24": {"loc": {"start": {"line": 150, "column": 4}, "end": {"line": 155, "column": null}}, "type": "if", "locations": [{"start": {"line": 150, "column": 4}, "end": {"line": 155, "column": null}}, {"start": {}, "end": {}}]}, "25": {"loc": {"start": {"line": 180, "column": 4}, "end": {"line": 182, "column": null}}, "type": "if", "locations": [{"start": {"line": 180, "column": 4}, "end": {"line": 182, "column": null}}, {"start": {}, "end": {}}]}, "26": {"loc": {"start": {"line": 192, "column": 21}, "end": {"line": 192, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 192, "column": 21}, "end": {"line": 192, "column": 53}}, {"start": {"line": 192, "column": 53}, "end": {"line": 192, "column": null}}]}, "27": {"loc": {"start": {"line": 196, "column": 6}, "end": {"line": 198, "column": null}}, "type": "if", "locations": [{"start": {"line": 196, "column": 6}, "end": {"line": 198, "column": null}}, {"start": {}, "end": {}}]}, "28": {"loc": {"start": {"line": 209, "column": 11}, "end": {"line": 209, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 209, "column": 32}, "end": {"line": 209, "column": 58}}, {"start": {"line": 209, "column": 58}, "end": {"line": 209, "column": null}}]}, "29": {"loc": {"start": {"line": 209, "column": 32}, "end": {"line": 209, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 209, "column": 32}, "end": {"line": 209, "column": 49}}, {"start": {"line": 209, "column": 53}, "end": {"line": 209, "column": 58}}]}, "30": {"loc": {"start": {"line": 212, "column": 22}, "end": {"line": 213, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 212, "column": 22}, "end": {"line": 212, "column": null}}, {"start": {"line": 213, "column": 4}, "end": {"line": 213, "column": 20}}, {"start": {"line": 213, "column": 24}, "end": {"line": 213, "column": 41}}, {"start": {"line": 213, "column": 45}, "end": {"line": 213, "column": 61}}, {"start": {"line": 213, "column": 65}, "end": {"line": 213, "column": 83}}]}, "31": {"loc": {"start": {"line": 219, "column": 11}, "end": {"line": 219, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 219, "column": 24}, "end": {"line": 219, "column": 44}}, {"start": {"line": 219, "column": 44}, "end": {"line": 219, "column": null}}]}, "32": {"loc": {"start": {"line": 225, "column": 9}, "end": {"line": 225, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 225, "column": 9}, "end": {"line": 225, "column": null}}]}, "33": {"loc": {"start": {"line": 249, "column": 16}, "end": {"line": 251, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 250, "column": 20}, "end": {"line": 250, "column": null}}, {"start": {"line": 251, "column": 20}, "end": {"line": 251, "column": null}}]}, "34": {"loc": {"start": {"line": 253, "column": 24}, "end": {"line": 253, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 253, "column": 24}, "end": {"line": 253, "column": 37}}, {"start": {"line": 253, "column": 37}, "end": {"line": 253, "column": null}}]}, "35": {"loc": {"start": {"line": 260, "column": 13}, "end": {"line": 260, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 260, "column": 13}, "end": {"line": 260, "column": null}}]}, "36": {"loc": {"start": {"line": 276, "column": 16}, "end": {"line": 278, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 277, "column": 20}, "end": {"line": 277, "column": null}}, {"start": {"line": 278, "column": 20}, "end": {"line": 278, "column": null}}]}, "37": {"loc": {"start": {"line": 280, "column": 24}, "end": {"line": 280, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 280, "column": 24}, "end": {"line": 280, "column": 37}}, {"start": {"line": 280, "column": 37}, "end": {"line": 280, "column": null}}]}, "38": {"loc": {"start": {"line": 287, "column": 13}, "end": {"line": 287, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 287, "column": 13}, "end": {"line": 287, "column": null}}]}, "39": {"loc": {"start": {"line": 303, "column": 16}, "end": {"line": 305, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 304, "column": 20}, "end": {"line": 304, "column": null}}, {"start": {"line": 305, "column": 20}, "end": {"line": 305, "column": null}}]}, "40": {"loc": {"start": {"line": 307, "column": 24}, "end": {"line": 307, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 307, "column": 24}, "end": {"line": 307, "column": 37}}, {"start": {"line": 307, "column": 37}, "end": {"line": 307, "column": null}}]}, "41": {"loc": {"start": {"line": 314, "column": 13}, "end": {"line": 314, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 314, "column": 13}, "end": {"line": 314, "column": null}}]}, "42": {"loc": {"start": {"line": 330, "column": 16}, "end": {"line": 332, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 331, "column": 20}, "end": {"line": 331, "column": null}}, {"start": {"line": 332, "column": 20}, "end": {"line": 332, "column": null}}]}, "43": {"loc": {"start": {"line": 334, "column": 24}, "end": {"line": 334, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 334, "column": 24}, "end": {"line": 334, "column": 37}}, {"start": {"line": 334, "column": 37}, "end": {"line": 334, "column": null}}]}, "44": {"loc": {"start": {"line": 341, "column": 13}, "end": {"line": 341, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 341, "column": 13}, "end": {"line": 341, "column": null}}]}, "45": {"loc": {"start": {"line": 358, "column": 68}, "end": {"line": 358, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 358, "column": 68}, "end": {"line": 358, "column": 96}}, {"start": {"line": 358, "column": 96}, "end": {"line": 358, "column": null}}]}, "46": {"loc": {"start": {"line": 361, "column": 14}, "end": {"line": 363, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 362, "column": 18}, "end": {"line": 362, "column": null}}, {"start": {"line": 363, "column": 18}, "end": {"line": 363, "column": null}}]}, "47": {"loc": {"start": {"line": 365, "column": 22}, "end": {"line": 365, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 365, "column": 22}, "end": {"line": 365, "column": 35}}, {"start": {"line": 365, "column": 35}, "end": {"line": 365, "column": null}}]}, "48": {"loc": {"start": {"line": 367, "column": 11}, "end": {"line": 367, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 367, "column": 11}, "end": {"line": 367, "column": null}}]}, "49": {"loc": {"start": {"line": 387, "column": 14}, "end": {"line": 389, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 388, "column": 18}, "end": {"line": 388, "column": null}}, {"start": {"line": 389, "column": 18}, "end": {"line": 389, "column": null}}]}, "50": {"loc": {"start": {"line": 392, "column": 22}, "end": {"line": 392, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 392, "column": 22}, "end": {"line": 392, "column": 35}}, {"start": {"line": 392, "column": 35}, "end": {"line": 392, "column": null}}]}, "51": {"loc": {"start": {"line": 394, "column": 11}, "end": {"line": 394, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 394, "column": 11}, "end": {"line": 394, "column": null}}]}, "52": {"loc": {"start": {"line": 400, "column": 9}, "end": {"line": 400, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 400, "column": 9}, "end": {"line": 400, "column": null}}]}, "53": {"loc": {"start": {"line": 408, "column": 26}, "end": {"line": 408, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 408, "column": 26}, "end": {"line": 408, "column": 39}}, {"start": {"line": 408, "column": 39}, "end": {"line": 408, "column": null}}]}, "54": {"loc": {"start": {"line": 426, "column": 22}, "end": {"line": 426, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 426, "column": 22}, "end": {"line": 426, "column": 35}}, {"start": {"line": 426, "column": 35}, "end": {"line": 426, "column": null}}]}, "55": {"loc": {"start": {"line": 432, "column": 22}, "end": {"line": 432, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 432, "column": 22}, "end": {"line": 432, "column": 38}}, {"start": {"line": 432, "column": 38}, "end": {"line": 432, "column": 51}}, {"start": {"line": 432, "column": 51}, "end": {"line": 432, "column": null}}]}, "56": {"loc": {"start": {"line": 435, "column": 13}, "end": {"line": 441, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 436, "column": 14}, "end": {"line": 441, "column": 27}}, {"start": {"line": 441, "column": 14}, "end": {"line": 441, "column": null}}]}, "57": {"loc": {"start": {"line": 438, "column": 17}, "end": {"line": 438, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 438, "column": 30}, "end": {"line": 438, "column": 47}}, {"start": {"line": 438, "column": 47}, "end": {"line": 438, "column": null}}]}, "58": {"loc": {"start": {"line": 441, "column": 14}, "end": {"line": 441, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 441, "column": 27}, "end": {"line": 441, "column": 48}}, {"start": {"line": 441, "column": 48}, "end": {"line": 441, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0, "98": 0, "99": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0, 0, 0], "9": [0, 0, 0, 0, 0, 0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [0, 0], "23": [0, 0], "24": [0, 0], "25": [0, 0], "26": [0, 0], "27": [0, 0], "28": [0, 0], "29": [0, 0], "30": [0, 0, 0, 0, 0], "31": [0, 0], "32": [0], "33": [0, 0], "34": [0, 0], "35": [0], "36": [0, 0], "37": [0, 0], "38": [0], "39": [0, 0], "40": [0, 0], "41": [0], "42": [0, 0], "43": [0, 0], "44": [0], "45": [0, 0], "46": [0, 0], "47": [0, 0], "48": [0], "49": [0, 0], "50": [0, 0], "51": [0], "52": [0], "53": [0, 0], "54": [0, 0], "55": [0, 0, 0], "56": [0, 0], "57": [0, 0], "58": [0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\permissions\\PermissionList.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\permissions\\PermissionList.tsx", "statementMap": {"0": {"start": {"line": 447, "column": 0}, "end": {"line": 447, "column": 15}}, "1": {"start": {"line": 3, "column": 41}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 10, "column": 63}, "end": {"line": 445, "column": null}}, "3": {"start": {"line": 21, "column": 36}, "end": {"line": 21, "column": null}}, "4": {"start": {"line": 22, "column": 44}, "end": {"line": 22, "column": null}}, "5": {"start": {"line": 23, "column": 56}, "end": {"line": 23, "column": null}}, "6": {"start": {"line": 24, "column": 42}, "end": {"line": 24, "column": null}}, "7": {"start": {"line": 25, "column": 44}, "end": {"line": 25, "column": null}}, "8": {"start": {"line": 26, "column": 42}, "end": {"line": 26, "column": null}}, "9": {"start": {"line": 29, "column": 24}, "end": {"line": 31, "column": null}}, "10": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": null}}, "11": {"start": {"line": 30, "column": 51}, "end": {"line": 30, "column": 59}}, "12": {"start": {"line": 33, "column": 25}, "end": {"line": 35, "column": null}}, "13": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": null}}, "14": {"start": {"line": 34, "column": 51}, "end": {"line": 34, "column": 60}}, "15": {"start": {"line": 37, "column": 24}, "end": {"line": 39, "column": null}}, "16": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": null}}, "17": {"start": {"line": 38, "column": 51}, "end": {"line": 38, "column": 59}}, "18": {"start": {"line": 42, "column": 39}, "end": {"line": 62, "column": null}}, "19": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": null}}, "20": {"start": {"line": 43, "column": 22}, "end": {"line": 43, "column": null}}, "21": {"start": {"line": 45, "column": 19}, "end": {"line": 51, "column": null}}, "22": {"start": {"line": 46, "column": 6}, "end": {"line": 49, "column": null}}, "23": {"start": {"line": 53, "column": 4}, "end": {"line": 61, "column": null}}, "24": {"start": {"line": 54, "column": 21}, "end": {"line": 54, "column": 33}}, "25": {"start": {"line": 55, "column": 21}, "end": {"line": 55, "column": 33}}, "26": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": null}}, "27": {"start": {"line": 57, "column": 29}, "end": {"line": 57, "column": null}}, "28": {"start": {"line": 59, "column": 25}, "end": {"line": 59, "column": null}}, "29": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": null}}, "30": {"start": {"line": 64, "column": 21}, "end": {"line": 71, "column": null}}, "31": {"start": {"line": 65, "column": 4}, "end": {"line": 70, "column": null}}, "32": {"start": {"line": 66, "column": 6}, "end": {"line": 66, "column": null}}, "33": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": null}}, "34": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": null}}, "35": {"start": {"line": 73, "column": 33}, "end": {"line": 81, "column": null}}, "36": {"start": {"line": 74, "column": 24}, "end": {"line": 74, "column": null}}, "37": {"start": {"line": 75, "column": 4}, "end": {"line": 79, "column": null}}, "38": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": null}}, "39": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": null}}, "40": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": null}}, "41": {"start": {"line": 83, "column": 26}, "end": {"line": 89, "column": null}}, "42": {"start": {"line": 84, "column": 4}, "end": {"line": 88, "column": null}}, "43": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": null}}, "44": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": null}}, "45": {"start": {"line": 87, "column": 75}, "end": {"line": 87, "column": 79}}, "46": {"start": {"line": 91, "column": 22}, "end": {"line": 96, "column": null}}, "47": {"start": {"line": 92, "column": 4}, "end": {"line": 94, "column": null}}, "48": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": null}}, "49": {"start": {"line": 95, "column": 4}, "end": {"line": 95, "column": null}}, "50": {"start": {"line": 98, "column": 33}, "end": {"line": 102, "column": null}}, "51": {"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": null}}, "52": {"start": {"line": 99, "column": 24}, "end": {"line": 99, "column": null}}, "53": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": null}}, "54": {"start": {"line": 100, "column": 24}, "end": {"line": 100, "column": null}}, "55": {"start": {"line": 101, "column": 4}, "end": {"line": 101, "column": null}}, "56": {"start": {"line": 104, "column": 30}, "end": {"line": 116, "column": null}}, "57": {"start": {"line": 105, "column": 43}, "end": {"line": 114, "column": null}}, "58": {"start": {"line": 115, "column": 4}, "end": {"line": 115, "column": null}}, "59": {"start": {"line": 118, "column": 2}, "end": {"line": 139, "column": null}}, "60": {"start": {"line": 127, "column": 14}, "end": {"line": 127, "column": 27}}, "61": {"start": {"line": 155, "column": 31}, "end": {"line": 155, "column": null}}, "62": {"start": {"line": 174, "column": 31}, "end": {"line": 174, "column": null}}, "63": {"start": {"line": 179, "column": 16}, "end": {"line": 179, "column": 37}}, "64": {"start": {"line": 189, "column": 31}, "end": {"line": 189, "column": null}}, "65": {"start": {"line": 194, "column": 16}, "end": {"line": 194, "column": 38}}, "66": {"start": {"line": 204, "column": 31}, "end": {"line": 204, "column": null}}, "67": {"start": {"line": 209, "column": 16}, "end": {"line": 209, "column": 37}}, "68": {"start": {"line": 218, "column": 16}, "end": {"line": 218, "column": null}}, "69": {"start": {"line": 219, "column": 16}, "end": {"line": 219, "column": null}}, "70": {"start": {"line": 220, "column": 16}, "end": {"line": 220, "column": null}}, "71": {"start": {"line": 245, "column": 31}, "end": {"line": 245, "column": null}}, "72": {"start": {"line": 254, "column": 31}, "end": {"line": 254, "column": null}}, "73": {"start": {"line": 263, "column": 31}, "end": {"line": 263, "column": null}}, "74": {"start": {"line": 272, "column": 31}, "end": {"line": 272, "column": null}}, "75": {"start": {"line": 281, "column": 31}, "end": {"line": 281, "column": null}}, "76": {"start": {"line": 290, "column": 31}, "end": {"line": 290, "column": null}}, "77": {"start": {"line": 307, "column": 14}, "end": {"line": 308, "column": null}}, "78": {"start": {"line": 317, "column": 36}, "end": {"line": 317, "column": null}}, "79": {"start": {"line": 372, "column": 37}, "end": {"line": 372, "column": null}}, "80": {"start": {"line": 378, "column": 37}, "end": {"line": 378, "column": null}}, "81": {"start": {"line": 384, "column": 37}, "end": {"line": 384, "column": null}}, "82": {"start": {"line": 423, "column": 31}, "end": {"line": 423, "column": null}}, "83": {"start": {"line": 433, "column": 31}, "end": {"line": 433, "column": null}}, "84": {"start": {"line": 447, "column": 15}, "end": {"line": 447, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 10, "column": 63}, "end": {"line": 10, "column": 64}}, "loc": {"start": {"line": 20, "column": 1}, "end": {"line": 445, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 29, "column": 32}, "end": {"line": 29, "column": null}}, "loc": {"start": {"line": 29, "column": 32}, "end": {"line": 31, "column": 5}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 30, "column": 46}, "end": {"line": 30, "column": 51}}, "loc": {"start": {"line": 30, "column": 51}, "end": {"line": 30, "column": 59}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 33, "column": 33}, "end": {"line": 33, "column": null}}, "loc": {"start": {"line": 33, "column": 33}, "end": {"line": 35, "column": 5}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 34, "column": 46}, "end": {"line": 34, "column": 51}}, "loc": {"start": {"line": 34, "column": 51}, "end": {"line": 34, "column": 60}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 37, "column": 32}, "end": {"line": 37, "column": null}}, "loc": {"start": {"line": 37, "column": 32}, "end": {"line": 39, "column": 5}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 38, "column": 46}, "end": {"line": 38, "column": 51}}, "loc": {"start": {"line": 38, "column": 51}, "end": {"line": 38, "column": 59}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 42, "column": 47}, "end": {"line": 42, "column": null}}, "loc": {"start": {"line": 42, "column": 47}, "end": {"line": 62, "column": 5}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 45, "column": 38}, "end": {"line": 45, "column": null}}, "loc": {"start": {"line": 45, "column": 38}, "end": {"line": 51, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 53, "column": 25}, "end": {"line": 53, "column": 26}}, "loc": {"start": {"line": 53, "column": 29}, "end": {"line": 61, "column": null}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 64, "column": 21}, "end": {"line": 64, "column": 22}}, "loc": {"start": {"line": 64, "column": 22}, "end": {"line": 71, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 73, "column": 33}, "end": {"line": 73, "column": 34}}, "loc": {"start": {"line": 73, "column": 34}, "end": {"line": 81, "column": null}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 83, "column": 26}, "end": {"line": 83, "column": null}}, "loc": {"start": {"line": 83, "column": 26}, "end": {"line": 89, "column": null}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 87, "column": 70}, "end": {"line": 87, "column": 75}}, "loc": {"start": {"line": 87, "column": 75}, "end": {"line": 87, "column": 79}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 91, "column": 22}, "end": {"line": 91, "column": 23}}, "loc": {"start": {"line": 91, "column": 23}, "end": {"line": 96, "column": null}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 98, "column": 33}, "end": {"line": 98, "column": 34}}, "loc": {"start": {"line": 98, "column": 34}, "end": {"line": 102, "column": null}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 104, "column": 30}, "end": {"line": 104, "column": 31}}, "loc": {"start": {"line": 104, "column": 31}, "end": {"line": 116, "column": null}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 126, "column": 31}, "end": {"line": 126, "column": 32}}, "loc": {"start": {"line": 127, "column": 14}, "end": {"line": 127, "column": 27}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 155, "column": 25}, "end": {"line": 155, "column": 31}}, "loc": {"start": {"line": 155, "column": 31}, "end": {"line": 155, "column": null}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 174, "column": 24}, "end": {"line": 174, "column": 25}}, "loc": {"start": {"line": 174, "column": 31}, "end": {"line": 174, "column": null}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 178, "column": 33}, "end": {"line": 178, "column": null}}, "loc": {"start": {"line": 179, "column": 16}, "end": {"line": 179, "column": 37}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 189, "column": 24}, "end": {"line": 189, "column": 25}}, "loc": {"start": {"line": 189, "column": 31}, "end": {"line": 189, "column": null}}}, "22": {"name": "(anonymous_26)", "decl": {"start": {"line": 193, "column": 34}, "end": {"line": 193, "column": null}}, "loc": {"start": {"line": 194, "column": 16}, "end": {"line": 194, "column": 38}}}, "23": {"name": "(anonymous_27)", "decl": {"start": {"line": 204, "column": 24}, "end": {"line": 204, "column": 25}}, "loc": {"start": {"line": 204, "column": 31}, "end": {"line": 204, "column": null}}}, "24": {"name": "(anonymous_28)", "decl": {"start": {"line": 208, "column": 33}, "end": {"line": 208, "column": null}}, "loc": {"start": {"line": 209, "column": 16}, "end": {"line": 209, "column": 37}}}, "25": {"name": "(anonymous_29)", "decl": {"start": {"line": 217, "column": 23}, "end": {"line": 217, "column": null}}, "loc": {"start": {"line": 217, "column": 23}, "end": {"line": 221, "column": null}}}, "26": {"name": "(anonymous_30)", "decl": {"start": {"line": 245, "column": 25}, "end": {"line": 245, "column": 31}}, "loc": {"start": {"line": 245, "column": 31}, "end": {"line": 245, "column": null}}}, "27": {"name": "(anonymous_31)", "decl": {"start": {"line": 254, "column": 25}, "end": {"line": 254, "column": 31}}, "loc": {"start": {"line": 254, "column": 31}, "end": {"line": 254, "column": null}}}, "28": {"name": "(anonymous_32)", "decl": {"start": {"line": 263, "column": 25}, "end": {"line": 263, "column": 31}}, "loc": {"start": {"line": 263, "column": 31}, "end": {"line": 263, "column": null}}}, "29": {"name": "(anonymous_33)", "decl": {"start": {"line": 272, "column": 25}, "end": {"line": 272, "column": 31}}, "loc": {"start": {"line": 272, "column": 31}, "end": {"line": 272, "column": null}}}, "30": {"name": "(anonymous_34)", "decl": {"start": {"line": 281, "column": 25}, "end": {"line": 281, "column": 31}}, "loc": {"start": {"line": 281, "column": 31}, "end": {"line": 281, "column": null}}}, "31": {"name": "(anonymous_35)", "decl": {"start": {"line": 290, "column": 25}, "end": {"line": 290, "column": 31}}, "loc": {"start": {"line": 290, "column": 31}, "end": {"line": 290, "column": null}}}, "32": {"name": "(anonymous_36)", "decl": {"start": {"line": 306, "column": 46}, "end": {"line": 306, "column": 47}}, "loc": {"start": {"line": 307, "column": 14}, "end": {"line": 308, "column": null}}}, "33": {"name": "(anonymous_37)", "decl": {"start": {"line": 317, "column": 30}, "end": {"line": 317, "column": 36}}, "loc": {"start": {"line": 317, "column": 36}, "end": {"line": 317, "column": null}}}, "34": {"name": "(anonymous_38)", "decl": {"start": {"line": 372, "column": 31}, "end": {"line": 372, "column": 37}}, "loc": {"start": {"line": 372, "column": 37}, "end": {"line": 372, "column": null}}}, "35": {"name": "(anonymous_39)", "decl": {"start": {"line": 378, "column": 31}, "end": {"line": 378, "column": 37}}, "loc": {"start": {"line": 378, "column": 37}, "end": {"line": 378, "column": null}}}, "36": {"name": "(anonymous_40)", "decl": {"start": {"line": 384, "column": 31}, "end": {"line": 384, "column": 37}}, "loc": {"start": {"line": 384, "column": 37}, "end": {"line": 384, "column": null}}}, "37": {"name": "(anonymous_41)", "decl": {"start": {"line": 423, "column": 25}, "end": {"line": 423, "column": 31}}, "loc": {"start": {"line": 423, "column": 31}, "end": {"line": 423, "column": null}}}, "38": {"name": "(anonymous_42)", "decl": {"start": {"line": 433, "column": 25}, "end": {"line": 433, "column": 31}}, "loc": {"start": {"line": 433, "column": 31}, "end": {"line": 433, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 17}}]}, "1": {"loc": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 16}}]}, "2": {"loc": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": null}}, "type": "if", "locations": [{"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": null}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 47, "column": 8}, "end": {"line": 49, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 47, "column": 9}, "end": {"line": 47, "column": 26}}, {"start": {"line": 47, "column": 26}, "end": {"line": 47, "column": 59}}, {"start": {"line": 48, "column": 9}, "end": {"line": 48, "column": 27}}, {"start": {"line": 48, "column": 27}, "end": {"line": 48, "column": 62}}, {"start": {"line": 49, "column": 9}, "end": {"line": 49, "column": 26}}, {"start": {"line": 49, "column": 26}, "end": {"line": 49, "column": 59}}]}, "4": {"loc": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": null}}, "type": "if", "locations": [{"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": null}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 59, "column": 25}, "end": {"line": 59, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 59, "column": 43}, "end": {"line": 59, "column": 48}}, {"start": {"line": 59, "column": 48}, "end": {"line": 59, "column": null}}]}, "6": {"loc": {"start": {"line": 60, "column": 13}, "end": {"line": 60, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 60, "column": 39}, "end": {"line": 60, "column": 52}}, {"start": {"line": 60, "column": 52}, "end": {"line": 60, "column": null}}]}, "7": {"loc": {"start": {"line": 65, "column": 4}, "end": {"line": 70, "column": null}}, "type": "if", "locations": [{"start": {"line": 65, "column": 4}, "end": {"line": 70, "column": null}}, {"start": {"line": 67, "column": 11}, "end": {"line": 70, "column": null}}]}, "8": {"loc": {"start": {"line": 66, "column": 23}, "end": {"line": 66, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 66, "column": 49}, "end": {"line": 66, "column": 58}}, {"start": {"line": 66, "column": 58}, "end": {"line": 66, "column": null}}]}, "9": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 79, "column": null}}, "type": "if", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 79, "column": null}}, {"start": {"line": 77, "column": 11}, "end": {"line": 79, "column": null}}]}, "10": {"loc": {"start": {"line": 84, "column": 4}, "end": {"line": 88, "column": null}}, "type": "if", "locations": [{"start": {"line": 84, "column": 4}, "end": {"line": 88, "column": null}}, {"start": {"line": 86, "column": 11}, "end": {"line": 88, "column": null}}]}, "11": {"loc": {"start": {"line": 92, "column": 4}, "end": {"line": 94, "column": null}}, "type": "if", "locations": [{"start": {"line": 92, "column": 4}, "end": {"line": 94, "column": null}}, {"start": {}, "end": {}}]}, "12": {"loc": {"start": {"line": 95, "column": 11}, "end": {"line": 95, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 95, "column": 37}, "end": {"line": 95, "column": 43}}, {"start": {"line": 95, "column": 43}, "end": {"line": 95, "column": null}}]}, "13": {"loc": {"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": null}}, "type": "if", "locations": [{"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": null}}, {"start": {}, "end": {}}]}, "14": {"loc": {"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": null}}, "type": "if", "locations": [{"start": {"line": 100, "column": 4}, "end": {"line": 100, "column": null}}, {"start": {}, "end": {}}]}, "15": {"loc": {"start": {"line": 115, "column": 11}, "end": {"line": 115, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 115, "column": 11}, "end": {"line": 115, "column": 25}}, {"start": {"line": 115, "column": 29}, "end": {"line": 115, "column": null}}]}, "16": {"loc": {"start": {"line": 118, "column": 2}, "end": {"line": 139, "column": null}}, "type": "if", "locations": [{"start": {"line": 118, "column": 2}, "end": {"line": 139, "column": null}}, {"start": {}, "end": {}}]}, "17": {"loc": {"start": {"line": 149, "column": 11}, "end": {"line": 149, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 149, "column": 11}, "end": {"line": 149, "column": null}}]}, "18": {"loc": {"start": {"line": 214, "column": 10}, "end": {"line": 214, "column": 26}}, "type": "binary-expr", "locations": [{"start": {"line": 214, "column": 10}, "end": {"line": 214, "column": 26}}, {"start": {"line": 214, "column": 26}, "end": {"line": 214, "column": 43}}, {"start": {"line": 214, "column": 43}, "end": {"line": 214, "column": 54}}]}, "19": {"loc": {"start": {"line": 238, "column": 27}, "end": {"line": 238, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 238, "column": 27}, "end": {"line": 238, "column": 91}}, {"start": {"line": 238, "column": 95}, "end": {"line": 238, "column": null}}]}, "20": {"loc": {"start": {"line": 310, "column": 18}, "end": {"line": 310, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 310, "column": 59}, "end": {"line": 310, "column": 74}}, {"start": {"line": 310, "column": 74}, "end": {"line": 310, "column": null}}]}, "21": {"loc": {"start": {"line": 325, "column": 19}, "end": {"line": 325, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 325, "column": 19}, "end": {"line": 325, "column": 41}}]}, "22": {"loc": {"start": {"line": 361, "column": 22}, "end": {"line": 363, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 362, "column": 26}, "end": {"line": 362, "column": null}}, {"start": {"line": 363, "column": 26}, "end": {"line": 363, "column": null}}]}, "23": {"loc": {"start": {"line": 366, "column": 21}, "end": {"line": 366, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 366, "column": 43}, "end": {"line": 366, "column": 53}}, {"start": {"line": 366, "column": 53}, "end": {"line": 366, "column": null}}]}, "24": {"loc": {"start": {"line": 398, "column": 7}, "end": {"line": 398, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 398, "column": 7}, "end": {"line": 398, "column": null}}]}, "25": {"loc": {"start": {"line": 402, "column": 15}, "end": {"line": 402, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 402, "column": 42}, "end": {"line": 402, "column": 77}}, {"start": {"line": 402, "column": 77}, "end": {"line": 402, "column": null}}]}, "26": {"loc": {"start": {"line": 405, "column": 15}, "end": {"line": 407, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 406, "column": 18}, "end": {"line": 406, "column": null}}, {"start": {"line": 407, "column": 18}, "end": {"line": 407, "column": null}}]}, "27": {"loc": {"start": {"line": 415, "column": 7}, "end": {"line": 415, "column": 21}}, "type": "binary-expr", "locations": [{"start": {"line": 415, "column": 7}, "end": {"line": 415, "column": 21}}, {"start": {"line": 415, "column": 21}, "end": {"line": 415, "column": null}}]}}, "s": {"0": 20, "1": 1, "2": 1, "3": 25, "4": 25, "5": 25, "6": 25, "7": 25, "8": 25, "9": 25, "10": 20, "11": 57, "12": 25, "13": 20, "14": 57, "15": 25, "16": 20, "17": 57, "18": 25, "19": 22, "20": 0, "21": 22, "22": 63, "23": 22, "24": 80, "25": 80, "26": 80, "27": 0, "28": 80, "29": 80, "30": 25, "31": 1, "32": 1, "33": 0, "34": 0, "35": 25, "36": 2, "37": 2, "38": 0, "39": 2, "40": 2, "41": 25, "42": 1, "43": 0, "44": 1, "45": 3, "46": 25, "47": 144, "48": 120, "49": 24, "50": 25, "51": 68, "52": 0, "53": 68, "54": 0, "55": 68, "56": 25, "57": 68, "58": 68, "59": 25, "60": 5, "61": 0, "62": 1, "63": 46, "64": 0, "65": 46, "66": 0, "67": 46, "68": 0, "69": 0, "70": 0, "71": 1, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 68, "78": 2, "79": 1, "80": 1, "81": 1, "82": 0, "83": 0, "84": 1}, "f": {"0": 25, "1": 20, "2": 57, "3": 20, "4": 57, "5": 20, "6": 57, "7": 22, "8": 63, "9": 80, "10": 1, "11": 2, "12": 1, "13": 3, "14": 144, "15": 68, "16": 68, "17": 5, "18": 0, "19": 1, "20": 46, "21": 0, "22": 46, "23": 0, "24": 46, "25": 0, "26": 1, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 68, "33": 2, "34": 1, "35": 1, "36": 1, "37": 0, "38": 0}, "b": {"0": [0], "1": [25], "2": [0, 22], "3": [63, 3, 62, 0, 62, 0], "4": [0, 80], "5": [59, 21], "6": [77, 3], "7": [1, 0], "8": [1, 0], "9": [0, 2], "10": [0, 1], "11": [120, 24], "12": [23, 1], "13": [0, 68], "14": [0, 68], "15": [68, 68], "16": [1, 24], "17": [24], "18": [24, 23, 23], "19": [24, 2], "20": [5, 63], "21": [68], "22": [46, 22], "23": [46, 22], "24": [24], "25": [1, 0], "26": [1, 0], "27": [24, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\permissions\\PermissionMatrix.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\permissions\\PermissionMatrix.tsx", "statementMap": {"0": {"start": {"line": 309, "column": 0}, "end": {"line": 309, "column": 15}}, "1": {"start": {"line": 3, "column": 41}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 10, "column": 76}, "end": {"line": 307, "column": null}}, "3": {"start": {"line": 19, "column": 54}, "end": {"line": 19, "column": null}}, "4": {"start": {"line": 20, "column": 48}, "end": {"line": 20, "column": null}}, "5": {"start": {"line": 23, "column": 21}, "end": {"line": 25, "column": null}}, "6": {"start": {"line": 24, "column": 4}, "end": {"line": 24, "column": null}}, "7": {"start": {"line": 28, "column": 27}, "end": {"line": 40, "column": null}}, "8": {"start": {"line": 29, "column": 4}, "end": {"line": 39, "column": null}}, "9": {"start": {"line": 30, "column": 6}, "end": {"line": 30, "column": null}}, "10": {"start": {"line": 32, "column": 26}, "end": {"line": 32, "column": null}}, "11": {"start": {"line": 33, "column": 6}, "end": {"line": 37, "column": null}}, "12": {"start": {"line": 34, "column": 8}, "end": {"line": 34, "column": null}}, "13": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": null}}, "14": {"start": {"line": 38, "column": 6}, "end": {"line": 38, "column": null}}, "15": {"start": {"line": 43, "column": 25}, "end": {"line": 45, "column": null}}, "16": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": null}}, "17": {"start": {"line": 48, "column": 32}, "end": {"line": 56, "column": null}}, "18": {"start": {"line": 49, "column": 24}, "end": {"line": 49, "column": null}}, "19": {"start": {"line": 50, "column": 4}, "end": {"line": 54, "column": null}}, "20": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": null}}, "21": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": null}}, "22": {"start": {"line": 55, "column": 4}, "end": {"line": 55, "column": null}}, "23": {"start": {"line": 59, "column": 27}, "end": {"line": 61, "column": null}}, "24": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": null}}, "25": {"start": {"line": 64, "column": 36}, "end": {"line": 90, "column": null}}, "26": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": null}}, "27": {"start": {"line": 65, "column": 32}, "end": {"line": 65, "column": null}}, "28": {"start": {"line": 67, "column": 21}, "end": {"line": 67, "column": null}}, "29": {"start": {"line": 68, "column": 20}, "end": {"line": 68, "column": null}}, "30": {"start": {"line": 69, "column": 21}, "end": {"line": 69, "column": null}}, "31": {"start": {"line": 70, "column": 20}, "end": {"line": 70, "column": null}}, "32": {"start": {"line": 71, "column": 22}, "end": {"line": 71, "column": null}}, "33": {"start": {"line": 73, "column": 4}, "end": {"line": 79, "column": null}}, "34": {"start": {"line": 74, "column": 50}, "end": {"line": 74, "column": null}}, "35": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": null}}, "36": {"start": {"line": 76, "column": 6}, "end": {"line": 76, "column": null}}, "37": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": null}}, "38": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": null}}, "39": {"start": {"line": 81, "column": 58}, "end": {"line": 87, "column": null}}, "40": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": null}}, "41": {"start": {"line": 93, "column": 16}, "end": {"line": 102, "column": null}}, "42": {"start": {"line": 94, "column": 21}, "end": {"line": 94, "column": null}}, "43": {"start": {"line": 95, "column": 23}, "end": {"line": 95, "column": 119}}, "44": {"start": {"line": 97, "column": 4}, "end": {"line": 101, "column": null}}, "45": {"start": {"line": 104, "column": 2}, "end": {"line": 117, "column": null}}, "46": {"start": {"line": 111, "column": 14}, "end": {"line": 111, "column": 27}}, "47": {"start": {"line": 136, "column": 29}, "end": {"line": 136, "column": null}}, "48": {"start": {"line": 142, "column": 29}, "end": {"line": 142, "column": null}}, "49": {"start": {"line": 185, "column": 31}, "end": {"line": 185, "column": null}}, "50": {"start": {"line": 187, "column": 12}, "end": {"line": 188, "column": 32}}, "51": {"start": {"line": 192, "column": 33}, "end": {"line": 192, "column": null}}, "52": {"start": {"line": 222, "column": 24}, "end": {"line": 222, "column": 43}}, "53": {"start": {"line": 231, "column": 32}, "end": {"line": 231, "column": 50}}, "54": {"start": {"line": 237, "column": 54}, "end": {"line": 237, "column": null}}, "55": {"start": {"line": 238, "column": 57}, "end": {"line": 238, "column": null}}, "56": {"start": {"line": 239, "column": 60}, "end": {"line": 239, "column": null}}, "57": {"start": {"line": 241, "column": 38}, "end": {"line": 243, "column": null}}, "58": {"start": {"line": 258, "column": 62}, "end": {"line": 258, "column": null}}, "59": {"start": {"line": 309, "column": 15}, "end": {"line": 309, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 10, "column": 76}, "end": {"line": 10, "column": 77}}, "loc": {"start": {"line": 18, "column": 1}, "end": {"line": 307, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 23, "column": 21}, "end": {"line": 23, "column": 22}}, "loc": {"start": {"line": 23, "column": 71}, "end": {"line": 25, "column": null}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 28, "column": 27}, "end": {"line": 28, "column": 28}}, "loc": {"start": {"line": 28, "column": 28}, "end": {"line": 40, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 43, "column": 25}, "end": {"line": 43, "column": 26}}, "loc": {"start": {"line": 43, "column": 26}, "end": {"line": 45, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 48, "column": 32}, "end": {"line": 48, "column": 33}}, "loc": {"start": {"line": 48, "column": 33}, "end": {"line": 56, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 59, "column": 27}, "end": {"line": 59, "column": null}}, "loc": {"start": {"line": 59, "column": 27}, "end": {"line": 61, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 64, "column": 36}, "end": {"line": 64, "column": null}}, "loc": {"start": {"line": 64, "column": 36}, "end": {"line": 90, "column": null}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 73, "column": 21}, "end": {"line": 73, "column": null}}, "loc": {"start": {"line": 73, "column": 21}, "end": {"line": 79, "column": null}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 93, "column": 24}, "end": {"line": 93, "column": null}}, "loc": {"start": {"line": 93, "column": 24}, "end": {"line": 102, "column": 5}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 110, "column": 31}, "end": {"line": 110, "column": 32}}, "loc": {"start": {"line": 111, "column": 14}, "end": {"line": 111, "column": 27}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 136, "column": 23}, "end": {"line": 136, "column": 29}}, "loc": {"start": {"line": 136, "column": 29}, "end": {"line": 136, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 142, "column": 23}, "end": {"line": 142, "column": 29}}, "loc": {"start": {"line": 142, "column": 29}, "end": {"line": 142, "column": null}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 184, "column": 30}, "end": {"line": 184, "column": 31}}, "loc": {"start": {"line": 184, "column": 31}, "end": {"line": 302, "column": null}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 192, "column": 27}, "end": {"line": 192, "column": 33}}, "loc": {"start": {"line": 192, "column": 33}, "end": {"line": 192, "column": null}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 221, "column": 43}, "end": {"line": 221, "column": 44}}, "loc": {"start": {"line": 222, "column": 24}, "end": {"line": 222, "column": 43}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 230, "column": 50}, "end": {"line": 230, "column": 51}}, "loc": {"start": {"line": 231, "column": 32}, "end": {"line": 231, "column": 50}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 236, "column": 58}, "end": {"line": 236, "column": 59}}, "loc": {"start": {"line": 236, "column": 59}, "end": {"line": 289, "column": null}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 258, "column": 56}, "end": {"line": 258, "column": 62}}, "loc": {"start": {"line": 258, "column": 62}, "end": {"line": 258, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 27}}, "type": "default-arg", "locations": [{"start": {"line": 12, "column": 18}, "end": {"line": 12, "column": 27}}]}, "1": {"loc": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 18}}, "type": "default-arg", "locations": [{"start": {"line": 15, "column": 13}, "end": {"line": 15, "column": 18}}]}, "2": {"loc": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 16, "column": 12}, "end": {"line": 16, "column": 17}}]}, "3": {"loc": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 17, "column": 14}, "end": {"line": 17, "column": 16}}]}, "4": {"loc": {"start": {"line": 29, "column": 4}, "end": {"line": 39, "column": null}}, "type": "if", "locations": [{"start": {"line": 29, "column": 4}, "end": {"line": 39, "column": null}}, {"start": {"line": 31, "column": 11}, "end": {"line": 39, "column": null}}]}, "5": {"loc": {"start": {"line": 33, "column": 6}, "end": {"line": 37, "column": null}}, "type": "if", "locations": [{"start": {"line": 33, "column": 6}, "end": {"line": 37, "column": null}}, {"start": {"line": 35, "column": 13}, "end": {"line": 37, "column": null}}]}, "6": {"loc": {"start": {"line": 44, "column": 11}, "end": {"line": 44, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 44, "column": 26}, "end": {"line": 44, "column": 55}}, {"start": {"line": 44, "column": 55}, "end": {"line": 44, "column": null}}]}, "7": {"loc": {"start": {"line": 50, "column": 4}, "end": {"line": 54, "column": null}}, "type": "if", "locations": [{"start": {"line": 50, "column": 4}, "end": {"line": 54, "column": null}}, {"start": {"line": 52, "column": 11}, "end": {"line": 54, "column": null}}]}, "8": {"loc": {"start": {"line": 60, "column": 11}, "end": {"line": 60, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 60, "column": 26}, "end": {"line": 60, "column": 42}}, {"start": {"line": 60, "column": 42}, "end": {"line": 60, "column": null}}]}, "9": {"loc": {"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": null}}, "type": "if", "locations": [{"start": {"line": 65, "column": 4}, "end": {"line": 65, "column": null}}, {"start": {}, "end": {}}]}, "10": {"loc": {"start": {"line": 100, "column": 27}, "end": {"line": 100, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 100, "column": 44}, "end": {"line": 100, "column": 93}}, {"start": {"line": 100, "column": 93}, "end": {"line": 100, "column": null}}]}, "11": {"loc": {"start": {"line": 104, "column": 2}, "end": {"line": 117, "column": null}}, "type": "if", "locations": [{"start": {"line": 104, "column": 2}, "end": {"line": 117, "column": null}}, {"start": {}, "end": {}}]}, "12": {"loc": {"start": {"line": 126, "column": 13}, "end": {"line": 126, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 126, "column": 13}, "end": {"line": 126, "column": null}}]}, "13": {"loc": {"start": {"line": 171, "column": 9}, "end": {"line": 171, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 171, "column": 9}, "end": {"line": 171, "column": null}}]}, "14": {"loc": {"start": {"line": 197, "column": 67}, "end": {"line": 197, "column": 98}}, "type": "cond-expr", "locations": [{"start": {"line": 197, "column": 80}, "end": {"line": 197, "column": 94}}, {"start": {"line": 197, "column": 94}, "end": {"line": 197, "column": 98}}]}, "15": {"loc": {"start": {"line": 218, "column": 17}, "end": {"line": 218, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 218, "column": 17}, "end": {"line": 218, "column": null}}]}, "16": {"loc": {"start": {"line": 239, "column": 60}, "end": {"line": 239, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 239, "column": 60}, "end": {"line": 239, "column": 114}}, {"start": {"line": 239, "column": 118}, "end": {"line": 239, "column": null}}]}, "17": {"loc": {"start": {"line": 245, "column": 44}, "end": {"line": 251, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 246, "column": 48}, "end": {"line": 248, "column": null}}, {"start": {"line": 249, "column": 48}, "end": {"line": 251, "column": null}}]}, "18": {"loc": {"start": {"line": 246, "column": 48}, "end": {"line": 248, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 247, "column": 50}, "end": {"line": 247, "column": null}}, {"start": {"line": 248, "column": 50}, "end": {"line": 248, "column": null}}]}, "19": {"loc": {"start": {"line": 249, "column": 48}, "end": {"line": 251, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 250, "column": 48}, "end": {"line": 250, "column": null}}, {"start": {"line": 251, "column": 48}, "end": {"line": 251, "column": null}}]}, "20": {"loc": {"start": {"line": 255, "column": 44}, "end": {"line": 262, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 255, "column": 44}, "end": {"line": 262, "column": 45}}]}, "21": {"loc": {"start": {"line": 264, "column": 48}, "end": {"line": 266, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 265, "column": 52}, "end": {"line": 265, "column": null}}, {"start": {"line": 266, "column": 52}, "end": {"line": 266, "column": null}}]}, "22": {"loc": {"start": {"line": 269, "column": 47}, "end": {"line": 269, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 269, "column": 47}, "end": {"line": 269, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0], "13": [0], "14": [0, 0], "15": [0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0], "21": [0, 0], "22": [0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\roles\\RoleForm.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\roles\\RoleForm.tsx", "statementMap": {"0": {"start": {"line": 393, "column": 0}, "end": {"line": 393, "column": 15}}, "1": {"start": {"line": 3, "column": 43}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 15, "column": 42}, "end": {"line": 391, "column": null}}, "3": {"start": {"line": 22, "column": 36}, "end": {"line": 28, "column": null}}, "4": {"start": {"line": 30, "column": 30}, "end": {"line": 30, "column": null}}, "5": {"start": {"line": 31, "column": 32}, "end": {"line": 31, "column": null}}, "6": {"start": {"line": 32, "column": 42}, "end": {"line": 32, "column": null}}, "7": {"start": {"line": 35, "column": 2}, "end": {"line": 45, "column": null}}, "8": {"start": {"line": 36, "column": 4}, "end": {"line": 44, "column": null}}, "9": {"start": {"line": 37, "column": 6}, "end": {"line": 43, "column": null}}, "10": {"start": {"line": 42, "column": 64}, "end": {"line": 42, "column": 68}}, "11": {"start": {"line": 48, "column": 24}, "end": {"line": 80, "column": null}}, "12": {"start": {"line": 49, "column": 4}, "end": {"line": 79, "column": null}}, "13": {"start": {"line": 51, "column": 8}, "end": {"line": 53, "column": null}}, "14": {"start": {"line": 52, "column": 10}, "end": {"line": 52, "column": null}}, "15": {"start": {"line": 54, "column": 8}, "end": {"line": 56, "column": null}}, "16": {"start": {"line": 55, "column": 10}, "end": {"line": 55, "column": null}}, "17": {"start": {"line": 57, "column": 8}, "end": {"line": 59, "column": null}}, "18": {"start": {"line": 58, "column": 10}, "end": {"line": 58, "column": null}}, "19": {"start": {"line": 60, "column": 8}, "end": {"line": 60, "column": null}}, "20": {"start": {"line": 63, "column": 8}, "end": {"line": 65, "column": null}}, "21": {"start": {"line": 64, "column": 10}, "end": {"line": 64, "column": null}}, "22": {"start": {"line": 66, "column": 8}, "end": {"line": 66, "column": null}}, "23": {"start": {"line": 69, "column": 8}, "end": {"line": 71, "column": null}}, "24": {"start": {"line": 70, "column": 10}, "end": {"line": 70, "column": null}}, "25": {"start": {"line": 72, "column": 8}, "end": {"line": 74, "column": null}}, "26": {"start": {"line": 73, "column": 10}, "end": {"line": 73, "column": null}}, "27": {"start": {"line": 75, "column": 8}, "end": {"line": 75, "column": null}}, "28": {"start": {"line": 78, "column": 8}, "end": {"line": 78, "column": null}}, "29": {"start": {"line": 82, "column": 23}, "end": {"line": 98, "column": null}}, "30": {"start": {"line": 83, "column": 46}, "end": {"line": 83, "column": null}}, "31": {"start": {"line": 85, "column": 4}, "end": {"line": 85, "column": null}}, "32": {"start": {"line": 86, "column": 4}, "end": {"line": 86, "column": null}}, "33": {"start": {"line": 87, "column": 4}, "end": {"line": 87, "column": null}}, "34": {"start": {"line": 90, "column": 4}, "end": {"line": 94, "column": null}}, "35": {"start": {"line": 91, "column": 6}, "end": {"line": 93, "column": null}}, "36": {"start": {"line": 92, "column": 8}, "end": {"line": 92, "column": null}}, "37": {"start": {"line": 96, "column": 4}, "end": {"line": 96, "column": null}}, "38": {"start": {"line": 97, "column": 4}, "end": {"line": 97, "column": null}}, "39": {"start": {"line": 100, "column": 28}, "end": {"line": 119, "column": null}}, "40": {"start": {"line": 101, "column": 4}, "end": {"line": 104, "column": null}}, "41": {"start": {"line": 101, "column": 26}, "end": {"line": 104, "column": null}}, "42": {"start": {"line": 107, "column": 4}, "end": {"line": 112, "column": null}}, "43": {"start": {"line": 108, "column": 6}, "end": {"line": 111, "column": null}}, "44": {"start": {"line": 108, "column": 25}, "end": {"line": 111, "column": null}}, "45": {"start": {"line": 115, "column": 4}, "end": {"line": 118, "column": null}}, "46": {"start": {"line": 115, "column": 24}, "end": {"line": 118, "column": null}}, "47": {"start": {"line": 121, "column": 21}, "end": {"line": 132, "column": null}}, "48": {"start": {"line": 122, "column": 4}, "end": {"line": 125, "column": null}}, "49": {"start": {"line": 122, "column": 24}, "end": {"line": 125, "column": null}}, "50": {"start": {"line": 127, "column": 18}, "end": {"line": 127, "column": null}}, "51": {"start": {"line": 128, "column": 4}, "end": {"line": 131, "column": null}}, "52": {"start": {"line": 128, "column": 23}, "end": {"line": 131, "column": null}}, "53": {"start": {"line": 134, "column": 33}, "end": {"line": 146, "column": null}}, "54": {"start": {"line": 135, "column": 24}, "end": {"line": 135, "column": null}}, "55": {"start": {"line": 136, "column": 4}, "end": {"line": 140, "column": null}}, "56": {"start": {"line": 137, "column": 6}, "end": {"line": 137, "column": null}}, "57": {"start": {"line": 139, "column": 6}, "end": {"line": 139, "column": null}}, "58": {"start": {"line": 142, "column": 4}, "end": {"line": 145, "column": null}}, "59": {"start": {"line": 142, "column": 26}, "end": {"line": 145, "column": null}}, "60": {"start": {"line": 148, "column": 37}, "end": {"line": 160, "column": null}}, "61": {"start": {"line": 149, "column": 4}, "end": {"line": 159, "column": null}}, "62": {"start": {"line": 150, "column": 6}, "end": {"line": 153, "column": null}}, "63": {"start": {"line": 150, "column": 28}, "end": {"line": 153, "column": null}}, "64": {"start": {"line": 155, "column": 6}, "end": {"line": 158, "column": null}}, "65": {"start": {"line": 155, "column": 28}, "end": {"line": 158, "column": null}}, "66": {"start": {"line": 157, "column": 67}, "end": {"line": 157, "column": 71}}, "67": {"start": {"line": 162, "column": 23}, "end": {"line": 189, "column": null}}, "68": {"start": {"line": 163, "column": 4}, "end": {"line": 163, "column": null}}, "69": {"start": {"line": 165, "column": 4}, "end": {"line": 167, "column": null}}, "70": {"start": {"line": 166, "column": 6}, "end": {"line": 166, "column": null}}, "71": {"start": {"line": 169, "column": 4}, "end": {"line": 169, "column": null}}, "72": {"start": {"line": 171, "column": 4}, "end": {"line": 188, "column": null}}, "73": {"start": {"line": 172, "column": 58}, "end": {"line": 177, "column": null}}, "74": {"start": {"line": 179, "column": 6}, "end": {"line": 181, "column": null}}, "75": {"start": {"line": 180, "column": 9}, "end": {"line": 180, "column": null}}, "76": {"start": {"line": 183, "column": 6}, "end": {"line": 183, "column": null}}, "77": {"start": {"line": 185, "column": 6}, "end": {"line": 185, "column": null}}, "78": {"start": {"line": 187, "column": 6}, "end": {"line": 187, "column": null}}, "79": {"start": {"line": 191, "column": 24}, "end": {"line": 193, "column": null}}, "80": {"start": {"line": 192, "column": 4}, "end": {"line": 192, "column": null}}, "81": {"start": {"line": 195, "column": 22}, "end": {"line": 195, "column": null}}, "82": {"start": {"line": 217, "column": 31}, "end": {"line": 217, "column": null}}, "83": {"start": {"line": 218, "column": 28}, "end": {"line": 218, "column": null}}, "84": {"start": {"line": 243, "column": 31}, "end": {"line": 243, "column": null}}, "85": {"start": {"line": 244, "column": 28}, "end": {"line": 244, "column": null}}, "86": {"start": {"line": 270, "column": 29}, "end": {"line": 270, "column": null}}, "87": {"start": {"line": 271, "column": 26}, "end": {"line": 271, "column": null}}, "88": {"start": {"line": 292, "column": 33}, "end": {"line": 292, "column": null}}, "89": {"start": {"line": 328, "column": 18}, "end": {"line": 329, "column": null}}, "90": {"start": {"line": 335, "column": 38}, "end": {"line": 335, "column": null}}, "91": {"start": {"line": 393, "column": 15}, "end": {"line": 393, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 15, "column": 42}, "end": {"line": 15, "column": 43}}, "loc": {"start": {"line": 21, "column": 1}, "end": {"line": 391, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 35, "column": 12}, "end": {"line": 35, "column": null}}, "loc": {"start": {"line": 35, "column": 12}, "end": {"line": 45, "column": 5}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 42, "column": 59}, "end": {"line": 42, "column": 64}}, "loc": {"start": {"line": 42, "column": 64}, "end": {"line": 42, "column": 68}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 48, "column": 24}, "end": {"line": 48, "column": 25}}, "loc": {"start": {"line": 48, "column": 39}, "end": {"line": 80, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 82, "column": 23}, "end": {"line": 82, "column": null}}, "loc": {"start": {"line": 82, "column": 23}, "end": {"line": 98, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 90, "column": 35}, "end": {"line": 90, "column": null}}, "loc": {"start": {"line": 90, "column": 35}, "end": {"line": 94, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 100, "column": 28}, "end": {"line": 100, "column": 29}}, "loc": {"start": {"line": 100, "column": 43}, "end": {"line": 119, "column": null}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 101, "column": 17}, "end": {"line": 101, "column": 26}}, "loc": {"start": {"line": 101, "column": 26}, "end": {"line": 104, "column": null}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 108, "column": 16}, "end": {"line": 108, "column": 25}}, "loc": {"start": {"line": 108, "column": 25}, "end": {"line": 111, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 115, "column": 15}, "end": {"line": 115, "column": 24}}, "loc": {"start": {"line": 115, "column": 24}, "end": {"line": 118, "column": null}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 121, "column": 21}, "end": {"line": 121, "column": 22}}, "loc": {"start": {"line": 121, "column": 22}, "end": {"line": 132, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 122, "column": 15}, "end": {"line": 122, "column": 24}}, "loc": {"start": {"line": 122, "column": 24}, "end": {"line": 125, "column": null}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 128, "column": 14}, "end": {"line": 128, "column": 23}}, "loc": {"start": {"line": 128, "column": 23}, "end": {"line": 131, "column": null}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 134, "column": 33}, "end": {"line": 134, "column": 34}}, "loc": {"start": {"line": 134, "column": 34}, "end": {"line": 146, "column": null}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 142, "column": 17}, "end": {"line": 142, "column": 26}}, "loc": {"start": {"line": 142, "column": 26}, "end": {"line": 145, "column": null}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 148, "column": 37}, "end": {"line": 148, "column": null}}, "loc": {"start": {"line": 148, "column": 37}, "end": {"line": 160, "column": null}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 150, "column": 19}, "end": {"line": 150, "column": 28}}, "loc": {"start": {"line": 150, "column": 28}, "end": {"line": 153, "column": null}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 155, "column": 19}, "end": {"line": 155, "column": 28}}, "loc": {"start": {"line": 155, "column": 28}, "end": {"line": 158, "column": null}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 157, "column": 62}, "end": {"line": 157, "column": 67}}, "loc": {"start": {"line": 157, "column": 67}, "end": {"line": 157, "column": 71}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 162, "column": 23}, "end": {"line": 162, "column": 30}}, "loc": {"start": {"line": 162, "column": 30}, "end": {"line": 189, "column": null}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 191, "column": 24}, "end": {"line": 191, "column": 25}}, "loc": {"start": {"line": 191, "column": 25}, "end": {"line": 193, "column": null}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 217, "column": 24}, "end": {"line": 217, "column": 25}}, "loc": {"start": {"line": 217, "column": 31}, "end": {"line": 217, "column": null}}}, "22": {"name": "(anonymous_26)", "decl": {"start": {"line": 218, "column": 22}, "end": {"line": 218, "column": 28}}, "loc": {"start": {"line": 218, "column": 28}, "end": {"line": 218, "column": null}}}, "23": {"name": "(anonymous_27)", "decl": {"start": {"line": 243, "column": 24}, "end": {"line": 243, "column": 25}}, "loc": {"start": {"line": 243, "column": 31}, "end": {"line": 243, "column": null}}}, "24": {"name": "(anonymous_28)", "decl": {"start": {"line": 244, "column": 22}, "end": {"line": 244, "column": 28}}, "loc": {"start": {"line": 244, "column": 28}, "end": {"line": 244, "column": null}}}, "25": {"name": "(anonymous_29)", "decl": {"start": {"line": 270, "column": 22}, "end": {"line": 270, "column": 23}}, "loc": {"start": {"line": 270, "column": 29}, "end": {"line": 270, "column": null}}}, "26": {"name": "(anonymous_30)", "decl": {"start": {"line": 271, "column": 20}, "end": {"line": 271, "column": 26}}, "loc": {"start": {"line": 271, "column": 26}, "end": {"line": 271, "column": null}}}, "27": {"name": "(anonymous_31)", "decl": {"start": {"line": 292, "column": 26}, "end": {"line": 292, "column": 27}}, "loc": {"start": {"line": 292, "column": 33}, "end": {"line": 292, "column": null}}}, "28": {"name": "(anonymous_32)", "decl": {"start": {"line": 327, "column": 42}, "end": {"line": 327, "column": 43}}, "loc": {"start": {"line": 328, "column": 18}, "end": {"line": 329, "column": null}}}, "29": {"name": "(anonymous_33)", "decl": {"start": {"line": 335, "column": 32}, "end": {"line": 335, "column": 38}}, "loc": {"start": {"line": 335, "column": 38}, "end": {"line": 335, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 19}}, "type": "default-arg", "locations": [{"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 19}}]}, "1": {"loc": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 27}}, "type": "default-arg", "locations": [{"start": {"line": 20, "column": 25}, "end": {"line": 20, "column": 27}}]}, "2": {"loc": {"start": {"line": 36, "column": 4}, "end": {"line": 44, "column": null}}, "type": "if", "locations": [{"start": {"line": 36, "column": 4}, "end": {"line": 44, "column": null}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 39, "column": 21}, "end": {"line": 39, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 39, "column": 21}, "end": {"line": 39, "column": 37}}, {"start": {"line": 39, "column": 41}, "end": {"line": 39, "column": null}}]}, "4": {"loc": {"start": {"line": 42, "column": 37}, "end": {"line": 42, "column": 75}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 37}, "end": {"line": 42, "column": 73}}, {"start": {"line": 42, "column": 73}, "end": {"line": 42, "column": 75}}]}, "5": {"loc": {"start": {"line": 49, "column": 4}, "end": {"line": 79, "column": null}}, "type": "switch", "locations": [{"start": {"line": 50, "column": 6}, "end": {"line": 60, "column": null}}, {"start": {"line": 62, "column": 6}, "end": {"line": 66, "column": null}}, {"start": {"line": 68, "column": 6}, "end": {"line": 75, "column": null}}, {"start": {"line": 77, "column": 6}, "end": {"line": 78, "column": null}}]}, "6": {"loc": {"start": {"line": 51, "column": 8}, "end": {"line": 53, "column": null}}, "type": "if", "locations": [{"start": {"line": 51, "column": 8}, "end": {"line": 53, "column": null}}, {"start": {}, "end": {}}]}, "7": {"loc": {"start": {"line": 51, "column": 12}, "end": {"line": 51, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 51, "column": 12}, "end": {"line": 51, "column": 22}}, {"start": {"line": 51, "column": 22}, "end": {"line": 51, "column": 49}}]}, "8": {"loc": {"start": {"line": 54, "column": 8}, "end": {"line": 56, "column": null}}, "type": "if", "locations": [{"start": {"line": 54, "column": 8}, "end": {"line": 56, "column": null}}, {"start": {}, "end": {}}]}, "9": {"loc": {"start": {"line": 57, "column": 8}, "end": {"line": 59, "column": null}}, "type": "if", "locations": [{"start": {"line": 57, "column": 8}, "end": {"line": 59, "column": null}}, {"start": {}, "end": {}}]}, "10": {"loc": {"start": {"line": 63, "column": 8}, "end": {"line": 65, "column": null}}, "type": "if", "locations": [{"start": {"line": 63, "column": 8}, "end": {"line": 65, "column": null}}, {"start": {}, "end": {}}]}, "11": {"loc": {"start": {"line": 63, "column": 12}, "end": {"line": 63, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 63, "column": 12}, "end": {"line": 63, "column": 21}}, {"start": {"line": 63, "column": 21}, "end": {"line": 63, "column": 41}}]}, "12": {"loc": {"start": {"line": 69, "column": 8}, "end": {"line": 71, "column": null}}, "type": "if", "locations": [{"start": {"line": 69, "column": 8}, "end": {"line": 71, "column": null}}, {"start": {}, "end": {}}]}, "13": {"loc": {"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 33}}, "type": "binary-expr", "locations": [{"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 22}}, {"start": {"line": 69, "column": 22}, "end": {"line": 69, "column": 33}}]}, "14": {"loc": {"start": {"line": 72, "column": 8}, "end": {"line": 74, "column": null}}, "type": "if", "locations": [{"start": {"line": 72, "column": 8}, "end": {"line": 74, "column": null}}, {"start": {}, "end": {}}]}, "15": {"loc": {"start": {"line": 91, "column": 6}, "end": {"line": 93, "column": null}}, "type": "if", "locations": [{"start": {"line": 91, "column": 6}, "end": {"line": 93, "column": null}}, {"start": {}, "end": {}}]}, "16": {"loc": {"start": {"line": 107, "column": 4}, "end": {"line": 112, "column": null}}, "type": "if", "locations": [{"start": {"line": 107, "column": 4}, "end": {"line": 112, "column": null}}, {"start": {}, "end": {}}]}, "17": {"loc": {"start": {"line": 136, "column": 4}, "end": {"line": 140, "column": null}}, "type": "if", "locations": [{"start": {"line": 136, "column": 4}, "end": {"line": 140, "column": null}}, {"start": {"line": 138, "column": 11}, "end": {"line": 140, "column": null}}]}, "18": {"loc": {"start": {"line": 149, "column": 4}, "end": {"line": 159, "column": null}}, "type": "if", "locations": [{"start": {"line": 149, "column": 4}, "end": {"line": 159, "column": null}}, {"start": {"line": 154, "column": 11}, "end": {"line": 159, "column": null}}]}, "19": {"loc": {"start": {"line": 165, "column": 4}, "end": {"line": 167, "column": null}}, "type": "if", "locations": [{"start": {"line": 165, "column": 4}, "end": {"line": 167, "column": null}}, {"start": {}, "end": {}}]}, "20": {"loc": {"start": {"line": 174, "column": 21}, "end": {"line": 174, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 174, "column": 21}, "end": {"line": 174, "column": 53}}, {"start": {"line": 174, "column": 53}, "end": {"line": 174, "column": null}}]}, "21": {"loc": {"start": {"line": 179, "column": 6}, "end": {"line": 181, "column": null}}, "type": "if", "locations": [{"start": {"line": 179, "column": 6}, "end": {"line": 181, "column": null}}, {"start": {}, "end": {}}]}, "22": {"loc": {"start": {"line": 192, "column": 11}, "end": {"line": 192, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 192, "column": 32}, "end": {"line": 192, "column": 58}}, {"start": {"line": 192, "column": 58}, "end": {"line": 192, "column": null}}]}, "23": {"loc": {"start": {"line": 192, "column": 32}, "end": {"line": 192, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 192, "column": 32}, "end": {"line": 192, "column": 49}}, {"start": {"line": 192, "column": 53}, "end": {"line": 192, "column": 58}}]}, "24": {"loc": {"start": {"line": 195, "column": 22}, "end": {"line": 195, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 195, "column": 22}, "end": {"line": 195, "column": 58}}, {"start": {"line": 195, "column": 58}, "end": {"line": 195, "column": null}}]}, "25": {"loc": {"start": {"line": 201, "column": 11}, "end": {"line": 201, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 201, "column": 18}, "end": {"line": 201, "column": 32}}, {"start": {"line": 201, "column": 32}, "end": {"line": 201, "column": null}}]}, "26": {"loc": {"start": {"line": 220, "column": 16}, "end": {"line": 222, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 221, "column": 20}, "end": {"line": 221, "column": null}}, {"start": {"line": 222, "column": 20}, "end": {"line": 222, "column": null}}]}, "27": {"loc": {"start": {"line": 225, "column": 24}, "end": {"line": 225, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 225, "column": 24}, "end": {"line": 225, "column": 37}}, {"start": {"line": 225, "column": 37}, "end": {"line": 225, "column": null}}]}, "28": {"loc": {"start": {"line": 227, "column": 13}, "end": {"line": 227, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 227, "column": 13}, "end": {"line": 227, "column": null}}]}, "29": {"loc": {"start": {"line": 243, "column": 62}, "end": {"line": 243, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 243, "column": 62}, "end": {"line": 243, "column": 90}}, {"start": {"line": 243, "column": 90}, "end": {"line": 243, "column": null}}]}, "30": {"loc": {"start": {"line": 246, "column": 16}, "end": {"line": 248, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 247, "column": 20}, "end": {"line": 247, "column": null}}, {"start": {"line": 248, "column": 20}, "end": {"line": 248, "column": null}}]}, "31": {"loc": {"start": {"line": 250, "column": 24}, "end": {"line": 250, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 250, "column": 24}, "end": {"line": 250, "column": 37}}, {"start": {"line": 250, "column": 37}, "end": {"line": 250, "column": null}}]}, "32": {"loc": {"start": {"line": 252, "column": 13}, "end": {"line": 252, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 252, "column": 13}, "end": {"line": 252, "column": null}}]}, "33": {"loc": {"start": {"line": 273, "column": 14}, "end": {"line": 275, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 274, "column": 18}, "end": {"line": 274, "column": null}}, {"start": {"line": 275, "column": 18}, "end": {"line": 275, "column": null}}]}, "34": {"loc": {"start": {"line": 278, "column": 22}, "end": {"line": 278, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 278, "column": 22}, "end": {"line": 278, "column": 35}}, {"start": {"line": 278, "column": 35}, "end": {"line": 278, "column": null}}]}, "35": {"loc": {"start": {"line": 280, "column": 11}, "end": {"line": 280, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 280, "column": 11}, "end": {"line": 280, "column": null}}]}, "36": {"loc": {"start": {"line": 286, "column": 9}, "end": {"line": 286, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 286, "column": 9}, "end": {"line": 286, "column": null}}]}, "37": {"loc": {"start": {"line": 294, "column": 26}, "end": {"line": 294, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 294, "column": 26}, "end": {"line": 294, "column": 39}}, {"start": {"line": 294, "column": 39}, "end": {"line": 294, "column": null}}]}, "38": {"loc": {"start": {"line": 307, "column": 9}, "end": {"line": 307, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 307, "column": 9}, "end": {"line": 307, "column": null}}]}, "39": {"loc": {"start": {"line": 317, "column": 26}, "end": {"line": 317, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 317, "column": 26}, "end": {"line": 317, "column": 39}}, {"start": {"line": 317, "column": 39}, "end": {"line": 317, "column": null}}]}, "40": {"loc": {"start": {"line": 319, "column": 17}, "end": {"line": 321, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 320, "column": 20}, "end": {"line": 320, "column": null}}, {"start": {"line": 321, "column": 20}, "end": {"line": 321, "column": null}}]}, "41": {"loc": {"start": {"line": 337, "column": 32}, "end": {"line": 337, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 337, "column": 32}, "end": {"line": 337, "column": 45}}, {"start": {"line": 337, "column": 45}, "end": {"line": 337, "column": null}}]}, "42": {"loc": {"start": {"line": 343, "column": 23}, "end": {"line": 343, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 343, "column": 23}, "end": {"line": 343, "column": 45}}]}, "43": {"loc": {"start": {"line": 369, "column": 22}, "end": {"line": 369, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 369, "column": 22}, "end": {"line": 369, "column": 35}}, {"start": {"line": 369, "column": 35}, "end": {"line": 369, "column": null}}]}, "44": {"loc": {"start": {"line": 375, "column": 22}, "end": {"line": 375, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 375, "column": 22}, "end": {"line": 375, "column": 38}}, {"start": {"line": 375, "column": 38}, "end": {"line": 375, "column": 51}}, {"start": {"line": 375, "column": 51}, "end": {"line": 375, "column": null}}]}, "45": {"loc": {"start": {"line": 378, "column": 13}, "end": {"line": 384, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 379, "column": 14}, "end": {"line": 384, "column": 21}}, {"start": {"line": 384, "column": 14}, "end": {"line": 384, "column": null}}]}, "46": {"loc": {"start": {"line": 381, "column": 17}, "end": {"line": 381, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 381, "column": 24}, "end": {"line": 381, "column": 41}}, {"start": {"line": 381, "column": 41}, "end": {"line": 381, "column": null}}]}, "47": {"loc": {"start": {"line": 384, "column": 14}, "end": {"line": 384, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 384, "column": 21}, "end": {"line": 384, "column": 42}}, {"start": {"line": 384, "column": 42}, "end": {"line": 384, "column": null}}]}}, "s": {"0": 16, "1": 1, "2": 1, "3": 89, "4": 89, "5": 89, "6": 89, "7": 89, "8": 16, "9": 3, "10": 6, "11": 89, "12": 6, "13": 4, "14": 0, "15": 4, "16": 0, "17": 4, "18": 0, "19": 4, "20": 1, "21": 0, "22": 1, "23": 1, "24": 0, "25": 1, "26": 1, "27": 0, "28": 0, "29": 89, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 89, "40": 61, "41": 61, "42": 61, "43": 0, "44": 0, "45": 61, "46": 61, "47": 89, "48": 6, "49": 6, "50": 6, "51": 6, "52": 6, "53": 89, "54": 3, "55": 3, "56": 1, "57": 2, "58": 3, "59": 3, "60": 89, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 89, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 89, "80": 536, "81": 89, "82": 41, "83": 4, "84": 4, "85": 1, "86": 16, "87": 1, "88": 0, "89": 178, "90": 3, "91": 1}, "f": {"0": 89, "1": 16, "2": 6, "3": 6, "4": 0, "5": 0, "6": 61, "7": 61, "8": 0, "9": 61, "10": 6, "11": 6, "12": 6, "13": 3, "14": 3, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 536, "21": 41, "22": 4, "23": 4, "24": 1, "25": 16, "26": 1, "27": 0, "28": 178, "29": 3}, "b": {"0": [89], "1": [0], "2": [3, 13], "3": [3, 0], "4": [3, 0], "5": [4, 1, 1, 0], "6": [0, 4], "7": [4, 4], "8": [0, 4], "9": [0, 4], "10": [0, 1], "11": [1, 1], "12": [0, 1], "13": [1, 1], "14": [1, 0], "15": [0, 0], "16": [0, 61], "17": [1, 2], "18": [0, 0], "19": [0, 0], "20": [0, 0], "21": [0, 0], "22": [188, 348], "23": [188, 182], "24": [89, 63], "25": [19, 70], "26": [0, 89], "27": [89, 89], "28": [89], "29": [4, 0], "30": [2, 87], "31": [89, 89], "32": [89], "33": [0, 89], "34": [89, 89], "35": [89], "36": [89], "37": [19, 19], "38": [89], "39": [89, 89], "40": [16, 73], "41": [178, 178], "42": [178], "43": [89, 89], "44": [89, 43, 43], "45": [0, 89], "46": [0, 0], "47": [19, 70]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\roles\\RoleHierarchy.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\roles\\RoleHierarchy.tsx", "statementMap": {"0": {"start": {"line": 248, "column": 0}, "end": {"line": 248, "column": 15}}, "1": {"start": {"line": 3, "column": 32}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 10, "column": 61}, "end": {"line": 246, "column": null}}, "3": {"start": {"line": 17, "column": 54}, "end": {"line": 17, "column": null}}, "4": {"start": {"line": 19, "column": 29}, "end": {"line": 31, "column": null}}, "5": {"start": {"line": 20, "column": 4}, "end": {"line": 30, "column": null}}, "6": {"start": {"line": 21, "column": 6}, "end": {"line": 21, "column": null}}, "7": {"start": {"line": 23, "column": 26}, "end": {"line": 23, "column": null}}, "8": {"start": {"line": 24, "column": 6}, "end": {"line": 28, "column": null}}, "9": {"start": {"line": 25, "column": 8}, "end": {"line": 25, "column": null}}, "10": {"start": {"line": 27, "column": 8}, "end": {"line": 27, "column": null}}, "11": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": null}}, "12": {"start": {"line": 33, "column": 21}, "end": {"line": 35, "column": null}}, "13": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": null}}, "14": {"start": {"line": 37, "column": 28}, "end": {"line": 50, "column": null}}, "15": {"start": {"line": 38, "column": 19}, "end": {"line": 48, "column": null}}, "16": {"start": {"line": 49, "column": 4}, "end": {"line": 49, "column": null}}, "17": {"start": {"line": 52, "column": 30}, "end": {"line": 151, "column": null}}, "18": {"start": {"line": 53, "column": 24}, "end": {"line": 53, "column": null}}, "19": {"start": {"line": 54, "column": 21}, "end": {"line": 54, "column": null}}, "20": {"start": {"line": 55, "column": 24}, "end": {"line": 55, "column": null}}, "21": {"start": {"line": 57, "column": 4}, "end": {"line": 58, "column": 30}}, "22": {"start": {"line": 68, "column": 29}, "end": {"line": 68, "column": null}}, "23": {"start": {"line": 90, "column": 27}, "end": {"line": 90, "column": null}}, "24": {"start": {"line": 145, "column": 14}, "end": {"line": 145, "column": null}}, "25": {"start": {"line": 153, "column": 2}, "end": {"line": 164, "column": null}}, "26": {"start": {"line": 174, "column": 35}, "end": {"line": 174, "column": null}}, "27": {"start": {"line": 175, "column": 39}, "end": {"line": 182, "column": null}}, "28": {"start": {"line": 176, "column": 18}, "end": {"line": 181, "column": null}}, "29": {"start": {"line": 177, "column": 20}, "end": {"line": 177, "column": null}}, "30": {"start": {"line": 178, "column": 20}, "end": {"line": 180, "column": null}}, "31": {"start": {"line": 179, "column": 22}, "end": {"line": 179, "column": null}}, "32": {"start": {"line": 183, "column": 16}, "end": {"line": 183, "column": null}}, "33": {"start": {"line": 185, "column": 16}, "end": {"line": 189, "column": null}}, "34": {"start": {"line": 186, "column": 18}, "end": {"line": 186, "column": null}}, "35": {"start": {"line": 186, "column": 43}, "end": {"line": 186, "column": null}}, "36": {"start": {"line": 188, "column": 18}, "end": {"line": 188, "column": null}}, "37": {"start": {"line": 197, "column": 16}, "end": {"line": 201, "column": null}}, "38": {"start": {"line": 200, "column": 18}, "end": {"line": 200, "column": null}}, "39": {"start": {"line": 241, "column": 35}, "end": {"line": 241, "column": null}}, "40": {"start": {"line": 248, "column": 15}, "end": {"line": 248, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 10, "column": 61}, "end": {"line": 10, "column": 62}}, "loc": {"start": {"line": 16, "column": 1}, "end": {"line": 246, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 19, "column": 29}, "end": {"line": 19, "column": 30}}, "loc": {"start": {"line": 19, "column": 30}, "end": {"line": 31, "column": null}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 33, "column": 21}, "end": {"line": 33, "column": 22}}, "loc": {"start": {"line": 33, "column": 22}, "end": {"line": 35, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 37, "column": 28}, "end": {"line": 37, "column": 29}}, "loc": {"start": {"line": 37, "column": 29}, "end": {"line": 50, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 52, "column": 30}, "end": {"line": 52, "column": 31}}, "loc": {"start": {"line": 52, "column": 73}, "end": {"line": 151, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 68, "column": 23}, "end": {"line": 68, "column": 29}}, "loc": {"start": {"line": 68, "column": 29}, "end": {"line": 68, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 90, "column": 21}, "end": {"line": 90, "column": 27}}, "loc": {"start": {"line": 90, "column": 27}, "end": {"line": 90, "column": null}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 144, "column": 31}, "end": {"line": 144, "column": 32}}, "loc": {"start": {"line": 145, "column": 14}, "end": {"line": 145, "column": null}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 173, "column": 23}, "end": {"line": 173, "column": null}}, "loc": {"start": {"line": 173, "column": 23}, "end": {"line": 190, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 175, "column": 39}, "end": {"line": 175, "column": 40}}, "loc": {"start": {"line": 175, "column": 40}, "end": {"line": 182, "column": null}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 176, "column": 32}, "end": {"line": 176, "column": null}}, "loc": {"start": {"line": 176, "column": 32}, "end": {"line": 181, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 186, "column": 37}, "end": {"line": 186, "column": 43}}, "loc": {"start": {"line": 186, "column": 43}, "end": {"line": 186, "column": null}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 196, "column": 23}, "end": {"line": 196, "column": null}}, "loc": {"start": {"line": 196, "column": 23}, "end": {"line": 202, "column": null}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 241, "column": 25}, "end": {"line": 241, "column": 26}}, "loc": {"start": {"line": 241, "column": 35}, "end": {"line": 241, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": 27}}, "type": "default-arg", "locations": [{"start": {"line": 13, "column": 18}, "end": {"line": 13, "column": 27}}]}, "1": {"loc": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 15, "column": 14}, "end": {"line": 15, "column": 16}}]}, "2": {"loc": {"start": {"line": 20, "column": 4}, "end": {"line": 30, "column": null}}, "type": "if", "locations": [{"start": {"line": 20, "column": 4}, "end": {"line": 30, "column": null}}, {"start": {"line": 22, "column": 11}, "end": {"line": 30, "column": null}}]}, "3": {"loc": {"start": {"line": 24, "column": 6}, "end": {"line": 28, "column": null}}, "type": "if", "locations": [{"start": {"line": 24, "column": 6}, "end": {"line": 28, "column": null}}, {"start": {"line": 26, "column": 13}, "end": {"line": 28, "column": null}}]}, "4": {"loc": {"start": {"line": 34, "column": 11}, "end": {"line": 34, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 34, "column": 28}, "end": {"line": 34, "column": 56}}, {"start": {"line": 34, "column": 56}, "end": {"line": 34, "column": null}}]}, "5": {"loc": {"start": {"line": 52, "column": 56}, "end": {"line": 52, "column": 73}}, "type": "default-arg", "locations": [{"start": {"line": 52, "column": 72}, "end": {"line": 52, "column": 73}}]}, "6": {"loc": {"start": {"line": 53, "column": 24}, "end": {"line": 53, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 53, "column": 24}, "end": {"line": 53, "column": 37}}, {"start": {"line": 53, "column": 41}, "end": {"line": 53, "column": null}}]}, "7": {"loc": {"start": {"line": 55, "column": 24}, "end": {"line": 55, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 55, "column": 36}, "end": {"line": 55, "column": 53}}, {"start": {"line": 55, "column": 56}, "end": {"line": 55, "column": null}}]}, "8": {"loc": {"start": {"line": 66, "column": 11}, "end": {"line": 66, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 66, "column": 11}, "end": {"line": 66, "column": null}}]}, "9": {"loc": {"start": {"line": 72, "column": 59}, "end": {"line": 72, "column": 88}}, "type": "cond-expr", "locations": [{"start": {"line": 72, "column": 70}, "end": {"line": 72, "column": 84}}, {"start": {"line": 72, "column": 84}, "end": {"line": 72, "column": 88}}]}, "10": {"loc": {"start": {"line": 96, "column": 19}, "end": {"line": 96, "column": 40}}, "type": "binary-expr", "locations": [{"start": {"line": 96, "column": 19}, "end": {"line": 96, "column": 40}}]}, "11": {"loc": {"start": {"line": 115, "column": 19}, "end": {"line": 115, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 115, "column": 19}, "end": {"line": 115, "column": 50}}, {"start": {"line": 115, "column": 50}, "end": {"line": 115, "column": null}}]}, "12": {"loc": {"start": {"line": 129, "column": 20}, "end": {"line": 131, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 130, "column": 24}, "end": {"line": 130, "column": null}}, {"start": {"line": 131, "column": 24}, "end": {"line": 131, "column": null}}]}, "13": {"loc": {"start": {"line": 134, "column": 19}, "end": {"line": 134, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 134, "column": 40}, "end": {"line": 134, "column": 50}}, {"start": {"line": 134, "column": 50}, "end": {"line": 134, "column": null}}]}, "14": {"loc": {"start": {"line": 142, "column": 9}, "end": {"line": 142, "column": 24}}, "type": "binary-expr", "locations": [{"start": {"line": 142, "column": 9}, "end": {"line": 142, "column": 24}}, {"start": {"line": 142, "column": 24}, "end": {"line": 142, "column": null}}]}, "15": {"loc": {"start": {"line": 153, "column": 2}, "end": {"line": 164, "column": null}}, "type": "if", "locations": [{"start": {"line": 153, "column": 2}, "end": {"line": 164, "column": null}}, {"start": {}, "end": {}}]}, "16": {"loc": {"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 153, "column": 6}, "end": {"line": 153, "column": 20}}, {"start": {"line": 153, "column": 20}, "end": {"line": 153, "column": 44}}]}, "17": {"loc": {"start": {"line": 178, "column": 20}, "end": {"line": 180, "column": null}}, "type": "if", "locations": [{"start": {"line": 178, "column": 20}, "end": {"line": 180, "column": null}}, {"start": {}, "end": {}}]}, "18": {"loc": {"start": {"line": 185, "column": 16}, "end": {"line": 189, "column": null}}, "type": "if", "locations": [{"start": {"line": 185, "column": 16}, "end": {"line": 189, "column": null}}, {"start": {"line": 187, "column": 23}, "end": {"line": 189, "column": null}}]}, "19": {"loc": {"start": {"line": 197, "column": 16}, "end": {"line": 201, "column": null}}, "type": "if", "locations": [{"start": {"line": 197, "column": 16}, "end": {"line": 201, "column": null}}, {"start": {"line": 199, "column": 23}, "end": {"line": 201, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0], "6": [0, 0], "7": [0, 0], "8": [0], "9": [0, 0], "10": [0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\roles\\RoleList.tsx": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\components\\roles\\RoleList.tsx", "statementMap": {"0": {"start": {"line": 309, "column": 0}, "end": {"line": 309, "column": 15}}, "1": {"start": {"line": 3, "column": 41}, "end": {"line": 3, "column": null}}, "2": {"start": {"line": 10, "column": 51}, "end": {"line": 307, "column": null}}, "3": {"start": {"line": 21, "column": 36}, "end": {"line": 21, "column": null}}, "4": {"start": {"line": 22, "column": 44}, "end": {"line": 22, "column": null}}, "5": {"start": {"line": 23, "column": 44}, "end": {"line": 23, "column": null}}, "6": {"start": {"line": 26, "column": 22}, "end": {"line": 38, "column": null}}, "7": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": null}}, "8": {"start": {"line": 27, "column": 16}, "end": {"line": 27, "column": null}}, "9": {"start": {"line": 29, "column": 4}, "end": {"line": 37, "column": null}}, "10": {"start": {"line": 30, "column": 21}, "end": {"line": 30, "column": 33}}, "11": {"start": {"line": 31, "column": 21}, "end": {"line": 31, "column": 33}}, "12": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": null}}, "13": {"start": {"line": 33, "column": 29}, "end": {"line": 33, "column": null}}, "14": {"start": {"line": 35, "column": 25}, "end": {"line": 35, "column": null}}, "15": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": null}}, "16": {"start": {"line": 40, "column": 21}, "end": {"line": 47, "column": null}}, "17": {"start": {"line": 41, "column": 4}, "end": {"line": 46, "column": null}}, "18": {"start": {"line": 42, "column": 6}, "end": {"line": 42, "column": null}}, "19": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": null}}, "20": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": null}}, "21": {"start": {"line": 49, "column": 27}, "end": {"line": 57, "column": null}}, "22": {"start": {"line": 50, "column": 24}, "end": {"line": 50, "column": null}}, "23": {"start": {"line": 51, "column": 4}, "end": {"line": 55, "column": null}}, "24": {"start": {"line": 52, "column": 6}, "end": {"line": 52, "column": null}}, "25": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": null}}, "26": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": null}}, "27": {"start": {"line": 59, "column": 26}, "end": {"line": 65, "column": null}}, "28": {"start": {"line": 60, "column": 4}, "end": {"line": 64, "column": null}}, "29": {"start": {"line": 61, "column": 6}, "end": {"line": 61, "column": null}}, "30": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": null}}, "31": {"start": {"line": 63, "column": 49}, "end": {"line": 63, "column": 56}}, "32": {"start": {"line": 67, "column": 22}, "end": {"line": 72, "column": null}}, "33": {"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": null}}, "34": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": null}}, "35": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": null}}, "36": {"start": {"line": 74, "column": 33}, "end": {"line": 78, "column": null}}, "37": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": null}}, "38": {"start": {"line": 75, "column": 24}, "end": {"line": 75, "column": null}}, "39": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": null}}, "40": {"start": {"line": 76, "column": 24}, "end": {"line": 76, "column": null}}, "41": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": null}}, "42": {"start": {"line": 80, "column": 2}, "end": {"line": 100, "column": null}}, "43": {"start": {"line": 89, "column": 14}, "end": {"line": 89, "column": 27}}, "44": {"start": {"line": 116, "column": 31}, "end": {"line": 116, "column": null}}, "45": {"start": {"line": 141, "column": 31}, "end": {"line": 141, "column": null}}, "46": {"start": {"line": 153, "column": 31}, "end": {"line": 153, "column": null}}, "47": {"start": {"line": 168, "column": 31}, "end": {"line": 168, "column": null}}, "48": {"start": {"line": 182, "column": 14}, "end": {"line": 183, "column": null}}, "49": {"start": {"line": 192, "column": 36}, "end": {"line": 192, "column": null}}, "50": {"start": {"line": 239, "column": 37}, "end": {"line": 239, "column": null}}, "51": {"start": {"line": 245, "column": 37}, "end": {"line": 245, "column": null}}, "52": {"start": {"line": 251, "column": 37}, "end": {"line": 251, "column": null}}, "53": {"start": {"line": 285, "column": 31}, "end": {"line": 285, "column": null}}, "54": {"start": {"line": 295, "column": 31}, "end": {"line": 295, "column": null}}, "55": {"start": {"line": 309, "column": 15}, "end": {"line": 309, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 10, "column": 51}, "end": {"line": 10, "column": 52}}, "loc": {"start": {"line": 20, "column": 1}, "end": {"line": 307, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 26, "column": 30}, "end": {"line": 26, "column": null}}, "loc": {"start": {"line": 26, "column": 30}, "end": {"line": 38, "column": 5}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 29, "column": 27}, "end": {"line": 29, "column": 28}}, "loc": {"start": {"line": 29, "column": 31}, "end": {"line": 37, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 40, "column": 21}, "end": {"line": 40, "column": 22}}, "loc": {"start": {"line": 40, "column": 22}, "end": {"line": 47, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 49, "column": 27}, "end": {"line": 49, "column": 28}}, "loc": {"start": {"line": 49, "column": 28}, "end": {"line": 57, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 59, "column": 26}, "end": {"line": 59, "column": null}}, "loc": {"start": {"line": 59, "column": 26}, "end": {"line": 65, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 63, "column": 41}, "end": {"line": 63, "column": 49}}, "loc": {"start": {"line": 63, "column": 49}, "end": {"line": 63, "column": 56}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 67, "column": 22}, "end": {"line": 67, "column": 23}}, "loc": {"start": {"line": 67, "column": 23}, "end": {"line": 72, "column": null}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 74, "column": 33}, "end": {"line": 74, "column": 34}}, "loc": {"start": {"line": 74, "column": 34}, "end": {"line": 78, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 88, "column": 31}, "end": {"line": 88, "column": 32}}, "loc": {"start": {"line": 89, "column": 14}, "end": {"line": 89, "column": 27}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 116, "column": 25}, "end": {"line": 116, "column": 31}}, "loc": {"start": {"line": 116, "column": 31}, "end": {"line": 116, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 141, "column": 25}, "end": {"line": 141, "column": 31}}, "loc": {"start": {"line": 141, "column": 31}, "end": {"line": 141, "column": null}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 153, "column": 25}, "end": {"line": 153, "column": 31}}, "loc": {"start": {"line": 153, "column": 31}, "end": {"line": 153, "column": null}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 168, "column": 25}, "end": {"line": 168, "column": 31}}, "loc": {"start": {"line": 168, "column": 31}, "end": {"line": 168, "column": null}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 181, "column": 29}, "end": {"line": 181, "column": 30}}, "loc": {"start": {"line": 182, "column": 14}, "end": {"line": 183, "column": null}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 192, "column": 30}, "end": {"line": 192, "column": 36}}, "loc": {"start": {"line": 192, "column": 36}, "end": {"line": 192, "column": null}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 239, "column": 31}, "end": {"line": 239, "column": 37}}, "loc": {"start": {"line": 239, "column": 37}, "end": {"line": 239, "column": null}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 245, "column": 31}, "end": {"line": 245, "column": 37}}, "loc": {"start": {"line": 245, "column": 37}, "end": {"line": 245, "column": null}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 251, "column": 31}, "end": {"line": 251, "column": 37}}, "loc": {"start": {"line": 251, "column": 37}, "end": {"line": 251, "column": null}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 285, "column": 25}, "end": {"line": 285, "column": 31}}, "loc": {"start": {"line": 285, "column": 31}, "end": {"line": 285, "column": null}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 295, "column": 25}, "end": {"line": 295, "column": 31}}, "loc": {"start": {"line": 295, "column": 31}, "end": {"line": 295, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 17}}, "type": "default-arg", "locations": [{"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 17}}]}, "1": {"loc": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": 16}}, "type": "default-arg", "locations": [{"start": {"line": 19, "column": 14}, "end": {"line": 19, "column": 16}}]}, "2": {"loc": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": null}}, "type": "if", "locations": [{"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": null}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": null}}, "type": "if", "locations": [{"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": null}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 35, "column": 25}, "end": {"line": 35, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 35, "column": 43}, "end": {"line": 35, "column": 48}}, {"start": {"line": 35, "column": 48}, "end": {"line": 35, "column": null}}]}, "5": {"loc": {"start": {"line": 36, "column": 13}, "end": {"line": 36, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 36, "column": 39}, "end": {"line": 36, "column": 52}}, {"start": {"line": 36, "column": 52}, "end": {"line": 36, "column": null}}]}, "6": {"loc": {"start": {"line": 41, "column": 4}, "end": {"line": 46, "column": null}}, "type": "if", "locations": [{"start": {"line": 41, "column": 4}, "end": {"line": 46, "column": null}}, {"start": {"line": 43, "column": 11}, "end": {"line": 46, "column": null}}]}, "7": {"loc": {"start": {"line": 42, "column": 23}, "end": {"line": 42, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 42, "column": 49}, "end": {"line": 42, "column": 58}}, {"start": {"line": 42, "column": 58}, "end": {"line": 42, "column": null}}]}, "8": {"loc": {"start": {"line": 51, "column": 4}, "end": {"line": 55, "column": null}}, "type": "if", "locations": [{"start": {"line": 51, "column": 4}, "end": {"line": 55, "column": null}}, {"start": {"line": 53, "column": 11}, "end": {"line": 55, "column": null}}]}, "9": {"loc": {"start": {"line": 60, "column": 4}, "end": {"line": 64, "column": null}}, "type": "if", "locations": [{"start": {"line": 60, "column": 4}, "end": {"line": 64, "column": null}}, {"start": {"line": 62, "column": 11}, "end": {"line": 64, "column": null}}]}, "10": {"loc": {"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": null}}, "type": "if", "locations": [{"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": null}}, {"start": {}, "end": {}}]}, "11": {"loc": {"start": {"line": 71, "column": 11}, "end": {"line": 71, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 71, "column": 37}, "end": {"line": 71, "column": 43}}, {"start": {"line": 71, "column": 43}, "end": {"line": 71, "column": null}}]}, "12": {"loc": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": null}}, "type": "if", "locations": [{"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": null}}, {"start": {}, "end": {}}]}, "13": {"loc": {"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": null}}, "type": "if", "locations": [{"start": {"line": 76, "column": 4}, "end": {"line": 76, "column": null}}, {"start": {}, "end": {}}]}, "14": {"loc": {"start": {"line": 80, "column": 2}, "end": {"line": 100, "column": null}}, "type": "if", "locations": [{"start": {"line": 80, "column": 2}, "end": {"line": 100, "column": null}}, {"start": {}, "end": {}}]}, "15": {"loc": {"start": {"line": 110, "column": 11}, "end": {"line": 110, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 110, "column": 11}, "end": {"line": 110, "column": null}}]}, "16": {"loc": {"start": {"line": 134, "column": 27}, "end": {"line": 134, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 134, "column": 27}, "end": {"line": 134, "column": 62}}, {"start": {"line": 134, "column": 66}, "end": {"line": 134, "column": null}}]}, "17": {"loc": {"start": {"line": 185, "column": 18}, "end": {"line": 185, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 185, "column": 47}, "end": {"line": 185, "column": 62}}, {"start": {"line": 185, "column": 62}, "end": {"line": 185, "column": null}}]}, "18": {"loc": {"start": {"line": 207, "column": 21}, "end": {"line": 207, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 207, "column": 21}, "end": {"line": 207, "column": 37}}, {"start": {"line": 207, "column": 41}, "end": {"line": 207, "column": null}}]}, "19": {"loc": {"start": {"line": 222, "column": 22}, "end": {"line": 224, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 223, "column": 26}, "end": {"line": 223, "column": null}}, {"start": {"line": 224, "column": 26}, "end": {"line": 224, "column": null}}]}, "20": {"loc": {"start": {"line": 227, "column": 21}, "end": {"line": 227, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 227, "column": 37}, "end": {"line": 227, "column": 47}}, {"start": {"line": 227, "column": 47}, "end": {"line": 227, "column": null}}]}, "21": {"loc": {"start": {"line": 231, "column": 19}, "end": {"line": 231, "column": 47}}, "type": "binary-expr", "locations": [{"start": {"line": 231, "column": 19}, "end": {"line": 231, "column": 45}}, {"start": {"line": 231, "column": 45}, "end": {"line": 231, "column": 47}}]}, "22": {"loc": {"start": {"line": 265, "column": 7}, "end": {"line": 265, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 265, "column": 7}, "end": {"line": 265, "column": null}}]}, "23": {"loc": {"start": {"line": 277, "column": 7}, "end": {"line": 277, "column": 21}}, "type": "binary-expr", "locations": [{"start": {"line": 277, "column": 7}, "end": {"line": 277, "column": 21}}, {"start": {"line": 277, "column": 21}, "end": {"line": 277, "column": null}}]}}, "s": {"0": 21, "1": 2, "2": 2, "3": 25, "4": 25, "5": 25, "6": 25, "7": 22, "8": 0, "9": 22, "10": 64, "11": 64, "12": 64, "13": 0, "14": 64, "15": 64, "16": 25, "17": 1, "18": 1, "19": 0, "20": 0, "21": 25, "22": 2, "23": 2, "24": 0, "25": 2, "26": 2, "27": 25, "28": 1, "29": 0, "30": 1, "31": 3, "32": 25, "33": 69, "34": 46, "35": 23, "36": 25, "37": 57, "38": 57, "39": 0, "40": 0, "41": 0, "42": 25, "43": 10, "44": 0, "45": 1, "46": 0, "47": 0, "48": 57, "49": 2, "50": 1, "51": 1, "52": 1, "53": 0, "54": 1, "55": 2}, "f": {"0": 25, "1": 22, "2": 64, "3": 1, "4": 2, "5": 1, "6": 3, "7": 69, "8": 57, "9": 10, "10": 0, "11": 1, "12": 0, "13": 0, "14": 57, "15": 2, "16": 1, "17": 1, "18": 1, "19": 0, "20": 1}, "b": {"0": [0], "1": [25], "2": [0, 22], "3": [0, 64], "4": [32, 32], "5": [60, 4], "6": [1, 0], "7": [1, 0], "8": [0, 2], "9": [0, 1], "10": [46, 23], "11": [22, 1], "12": [57, 0], "13": [0, 0], "14": [2, 23], "15": [23], "16": [23, 3], "17": [5, 52], "18": [57, 0], "19": [39, 18], "20": [39, 18], "21": [57, 57], "22": [23], "23": [23, 23]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\hooks\\usePermissions.ts": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\hooks\\usePermissions.ts", "statementMap": {"0": {"start": {"line": 281, "column": 13}, "end": {"line": 281, "column": 35}}, "1": {"start": {"line": 320, "column": 13}, "end": {"line": 320, "column": 39}}, "2": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 30}}, "3": {"start": {"line": 1, "column": 26}, "end": {"line": 1, "column": null}}, "4": {"start": {"line": 2, "column": 35}, "end": {"line": 2, "column": null}}, "5": {"start": {"line": 8, "column": 30}, "end": {"line": 276, "column": null}}, "6": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": null}}, "7": {"start": {"line": 62, "column": 2}, "end": {"line": 66, "column": null}}, "8": {"start": {"line": 63, "column": 4}, "end": {"line": 65, "column": null}}, "9": {"start": {"line": 64, "column": 6}, "end": {"line": 64, "column": null}}, "10": {"start": {"line": 69, "column": 2}, "end": {"line": 73, "column": null}}, "11": {"start": {"line": 70, "column": 4}, "end": {"line": 72, "column": null}}, "12": {"start": {"line": 71, "column": 6}, "end": {"line": 71, "column": null}}, "13": {"start": {"line": 76, "column": 29}, "end": {"line": 78, "column": null}}, "14": {"start": {"line": 77, "column": 4}, "end": {"line": 77, "column": null}}, "15": {"start": {"line": 80, "column": 24}, "end": {"line": 82, "column": null}}, "16": {"start": {"line": 81, "column": 4}, "end": {"line": 81, "column": null}}, "17": {"start": {"line": 84, "column": 27}, "end": {"line": 86, "column": null}}, "18": {"start": {"line": 85, "column": 4}, "end": {"line": 85, "column": null}}, "19": {"start": {"line": 88, "column": 28}, "end": {"line": 90, "column": null}}, "20": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": null}}, "21": {"start": {"line": 92, "column": 33}, "end": {"line": 100, "column": null}}, "22": {"start": {"line": 93, "column": 4}, "end": {"line": 99, "column": null}}, "23": {"start": {"line": 94, "column": 28}, "end": {"line": 94, "column": null}}, "24": {"start": {"line": 95, "column": 6}, "end": {"line": 95, "column": 23}}, "25": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": null}}, "26": {"start": {"line": 98, "column": 6}, "end": {"line": 98, "column": null}}, "27": {"start": {"line": 102, "column": 33}, "end": {"line": 110, "column": null}}, "28": {"start": {"line": 103, "column": 4}, "end": {"line": 109, "column": null}}, "29": {"start": {"line": 104, "column": 32}, "end": {"line": 104, "column": null}}, "30": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 23}}, "31": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": null}}, "32": {"start": {"line": 108, "column": 6}, "end": {"line": 108, "column": null}}, "33": {"start": {"line": 112, "column": 33}, "end": {"line": 119, "column": null}}, "34": {"start": {"line": 113, "column": 4}, "end": {"line": 118, "column": null}}, "35": {"start": {"line": 114, "column": 6}, "end": {"line": 114, "column": null}}, "36": {"start": {"line": 115, "column": 6}, "end": {"line": 115, "column": 23}}, "37": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": null}}, "38": {"start": {"line": 121, "column": 38}, "end": {"line": 128, "column": null}}, "39": {"start": {"line": 122, "column": 4}, "end": {"line": 127, "column": null}}, "40": {"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": null}}, "41": {"start": {"line": 124, "column": 6}, "end": {"line": 124, "column": 23}}, "42": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": null}}, "43": {"start": {"line": 130, "column": 38}, "end": {"line": 138, "column": null}}, "44": {"start": {"line": 131, "column": 4}, "end": {"line": 137, "column": null}}, "45": {"start": {"line": 132, "column": 29}, "end": {"line": 132, "column": null}}, "46": {"start": {"line": 133, "column": 6}, "end": {"line": 133, "column": 23}}, "47": {"start": {"line": 134, "column": 6}, "end": {"line": 134, "column": null}}, "48": {"start": {"line": 136, "column": 6}, "end": {"line": 136, "column": null}}, "49": {"start": {"line": 140, "column": 35}, "end": {"line": 146, "column": null}}, "50": {"start": {"line": 141, "column": 4}, "end": {"line": 145, "column": null}}, "51": {"start": {"line": 142, "column": 6}, "end": {"line": 142, "column": null}}, "52": {"start": {"line": 144, "column": 6}, "end": {"line": 144, "column": null}}, "53": {"start": {"line": 148, "column": 35}, "end": {"line": 161, "column": null}}, "54": {"start": {"line": 154, "column": 4}, "end": {"line": 160, "column": null}}, "55": {"start": {"line": 155, "column": 26}, "end": {"line": 155, "column": null}}, "56": {"start": {"line": 156, "column": 6}, "end": {"line": 156, "column": 23}}, "57": {"start": {"line": 157, "column": 6}, "end": {"line": 157, "column": null}}, "58": {"start": {"line": 159, "column": 6}, "end": {"line": 159, "column": null}}, "59": {"start": {"line": 163, "column": 29}, "end": {"line": 165, "column": null}}, "60": {"start": {"line": 164, "column": 4}, "end": {"line": 164, "column": null}}, "61": {"start": {"line": 167, "column": 27}, "end": {"line": 169, "column": null}}, "62": {"start": {"line": 168, "column": 4}, "end": {"line": 168, "column": null}}, "63": {"start": {"line": 171, "column": 31}, "end": {"line": 173, "column": null}}, "64": {"start": {"line": 172, "column": 4}, "end": {"line": 172, "column": null}}, "65": {"start": {"line": 175, "column": 27}, "end": {"line": 177, "column": null}}, "66": {"start": {"line": 176, "column": 4}, "end": {"line": 176, "column": null}}, "67": {"start": {"line": 179, "column": 25}, "end": {"line": 181, "column": null}}, "68": {"start": {"line": 180, "column": 4}, "end": {"line": 180, "column": null}}, "69": {"start": {"line": 183, "column": 22}, "end": {"line": 185, "column": null}}, "70": {"start": {"line": 184, "column": 4}, "end": {"line": 184, "column": null}}, "71": {"start": {"line": 187, "column": 21}, "end": {"line": 189, "column": null}}, "72": {"start": {"line": 188, "column": 4}, "end": {"line": 188, "column": null}}, "73": {"start": {"line": 191, "column": 32}, "end": {"line": 193, "column": null}}, "74": {"start": {"line": 192, "column": 4}, "end": {"line": 192, "column": null}}, "75": {"start": {"line": 195, "column": 33}, "end": {"line": 197, "column": null}}, "76": {"start": {"line": 196, "column": 4}, "end": {"line": 196, "column": null}}, "77": {"start": {"line": 199, "column": 32}, "end": {"line": 201, "column": null}}, "78": {"start": {"line": 200, "column": 4}, "end": {"line": 200, "column": null}}, "79": {"start": {"line": 204, "column": 25}, "end": {"line": 204, "column": null}}, "80": {"start": {"line": 205, "column": 19}, "end": {"line": 205, "column": null}}, "81": {"start": {"line": 206, "column": 23}, "end": {"line": 206, "column": null}}, "82": {"start": {"line": 207, "column": 22}, "end": {"line": 207, "column": null}}, "83": {"start": {"line": 208, "column": 21}, "end": {"line": 208, "column": null}}, "84": {"start": {"line": 209, "column": 22}, "end": {"line": 209, "column": null}}, "85": {"start": {"line": 210, "column": 22}, "end": {"line": 210, "column": null}}, "86": {"start": {"line": 211, "column": 29}, "end": {"line": 211, "column": null}}, "87": {"start": {"line": 213, "column": 2}, "end": {"line": 275, "column": null}}, "88": {"start": {"line": 281, "column": 35}, "end": {"line": 315, "column": null}}, "89": {"start": {"line": 295, "column": 6}, "end": {"line": 295, "column": null}}, "90": {"start": {"line": 297, "column": 2}, "end": {"line": 299, "column": null}}, "91": {"start": {"line": 298, "column": 4}, "end": {"line": 298, "column": null}}, "92": {"start": {"line": 301, "column": 2}, "end": {"line": 314, "column": null}}, "93": {"start": {"line": 320, "column": 39}, "end": {"line": 340, "column": null}}, "94": {"start": {"line": 327, "column": 6}, "end": {"line": 327, "column": null}}, "95": {"start": {"line": 329, "column": 2}, "end": {"line": 331, "column": null}}, "96": {"start": {"line": 330, "column": 4}, "end": {"line": 330, "column": null}}, "97": {"start": {"line": 333, "column": 2}, "end": {"line": 339, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 8, "column": 30}, "end": {"line": 8, "column": 31}}, "loc": {"start": {"line": 8, "column": 58}, "end": {"line": 276, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 62, "column": 12}, "end": {"line": 62, "column": null}}, "loc": {"start": {"line": 62, "column": 12}, "end": {"line": 66, "column": 5}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": null}}, "loc": {"start": {"line": 69, "column": 12}, "end": {"line": 73, "column": 5}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 76, "column": 29}, "end": {"line": 76, "column": null}}, "loc": {"start": {"line": 76, "column": 29}, "end": {"line": 78, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 80, "column": 24}, "end": {"line": 80, "column": null}}, "loc": {"start": {"line": 80, "column": 24}, "end": {"line": 82, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 84, "column": 27}, "end": {"line": 84, "column": null}}, "loc": {"start": {"line": 84, "column": 27}, "end": {"line": 86, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 88, "column": 28}, "end": {"line": 88, "column": null}}, "loc": {"start": {"line": 88, "column": 28}, "end": {"line": 90, "column": null}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 92, "column": 33}, "end": {"line": 92, "column": 40}}, "loc": {"start": {"line": 92, "column": 40}, "end": {"line": 100, "column": null}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 102, "column": 33}, "end": {"line": 102, "column": 40}}, "loc": {"start": {"line": 102, "column": 52}, "end": {"line": 110, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 112, "column": 33}, "end": {"line": 112, "column": 40}}, "loc": {"start": {"line": 112, "column": 40}, "end": {"line": 119, "column": null}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 121, "column": 38}, "end": {"line": 121, "column": 45}}, "loc": {"start": {"line": 121, "column": 45}, "end": {"line": 128, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 130, "column": 38}, "end": {"line": 130, "column": 45}}, "loc": {"start": {"line": 130, "column": 45}, "end": {"line": 138, "column": null}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 140, "column": 35}, "end": {"line": 140, "column": 42}}, "loc": {"start": {"line": 140, "column": 42}, "end": {"line": 146, "column": null}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 148, "column": 35}, "end": {"line": 148, "column": null}}, "loc": {"start": {"line": 152, "column": 4}, "end": {"line": 161, "column": null}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 163, "column": 29}, "end": {"line": 163, "column": 30}}, "loc": {"start": {"line": 163, "column": 30}, "end": {"line": 165, "column": null}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 167, "column": 27}, "end": {"line": 167, "column": 28}}, "loc": {"start": {"line": 167, "column": 28}, "end": {"line": 169, "column": null}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 171, "column": 31}, "end": {"line": 171, "column": 32}}, "loc": {"start": {"line": 171, "column": 32}, "end": {"line": 173, "column": null}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 175, "column": 27}, "end": {"line": 175, "column": 28}}, "loc": {"start": {"line": 175, "column": 28}, "end": {"line": 177, "column": null}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 179, "column": 25}, "end": {"line": 179, "column": null}}, "loc": {"start": {"line": 179, "column": 25}, "end": {"line": 181, "column": null}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 183, "column": 22}, "end": {"line": 183, "column": null}}, "loc": {"start": {"line": 183, "column": 22}, "end": {"line": 185, "column": null}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 187, "column": 21}, "end": {"line": 187, "column": null}}, "loc": {"start": {"line": 187, "column": 21}, "end": {"line": 189, "column": null}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 191, "column": 32}, "end": {"line": 191, "column": 33}}, "loc": {"start": {"line": 191, "column": 33}, "end": {"line": 193, "column": null}}}, "22": {"name": "(anonymous_26)", "decl": {"start": {"line": 195, "column": 33}, "end": {"line": 195, "column": 34}}, "loc": {"start": {"line": 195, "column": 34}, "end": {"line": 197, "column": null}}}, "23": {"name": "(anonymous_27)", "decl": {"start": {"line": 199, "column": 32}, "end": {"line": 199, "column": 33}}, "loc": {"start": {"line": 199, "column": 33}, "end": {"line": 201, "column": null}}}, "24": {"name": "(anonymous_28)", "decl": {"start": {"line": 281, "column": 35}, "end": {"line": 281, "column": null}}, "loc": {"start": {"line": 281, "column": 35}, "end": {"line": 315, "column": null}}}, "25": {"name": "(anonymous_29)", "decl": {"start": {"line": 297, "column": 12}, "end": {"line": 297, "column": null}}, "loc": {"start": {"line": 297, "column": 12}, "end": {"line": 299, "column": 5}}}, "26": {"name": "(anonymous_30)", "decl": {"start": {"line": 320, "column": 39}, "end": {"line": 320, "column": null}}, "loc": {"start": {"line": 320, "column": 39}, "end": {"line": 340, "column": null}}}, "27": {"name": "(anonymous_31)", "decl": {"start": {"line": 329, "column": 12}, "end": {"line": 329, "column": null}}, "loc": {"start": {"line": 329, "column": 12}, "end": {"line": 331, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 8, "column": 31}, "end": {"line": 8, "column": 56}}, "type": "default-arg", "locations": [{"start": {"line": 8, "column": 52}, "end": {"line": 8, "column": 56}}]}, "1": {"loc": {"start": {"line": 63, "column": 4}, "end": {"line": 65, "column": null}}, "type": "if", "locations": [{"start": {"line": 63, "column": 4}, "end": {"line": 65, "column": null}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 70, "column": 4}, "end": {"line": 72, "column": null}}, "type": "if", "locations": [{"start": {"line": 70, "column": 4}, "end": {"line": 72, "column": null}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 211, "column": 29}, "end": {"line": 211, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 211, "column": 29}, "end": {"line": 211, "column": 59}}, {"start": {"line": 211, "column": 59}, "end": {"line": 211, "column": 90}}, {"start": {"line": 211, "column": 90}, "end": {"line": 211, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "95": 0, "96": 0, "97": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\hooks\\useRoles.ts": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\hooks\\useRoles.ts", "statementMap": {"0": {"start": {"line": 232, "column": 13}, "end": {"line": 232, "column": 32}}, "1": {"start": {"line": 257, "column": 13}, "end": {"line": 257, "column": 33}}, "2": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 24}}, "3": {"start": {"line": 1, "column": 26}, "end": {"line": 1, "column": null}}, "4": {"start": {"line": 2, "column": 29}, "end": {"line": 2, "column": null}}, "5": {"start": {"line": 8, "column": 24}, "end": {"line": 227, "column": null}}, "6": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": null}}, "7": {"start": {"line": 51, "column": 2}, "end": {"line": 55, "column": null}}, "8": {"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": null}}, "9": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": null}}, "10": {"start": {"line": 58, "column": 2}, "end": {"line": 62, "column": null}}, "11": {"start": {"line": 59, "column": 4}, "end": {"line": 61, "column": null}}, "12": {"start": {"line": 60, "column": 6}, "end": {"line": 60, "column": null}}, "13": {"start": {"line": 65, "column": 23}, "end": {"line": 67, "column": null}}, "14": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": null}}, "15": {"start": {"line": 69, "column": 27}, "end": {"line": 71, "column": null}}, "16": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": null}}, "17": {"start": {"line": 73, "column": 28}, "end": {"line": 75, "column": null}}, "18": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": null}}, "19": {"start": {"line": 77, "column": 27}, "end": {"line": 85, "column": null}}, "20": {"start": {"line": 78, "column": 4}, "end": {"line": 84, "column": null}}, "21": {"start": {"line": 79, "column": 22}, "end": {"line": 79, "column": null}}, "22": {"start": {"line": 80, "column": 6}, "end": {"line": 80, "column": 26}}, "23": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": null}}, "24": {"start": {"line": 83, "column": 6}, "end": {"line": 83, "column": null}}, "25": {"start": {"line": 87, "column": 27}, "end": {"line": 95, "column": null}}, "26": {"start": {"line": 88, "column": 4}, "end": {"line": 94, "column": null}}, "27": {"start": {"line": 89, "column": 26}, "end": {"line": 89, "column": null}}, "28": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": 26}}, "29": {"start": {"line": 91, "column": 6}, "end": {"line": 91, "column": null}}, "30": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": null}}, "31": {"start": {"line": 97, "column": 27}, "end": {"line": 104, "column": null}}, "32": {"start": {"line": 98, "column": 4}, "end": {"line": 103, "column": null}}, "33": {"start": {"line": 99, "column": 6}, "end": {"line": 99, "column": null}}, "34": {"start": {"line": 100, "column": 6}, "end": {"line": 100, "column": 26}}, "35": {"start": {"line": 102, "column": 6}, "end": {"line": 102, "column": null}}, "36": {"start": {"line": 106, "column": 32}, "end": {"line": 113, "column": null}}, "37": {"start": {"line": 107, "column": 4}, "end": {"line": 112, "column": null}}, "38": {"start": {"line": 108, "column": 6}, "end": {"line": 108, "column": null}}, "39": {"start": {"line": 109, "column": 6}, "end": {"line": 109, "column": 26}}, "40": {"start": {"line": 111, "column": 6}, "end": {"line": 111, "column": null}}, "41": {"start": {"line": 115, "column": 34}, "end": {"line": 125, "column": null}}, "42": {"start": {"line": 116, "column": 4}, "end": {"line": 124, "column": null}}, "43": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": null}}, "44": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": null}}, "45": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": null}}, "46": {"start": {"line": 123, "column": 6}, "end": {"line": 123, "column": null}}, "47": {"start": {"line": 127, "column": 34}, "end": {"line": 137, "column": null}}, "48": {"start": {"line": 128, "column": 4}, "end": {"line": 136, "column": null}}, "49": {"start": {"line": 129, "column": 6}, "end": {"line": 129, "column": null}}, "50": {"start": {"line": 131, "column": 6}, "end": {"line": 133, "column": null}}, "51": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": null}}, "52": {"start": {"line": 135, "column": 6}, "end": {"line": 135, "column": null}}, "53": {"start": {"line": 139, "column": 29}, "end": {"line": 141, "column": null}}, "54": {"start": {"line": 140, "column": 4}, "end": {"line": 140, "column": null}}, "55": {"start": {"line": 143, "column": 27}, "end": {"line": 145, "column": null}}, "56": {"start": {"line": 144, "column": 4}, "end": {"line": 144, "column": null}}, "57": {"start": {"line": 147, "column": 31}, "end": {"line": 149, "column": null}}, "58": {"start": {"line": 148, "column": 4}, "end": {"line": 148, "column": null}}, "59": {"start": {"line": 151, "column": 21}, "end": {"line": 153, "column": null}}, "60": {"start": {"line": 152, "column": 4}, "end": {"line": 152, "column": null}}, "61": {"start": {"line": 155, "column": 25}, "end": {"line": 157, "column": null}}, "62": {"start": {"line": 156, "column": 4}, "end": {"line": 156, "column": null}}, "63": {"start": {"line": 159, "column": 22}, "end": {"line": 161, "column": null}}, "64": {"start": {"line": 160, "column": 4}, "end": {"line": 160, "column": null}}, "65": {"start": {"line": 163, "column": 21}, "end": {"line": 165, "column": null}}, "66": {"start": {"line": 164, "column": 4}, "end": {"line": 164, "column": null}}, "67": {"start": {"line": 168, "column": 19}, "end": {"line": 168, "column": null}}, "68": {"start": {"line": 169, "column": 19}, "end": {"line": 169, "column": null}}, "69": {"start": {"line": 170, "column": 23}, "end": {"line": 170, "column": null}}, "70": {"start": {"line": 171, "column": 22}, "end": {"line": 171, "column": null}}, "71": {"start": {"line": 172, "column": 21}, "end": {"line": 172, "column": null}}, "72": {"start": {"line": 173, "column": 22}, "end": {"line": 173, "column": null}}, "73": {"start": {"line": 174, "column": 22}, "end": {"line": 174, "column": null}}, "74": {"start": {"line": 176, "column": 2}, "end": {"line": 226, "column": null}}, "75": {"start": {"line": 232, "column": 32}, "end": {"line": 252, "column": null}}, "76": {"start": {"line": 239, "column": 6}, "end": {"line": 239, "column": null}}, "77": {"start": {"line": 241, "column": 2}, "end": {"line": 243, "column": null}}, "78": {"start": {"line": 242, "column": 4}, "end": {"line": 242, "column": null}}, "79": {"start": {"line": 245, "column": 2}, "end": {"line": 251, "column": null}}, "80": {"start": {"line": 257, "column": 33}, "end": {"line": 277, "column": null}}, "81": {"start": {"line": 264, "column": 6}, "end": {"line": 264, "column": null}}, "82": {"start": {"line": 266, "column": 2}, "end": {"line": 268, "column": null}}, "83": {"start": {"line": 267, "column": 4}, "end": {"line": 267, "column": null}}, "84": {"start": {"line": 270, "column": 2}, "end": {"line": 276, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 8, "column": 24}, "end": {"line": 8, "column": 25}}, "loc": {"start": {"line": 8, "column": 52}, "end": {"line": 227, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 51, "column": 12}, "end": {"line": 51, "column": null}}, "loc": {"start": {"line": 51, "column": 12}, "end": {"line": 55, "column": 5}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 58, "column": 12}, "end": {"line": 58, "column": null}}, "loc": {"start": {"line": 58, "column": 12}, "end": {"line": 62, "column": 5}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 65, "column": 23}, "end": {"line": 65, "column": null}}, "loc": {"start": {"line": 65, "column": 23}, "end": {"line": 67, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 69, "column": 27}, "end": {"line": 69, "column": null}}, "loc": {"start": {"line": 69, "column": 27}, "end": {"line": 71, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 73, "column": 28}, "end": {"line": 73, "column": null}}, "loc": {"start": {"line": 73, "column": 28}, "end": {"line": 75, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 77, "column": 27}, "end": {"line": 77, "column": 34}}, "loc": {"start": {"line": 77, "column": 34}, "end": {"line": 85, "column": null}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 87, "column": 27}, "end": {"line": 87, "column": 34}}, "loc": {"start": {"line": 87, "column": 46}, "end": {"line": 95, "column": null}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 97, "column": 27}, "end": {"line": 97, "column": 34}}, "loc": {"start": {"line": 97, "column": 34}, "end": {"line": 104, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 106, "column": 32}, "end": {"line": 106, "column": 39}}, "loc": {"start": {"line": 106, "column": 39}, "end": {"line": 113, "column": null}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 115, "column": 34}, "end": {"line": 115, "column": 41}}, "loc": {"start": {"line": 115, "column": 57}, "end": {"line": 125, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 127, "column": 34}, "end": {"line": 127, "column": 41}}, "loc": {"start": {"line": 127, "column": 57}, "end": {"line": 137, "column": null}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 139, "column": 29}, "end": {"line": 139, "column": 30}}, "loc": {"start": {"line": 139, "column": 30}, "end": {"line": 141, "column": null}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 143, "column": 27}, "end": {"line": 143, "column": 28}}, "loc": {"start": {"line": 143, "column": 28}, "end": {"line": 145, "column": null}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 147, "column": 31}, "end": {"line": 147, "column": 32}}, "loc": {"start": {"line": 147, "column": 32}, "end": {"line": 149, "column": null}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 151, "column": 21}, "end": {"line": 151, "column": 22}}, "loc": {"start": {"line": 151, "column": 22}, "end": {"line": 153, "column": null}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 155, "column": 25}, "end": {"line": 155, "column": null}}, "loc": {"start": {"line": 155, "column": 25}, "end": {"line": 157, "column": null}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 159, "column": 22}, "end": {"line": 159, "column": null}}, "loc": {"start": {"line": 159, "column": 22}, "end": {"line": 161, "column": null}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 163, "column": 21}, "end": {"line": 163, "column": null}}, "loc": {"start": {"line": 163, "column": 21}, "end": {"line": 165, "column": null}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 232, "column": 32}, "end": {"line": 232, "column": null}}, "loc": {"start": {"line": 232, "column": 32}, "end": {"line": 252, "column": null}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 241, "column": 12}, "end": {"line": 241, "column": null}}, "loc": {"start": {"line": 241, "column": 12}, "end": {"line": 243, "column": 5}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 257, "column": 33}, "end": {"line": 257, "column": null}}, "loc": {"start": {"line": 257, "column": 33}, "end": {"line": 277, "column": null}}}, "22": {"name": "(anonymous_26)", "decl": {"start": {"line": 266, "column": 12}, "end": {"line": 266, "column": null}}, "loc": {"start": {"line": 266, "column": 12}, "end": {"line": 268, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 8, "column": 25}, "end": {"line": 8, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 8, "column": 46}, "end": {"line": 8, "column": 50}}]}, "1": {"loc": {"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": null}}, "type": "if", "locations": [{"start": {"line": 52, "column": 4}, "end": {"line": 54, "column": null}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 59, "column": 4}, "end": {"line": 61, "column": null}}, "type": "if", "locations": [{"start": {"line": 59, "column": 4}, "end": {"line": 61, "column": null}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": null}}, "type": "if", "locations": [{"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": null}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 131, "column": 6}, "end": {"line": 133, "column": null}}, "type": "if", "locations": [{"start": {"line": 131, "column": 6}, "end": {"line": 133, "column": null}}, {"start": {}, "end": {}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\services\\api.ts": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\services\\api.ts", "statementMap": {"0": {"start": {"line": 4, "column": 13}, "end": {"line": 4, "column": 30}}, "1": {"start": {"line": 156, "column": 13}, "end": {"line": 156, "column": 26}}, "2": {"start": {"line": 193, "column": 13}, "end": {"line": 193, "column": 32}}, "3": {"start": {"line": 217, "column": 0}, "end": {"line": 217, "column": 15}}, "4": {"start": {"line": 159, "column": 13}, "end": {"line": 159, "column": 30}}, "5": {"start": {"line": 171, "column": 13}, "end": {"line": 171, "column": 26}}, "6": {"start": {"line": 182, "column": 13}, "end": {"line": 182, "column": 35}}, "7": {"start": {"line": 176, "column": 13}, "end": {"line": 176, "column": 33}}, "8": {"start": {"line": 205, "column": 13}, "end": {"line": 205, "column": 32}}, "9": {"start": {"line": 2, "column": 21}, "end": {"line": 2, "column": null}}, "10": {"start": {"line": 11, "column": 4}, "end": {"line": 9, "column": 26}}, "11": {"start": {"line": 12, "column": 4}, "end": {"line": 12, "column": null}}, "12": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": null}}, "13": {"start": {"line": 46, "column": 16}, "end": {"line": 46, "column": 44}}, "14": {"start": {"line": 48, "column": 40}, "end": {"line": 50, "column": null}}, "15": {"start": {"line": 53, "column": 19}, "end": {"line": 53, "column": null}}, "16": {"start": {"line": 54, "column": 4}, "end": {"line": 56, "column": null}}, "17": {"start": {"line": 55, "column": 6}, "end": {"line": 55, "column": null}}, "18": {"start": {"line": 58, "column": 32}, "end": {"line": 64, "column": null}}, "19": {"start": {"line": 66, "column": 4}, "end": {"line": 99, "column": null}}, "20": {"start": {"line": 67, "column": 23}, "end": {"line": 67, "column": null}}, "21": {"start": {"line": 68, "column": 19}, "end": {"line": 68, "column": null}}, "22": {"start": {"line": 70, "column": 6}, "end": {"line": 77, "column": null}}, "23": {"start": {"line": 71, "column": 8}, "end": {"line": 75, "column": null}}, "24": {"start": {"line": 79, "column": 6}, "end": {"line": 86, "column": null}}, "25": {"start": {"line": 80, "column": 8}, "end": {"line": 84, "column": null}}, "26": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": null}}, "27": {"start": {"line": 90, "column": 6}, "end": {"line": 92, "column": null}}, "28": {"start": {"line": 91, "column": 8}, "end": {"line": 91, "column": null}}, "29": {"start": {"line": 95, "column": 6}, "end": {"line": 97, "column": null}}, "30": {"start": {"line": 105, "column": 4}, "end": {"line": 105, "column": null}}, "31": {"start": {"line": 110, "column": 16}, "end": {"line": 110, "column": null}}, "32": {"start": {"line": 112, "column": 4}, "end": {"line": 118, "column": null}}, "33": {"start": {"line": 113, "column": 6}, "end": {"line": 117, "column": null}}, "34": {"start": {"line": 114, "column": 8}, "end": {"line": 116, "column": null}}, "35": {"start": {"line": 115, "column": 10}, "end": {"line": 115, "column": null}}, "36": {"start": {"line": 120, "column": 4}, "end": {"line": 120, "column": null}}, "37": {"start": {"line": 125, "column": 4}, "end": {"line": 128, "column": null}}, "38": {"start": {"line": 133, "column": 4}, "end": {"line": 136, "column": null}}, "39": {"start": {"line": 141, "column": 4}, "end": {"line": 143, "column": null}}, "40": {"start": {"line": 148, "column": 4}, "end": {"line": 151, "column": null}}, "41": {"start": {"line": 156, "column": 26}, "end": {"line": 156, "column": null}}, "42": {"start": {"line": 159, "column": 30}, "end": {"line": 169, "column": null}}, "43": {"start": {"line": 160, "column": 2}, "end": {"line": 162, "column": null}}, "44": {"start": {"line": 161, "column": 4}, "end": {"line": 161, "column": null}}, "45": {"start": {"line": 164, "column": 2}, "end": {"line": 166, "column": null}}, "46": {"start": {"line": 165, "column": 4}, "end": {"line": 165, "column": null}}, "47": {"start": {"line": 168, "column": 2}, "end": {"line": 168, "column": null}}, "48": {"start": {"line": 171, "column": 26}, "end": {"line": 173, "column": null}}, "49": {"start": {"line": 172, "column": 2}, "end": {"line": 172, "column": null}}, "50": {"start": {"line": 176, "column": 33}, "end": {"line": 180, "column": null}}, "51": {"start": {"line": 179, "column": 2}, "end": {"line": 179, "column": null}}, "52": {"start": {"line": 182, "column": 35}, "end": {"line": 190, "column": null}}, "53": {"start": {"line": 185, "column": 2}, "end": {"line": 188, "column": null}}, "54": {"start": {"line": 193, "column": 32}, "end": {"line": 203, "column": null}}, "55": {"start": {"line": 194, "column": 23}, "end": {"line": 194, "column": null}}, "56": {"start": {"line": 196, "column": 2}, "end": {"line": 200, "column": null}}, "57": {"start": {"line": 197, "column": 4}, "end": {"line": 199, "column": null}}, "58": {"start": {"line": 198, "column": 6}, "end": {"line": 198, "column": null}}, "59": {"start": {"line": 202, "column": 2}, "end": {"line": 202, "column": null}}, "60": {"start": {"line": 205, "column": 32}, "end": {"line": 214, "column": null}}, "61": {"start": {"line": 206, "column": 17}, "end": {"line": 206, "column": null}}, "62": {"start": {"line": 207, "column": 41}, "end": {"line": 207, "column": null}}, "63": {"start": {"line": 209, "column": 2}, "end": {"line": 211, "column": null}}, "64": {"start": {"line": 210, "column": 4}, "end": {"line": 210, "column": null}}, "65": {"start": {"line": 213, "column": 2}, "end": {"line": 213, "column": null}}, "66": {"start": {"line": 217, "column": 15}, "end": {"line": 217, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_10)", "decl": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": null}}, "loc": {"start": {"line": 10, "column": 4}, "end": {"line": 13, "column": null}}}, "1": {"name": "(anonymous_11)", "decl": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": 14}}, "loc": {"start": {"line": 38, "column": 46}, "end": {"line": 40, "column": null}}}, "2": {"name": "(anonymous_12)", "decl": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 16}}, "loc": {"start": {"line": 45, "column": 16}, "end": {"line": 100, "column": null}}}, "3": {"name": "(anonymous_13)", "decl": {"start": {"line": 102, "column": 10}, "end": {"line": 102, "column": 37}}, "loc": {"start": {"line": 102, "column": 37}, "end": {"line": 106, "column": null}}}, "4": {"name": "(anonymous_14)", "decl": {"start": {"line": 109, "column": 2}, "end": {"line": 109, "column": 8}}, "loc": {"start": {"line": 109, "column": 75}, "end": {"line": 121, "column": null}}}, "5": {"name": "(anonymous_15)", "decl": {"start": {"line": 113, "column": 37}, "end": {"line": 113, "column": 38}}, "loc": {"start": {"line": 113, "column": 50}, "end": {"line": 117, "column": null}}}, "6": {"name": "(anonymous_16)", "decl": {"start": {"line": 124, "column": 2}, "end": {"line": 124, "column": 8}}, "loc": {"start": {"line": 124, "column": 58}, "end": {"line": 129, "column": null}}}, "7": {"name": "(anonymous_17)", "decl": {"start": {"line": 132, "column": 2}, "end": {"line": 132, "column": 8}}, "loc": {"start": {"line": 132, "column": 57}, "end": {"line": 137, "column": null}}}, "8": {"name": "(anonymous_18)", "decl": {"start": {"line": 140, "column": 2}, "end": {"line": 140, "column": 8}}, "loc": {"start": {"line": 140, "column": 48}, "end": {"line": 144, "column": null}}}, "9": {"name": "(anonymous_19)", "decl": {"start": {"line": 147, "column": 2}, "end": {"line": 147, "column": 8}}, "loc": {"start": {"line": 147, "column": 59}, "end": {"line": 152, "column": null}}}, "10": {"name": "(anonymous_20)", "decl": {"start": {"line": 159, "column": 30}, "end": {"line": 159, "column": 31}}, "loc": {"start": {"line": 159, "column": 31}, "end": {"line": 169, "column": null}}}, "11": {"name": "(anonymous_21)", "decl": {"start": {"line": 171, "column": 26}, "end": {"line": 171, "column": 27}}, "loc": {"start": {"line": 171, "column": 27}, "end": {"line": 173, "column": null}}}, "12": {"name": "(anonymous_22)", "decl": {"start": {"line": 176, "column": 33}, "end": {"line": 176, "column": null}}, "loc": {"start": {"line": 177, "column": 2}, "end": {"line": 180, "column": null}}}, "13": {"name": "(anonymous_23)", "decl": {"start": {"line": 182, "column": 35}, "end": {"line": 182, "column": null}}, "loc": {"start": {"line": 183, "column": 2}, "end": {"line": 190, "column": null}}}, "14": {"name": "(anonymous_24)", "decl": {"start": {"line": 193, "column": 32}, "end": {"line": 193, "column": 33}}, "loc": {"start": {"line": 193, "column": 33}, "end": {"line": 203, "column": null}}}, "15": {"name": "(anonymous_25)", "decl": {"start": {"line": 196, "column": 33}, "end": {"line": 196, "column": 34}}, "loc": {"start": {"line": 196, "column": 46}, "end": {"line": 200, "column": null}}}, "16": {"name": "(anonymous_26)", "decl": {"start": {"line": 205, "column": 32}, "end": {"line": 205, "column": 33}}, "loc": {"start": {"line": 205, "column": 33}, "end": {"line": 214, "column": null}}}, "17": {"name": "(anonymous_27)", "decl": {"start": {"line": 209, "column": 17}, "end": {"line": 209, "column": 18}}, "loc": {"start": {"line": 209, "column": 25}, "end": {"line": 211, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 2, "column": 21}, "end": {"line": 2, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 2, "column": 21}, "end": {"line": 2, "column": 52}}, {"start": {"line": 2, "column": 56}, "end": {"line": 2, "column": null}}]}, "1": {"loc": {"start": {"line": 38, "column": 14}, "end": {"line": 38, "column": 44}}, "type": "default-arg", "locations": [{"start": {"line": 38, "column": 32}, "end": {"line": 38, "column": 44}}]}, "2": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 29}}, "type": "default-arg", "locations": [{"start": {"line": 44, "column": 27}, "end": {"line": 44, "column": 29}}]}, "3": {"loc": {"start": {"line": 54, "column": 4}, "end": {"line": 56, "column": null}}, "type": "if", "locations": [{"start": {"line": 54, "column": 4}, "end": {"line": 56, "column": null}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 70, "column": 6}, "end": {"line": 77, "column": null}}, "type": "if", "locations": [{"start": {"line": 70, "column": 6}, "end": {"line": 77, "column": null}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 72, "column": 10}, "end": {"line": 72, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 72, "column": 10}, "end": {"line": 72, "column": 20}}, {"start": {"line": 72, "column": 24}, "end": {"line": 72, "column": 49}}]}, "6": {"loc": {"start": {"line": 79, "column": 6}, "end": {"line": 86, "column": null}}, "type": "if", "locations": [{"start": {"line": 79, "column": 6}, "end": {"line": 86, "column": null}}, {"start": {}, "end": {}}]}, "7": {"loc": {"start": {"line": 81, "column": 10}, "end": {"line": 81, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 81, "column": 10}, "end": {"line": 81, "column": 20}}, {"start": {"line": 81, "column": 24}, "end": {"line": 81, "column": null}}]}, "8": {"loc": {"start": {"line": 90, "column": 6}, "end": {"line": 92, "column": null}}, "type": "if", "locations": [{"start": {"line": 90, "column": 6}, "end": {"line": 92, "column": null}}, {"start": {}, "end": {}}]}, "9": {"loc": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 96, "column": 33}, "end": {"line": 96, "column": 46}}, {"start": {"line": 96, "column": 49}, "end": {"line": 96, "column": null}}]}, "10": {"loc": {"start": {"line": 112, "column": 4}, "end": {"line": 118, "column": null}}, "type": "if", "locations": [{"start": {"line": 112, "column": 4}, "end": {"line": 118, "column": null}}, {"start": {}, "end": {}}]}, "11": {"loc": {"start": {"line": 114, "column": 8}, "end": {"line": 116, "column": null}}, "type": "if", "locations": [{"start": {"line": 114, "column": 8}, "end": {"line": 116, "column": null}}, {"start": {}, "end": {}}]}, "12": {"loc": {"start": {"line": 114, "column": 12}, "end": {"line": 114, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 114, "column": 12}, "end": {"line": 114, "column": 35}}, {"start": {"line": 114, "column": 35}, "end": {"line": 114, "column": 51}}]}, "13": {"loc": {"start": {"line": 127, "column": 12}, "end": {"line": 127, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 127, "column": 19}, "end": {"line": 127, "column": 42}}, {"start": {"line": 127, "column": 42}, "end": {"line": 127, "column": null}}]}, "14": {"loc": {"start": {"line": 135, "column": 12}, "end": {"line": 135, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 135, "column": 19}, "end": {"line": 135, "column": 42}}, {"start": {"line": 135, "column": 42}, "end": {"line": 135, "column": null}}]}, "15": {"loc": {"start": {"line": 150, "column": 12}, "end": {"line": 150, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 150, "column": 19}, "end": {"line": 150, "column": 42}}, {"start": {"line": 150, "column": 42}, "end": {"line": 150, "column": null}}]}, "16": {"loc": {"start": {"line": 160, "column": 2}, "end": {"line": 162, "column": null}}, "type": "if", "locations": [{"start": {"line": 160, "column": 2}, "end": {"line": 162, "column": null}}, {"start": {}, "end": {}}]}, "17": {"loc": {"start": {"line": 164, "column": 2}, "end": {"line": 166, "column": null}}, "type": "if", "locations": [{"start": {"line": 164, "column": 2}, "end": {"line": 166, "column": null}}, {"start": {}, "end": {}}]}, "18": {"loc": {"start": {"line": 179, "column": 9}, "end": {"line": 179, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 179, "column": 9}, "end": {"line": 179, "column": 21}}, {"start": {"line": 179, "column": 21}, "end": {"line": 179, "column": null}}]}, "19": {"loc": {"start": {"line": 186, "column": 4}, "end": {"line": 188, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 186, "column": 4}, "end": {"line": 186, "column": null}}, {"start": {"line": 187, "column": 4}, "end": {"line": 187, "column": 23}}, {"start": {"line": 188, "column": 4}, "end": {"line": 188, "column": null}}]}, "20": {"loc": {"start": {"line": 197, "column": 4}, "end": {"line": 199, "column": null}}, "type": "if", "locations": [{"start": {"line": 197, "column": 4}, "end": {"line": 199, "column": null}}, {"start": {}, "end": {}}]}, "21": {"loc": {"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 197, "column": 8}, "end": {"line": 197, "column": 31}}, {"start": {"line": 197, "column": 31}, "end": {"line": 197, "column": 49}}, {"start": {"line": 197, "column": 49}, "end": {"line": 197, "column": 63}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 0, "11": 0, "12": 1, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 1, "42": 1, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 1, "49": 0, "50": 1, "51": 0, "52": 1, "53": 0, "54": 1, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 1, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 1}, "f": {"0": 0, "1": 1, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "b": {"0": [1, 1], "1": [1], "2": [0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0, 0], "20": [0, 0], "21": [0, 0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\services\\permissionService.ts": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\services\\permissionService.ts", "statementMap": {"0": {"start": {"line": 19, "column": 13}, "end": {"line": 19, "column": null}}, "1": {"start": {"line": 327, "column": 0}, "end": {"line": 327, "column": 15}}, "2": {"start": {"line": 324, "column": 13}, "end": {"line": 324, "column": 33}}, "3": {"start": {"line": 2, "column": 27}, "end": {"line": 2, "column": null}}, "4": {"start": {"line": 26, "column": 4}, "end": {"line": 26, "column": null}}, "5": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": null}}, "6": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": null}}, "7": {"start": {"line": 47, "column": 4}, "end": {"line": 47, "column": null}}, "8": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": null}}, "9": {"start": {"line": 61, "column": 4}, "end": {"line": 61, "column": null}}, "10": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": null}}, "11": {"start": {"line": 78, "column": 4}, "end": {"line": 81, "column": null}}, "12": {"start": {"line": 92, "column": 4}, "end": {"line": 96, "column": null}}, "13": {"start": {"line": 103, "column": 4}, "end": {"line": 103, "column": null}}, "14": {"start": {"line": 110, "column": 4}, "end": {"line": 114, "column": null}}, "15": {"start": {"line": 121, "column": 4}, "end": {"line": 123, "column": null}}, "16": {"start": {"line": 130, "column": 4}, "end": {"line": 132, "column": null}}, "17": {"start": {"line": 139, "column": 4}, "end": {"line": 141, "column": null}}, "18": {"start": {"line": 148, "column": 4}, "end": {"line": 150, "column": null}}, "19": {"start": {"line": 157, "column": 4}, "end": {"line": 159, "column": null}}, "20": {"start": {"line": 166, "column": 4}, "end": {"line": 168, "column": null}}, "21": {"start": {"line": 175, "column": 4}, "end": {"line": 200, "column": null}}, "22": {"start": {"line": 176, "column": 23}, "end": {"line": 179, "column": null}}, "23": {"start": {"line": 181, "column": 6}, "end": {"line": 183, "column": null}}, "24": {"start": {"line": 182, "column": 8}, "end": {"line": 182, "column": null}}, "25": {"start": {"line": 186, "column": 25}, "end": {"line": 186, "column": null}}, "26": {"start": {"line": 186, "column": 58}, "end": {"line": 186, "column": null}}, "27": {"start": {"line": 187, "column": 6}, "end": {"line": 189, "column": null}}, "28": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": null}}, "29": {"start": {"line": 192, "column": 6}, "end": {"line": 194, "column": null}}, "30": {"start": {"line": 193, "column": 8}, "end": {"line": 193, "column": null}}, "31": {"start": {"line": 196, "column": 6}, "end": {"line": 196, "column": null}}, "32": {"start": {"line": 199, "column": 6}, "end": {"line": 199, "column": null}}, "33": {"start": {"line": 217, "column": 26}, "end": {"line": 217, "column": 70}}, "34": {"start": {"line": 219, "column": 4}, "end": {"line": 263, "column": null}}, "35": {"start": {"line": 220, "column": 26}, "end": {"line": 220, "column": null}}, "36": {"start": {"line": 222, "column": 6}, "end": {"line": 228, "column": null}}, "37": {"start": {"line": 223, "column": 8}, "end": {"line": 227, "column": null}}, "38": {"start": {"line": 230, "column": 26}, "end": {"line": 230, "column": null}}, "39": {"start": {"line": 232, "column": 6}, "end": {"line": 250, "column": null}}, "40": {"start": {"line": 233, "column": 36}, "end": {"line": 236, "column": null}}, "41": {"start": {"line": 238, "column": 24}, "end": {"line": 238, "column": 93}}, "42": {"start": {"line": 238, "column": 62}, "end": {"line": 238, "column": 91}}, "43": {"start": {"line": 241, "column": 8}, "end": {"line": 243, "column": null}}, "44": {"start": {"line": 242, "column": 10}, "end": {"line": 242, "column": null}}, "45": {"start": {"line": 242, "column": 44}, "end": {"line": 242, "column": null}}, "46": {"start": {"line": 245, "column": 8}, "end": {"line": 249, "column": null}}, "47": {"start": {"line": 252, "column": 6}, "end": {"line": 256, "column": null}}, "48": {"start": {"line": 258, "column": 6}, "end": {"line": 262, "column": null}}, "49": {"start": {"line": 284, "column": 27}, "end": {"line": 284, "column": null}}, "50": {"start": {"line": 285, "column": 24}, "end": {"line": 285, "column": 49}}, "51": {"start": {"line": 287, "column": 30}, "end": {"line": 287, "column": null}}, "52": {"start": {"line": 287, "column": 54}, "end": {"line": 287, "column": 64}}, "53": {"start": {"line": 288, "column": 32}, "end": {"line": 288, "column": null}}, "54": {"start": {"line": 288, "column": 56}, "end": {"line": 288, "column": 67}}, "55": {"start": {"line": 290, "column": 56}, "end": {"line": 290, "column": null}}, "56": {"start": {"line": 291, "column": 57}, "end": {"line": 291, "column": null}}, "57": {"start": {"line": 292, "column": 56}, "end": {"line": 292, "column": null}}, "58": {"start": {"line": 293, "column": 58}, "end": {"line": 293, "column": null}}, "59": {"start": {"line": 295, "column": 4}, "end": {"line": 300, "column": null}}, "60": {"start": {"line": 296, "column": 6}, "end": {"line": 296, "column": null}}, "61": {"start": {"line": 297, "column": 6}, "end": {"line": 297, "column": null}}, "62": {"start": {"line": 298, "column": 6}, "end": {"line": 298, "column": null}}, "63": {"start": {"line": 299, "column": 6}, "end": {"line": 299, "column": null}}, "64": {"start": {"line": 302, "column": 24}, "end": {"line": 302, "column": null}}, "65": {"start": {"line": 302, "column": 45}, "end": {"line": 302, "column": 64}}, "66": {"start": {"line": 303, "column": 29}, "end": {"line": 305, "column": null}}, "67": {"start": {"line": 304, "column": 39}, "end": {"line": 304, "column": 48}}, "68": {"start": {"line": 307, "column": 4}, "end": {"line": 319, "column": null}}, "69": {"start": {"line": 20, "column": 19}, "end": {"line": 20, "column": null}}, "70": {"start": {"line": 324, "column": 33}, "end": {"line": 324, "column": null}}, "71": {"start": {"line": 327, "column": 15}, "end": {"line": 327, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": 8}}, "loc": {"start": {"line": 25, "column": 88}, "end": {"line": 27, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": 8}}, "loc": {"start": {"line": 32, "column": 67}, "end": {"line": 34, "column": null}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": 8}}, "loc": {"start": {"line": 39, "column": 85}, "end": {"line": 41, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": 8}}, "loc": {"start": {"line": 46, "column": 97}, "end": {"line": 48, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 8}}, "loc": {"start": {"line": 53, "column": 85}, "end": {"line": 55, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": 8}}, "loc": {"start": {"line": 60, "column": 65}, "end": {"line": 62, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": 8}}, "loc": {"start": {"line": 67, "column": 71}, "end": {"line": 69, "column": null}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": 8}}, "loc": {"start": {"line": 77, "column": 5}, "end": {"line": 82, "column": null}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 8}}, "loc": {"start": {"line": 91, "column": 5}, "end": {"line": 97, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": 8}}, "loc": {"start": {"line": 102, "column": 77}, "end": {"line": 104, "column": null}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 109, "column": 2}, "end": {"line": 109, "column": 8}}, "loc": {"start": {"line": 109, "column": 94}, "end": {"line": 115, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 120, "column": 2}, "end": {"line": 120, "column": 8}}, "loc": {"start": {"line": 120, "column": 80}, "end": {"line": 124, "column": null}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 129, "column": 2}, "end": {"line": 129, "column": 8}}, "loc": {"start": {"line": 129, "column": 82}, "end": {"line": 133, "column": null}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 138, "column": 2}, "end": {"line": 138, "column": 8}}, "loc": {"start": {"line": 138, "column": 80}, "end": {"line": 142, "column": null}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 147, "column": 2}, "end": {"line": 147, "column": 8}}, "loc": {"start": {"line": 147, "column": 84}, "end": {"line": 151, "column": null}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 156, "column": 2}, "end": {"line": 156, "column": 8}}, "loc": {"start": {"line": 156, "column": 86}, "end": {"line": 160, "column": null}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 165, "column": 2}, "end": {"line": 165, "column": 8}}, "loc": {"start": {"line": 165, "column": 64}, "end": {"line": 169, "column": null}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 174, "column": 2}, "end": {"line": 174, "column": 8}}, "loc": {"start": {"line": 174, "column": 86}, "end": {"line": 201, "column": null}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 186, "column": 44}, "end": {"line": 186, "column": 58}}, "loc": {"start": {"line": 186, "column": 58}, "end": {"line": 186, "column": null}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 206, "column": 2}, "end": {"line": 206, "column": 8}}, "loc": {"start": {"line": 216, "column": 5}, "end": {"line": 264, "column": null}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 238, "column": 57}, "end": {"line": 238, "column": 62}}, "loc": {"start": {"line": 238, "column": 62}, "end": {"line": 238, "column": 91}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 242, "column": 39}, "end": {"line": 242, "column": 44}}, "loc": {"start": {"line": 242, "column": 44}, "end": {"line": 242, "column": null}}}, "22": {"name": "(anonymous_26)", "decl": {"start": {"line": 269, "column": 2}, "end": {"line": 269, "column": 8}}, "loc": {"start": {"line": 281, "column": 5}, "end": {"line": 320, "column": null}}}, "23": {"name": "(anonymous_27)", "decl": {"start": {"line": 287, "column": 49}, "end": {"line": 287, "column": 54}}, "loc": {"start": {"line": 287, "column": 54}, "end": {"line": 287, "column": 64}}}, "24": {"name": "(anonymous_28)", "decl": {"start": {"line": 288, "column": 51}, "end": {"line": 288, "column": 56}}, "loc": {"start": {"line": 288, "column": 56}, "end": {"line": 288, "column": 67}}}, "25": {"name": "(anonymous_29)", "decl": {"start": {"line": 295, "column": 24}, "end": {"line": 295, "column": null}}, "loc": {"start": {"line": 295, "column": 24}, "end": {"line": 300, "column": null}}}, "26": {"name": "(anonymous_30)", "decl": {"start": {"line": 302, "column": 40}, "end": {"line": 302, "column": 45}}, "loc": {"start": {"line": 302, "column": 45}, "end": {"line": 302, "column": 64}}}, "27": {"name": "(anonymous_31)", "decl": {"start": {"line": 304, "column": 27}, "end": {"line": 304, "column": 28}}, "loc": {"start": {"line": 304, "column": 39}, "end": {"line": 304, "column": 48}}}}, "branchMap": {"0": {"loc": {"start": {"line": 109, "column": 41}, "end": {"line": 109, "column": 59}}, "type": "default-arg", "locations": [{"start": {"line": 109, "column": 57}, "end": {"line": 109, "column": 59}}]}, "1": {"loc": {"start": {"line": 181, "column": 6}, "end": {"line": 183, "column": null}}, "type": "if", "locations": [{"start": {"line": 181, "column": 6}, "end": {"line": 183, "column": null}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 181, "column": 10}, "end": {"line": 181, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 181, "column": 10}, "end": {"line": 181, "column": 24}}, {"start": {"line": 181, "column": 28}, "end": {"line": 181, "column": 56}}]}, "3": {"loc": {"start": {"line": 187, "column": 6}, "end": {"line": 189, "column": null}}, "type": "if", "locations": [{"start": {"line": 187, "column": 6}, "end": {"line": 189, "column": null}}, {"start": {}, "end": {}}]}, "4": {"loc": {"start": {"line": 192, "column": 6}, "end": {"line": 194, "column": null}}, "type": "if", "locations": [{"start": {"line": 192, "column": 6}, "end": {"line": 194, "column": null}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 222, "column": 6}, "end": {"line": 228, "column": null}}, "type": "if", "locations": [{"start": {"line": 222, "column": 6}, "end": {"line": 228, "column": null}}, {"start": {}, "end": {}}]}, "6": {"loc": {"start": {"line": 232, "column": 6}, "end": {"line": 250, "column": null}}, "type": "if", "locations": [{"start": {"line": 232, "column": 6}, "end": {"line": 250, "column": null}}, {"start": {}, "end": {}}]}, "7": {"loc": {"start": {"line": 238, "column": 24}, "end": {"line": 238, "column": 93}}, "type": "binary-expr", "locations": [{"start": {"line": 238, "column": 24}, "end": {"line": 238, "column": 91}}, {"start": {"line": 238, "column": 91}, "end": {"line": 238, "column": 93}}]}, "8": {"loc": {"start": {"line": 241, "column": 8}, "end": {"line": 243, "column": null}}, "type": "if", "locations": [{"start": {"line": 241, "column": 8}, "end": {"line": 243, "column": null}}, {"start": {}, "end": {}}]}, "9": {"loc": {"start": {"line": 285, "column": 24}, "end": {"line": 285, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 285, "column": 24}, "end": {"line": 285, "column": 43}}, {"start": {"line": 285, "column": 47}, "end": {"line": 285, "column": 49}}]}, "10": {"loc": {"start": {"line": 296, "column": 48}, "end": {"line": 296, "column": 95}}, "type": "binary-expr", "locations": [{"start": {"line": 296, "column": 48}, "end": {"line": 296, "column": 86}}, {"start": {"line": 296, "column": 90}, "end": {"line": 296, "column": 95}}]}, "11": {"loc": {"start": {"line": 297, "column": 50}, "end": {"line": 297, "column": 99}}, "type": "binary-expr", "locations": [{"start": {"line": 297, "column": 50}, "end": {"line": 297, "column": 90}}, {"start": {"line": 297, "column": 94}, "end": {"line": 297, "column": 99}}]}, "12": {"loc": {"start": {"line": 298, "column": 48}, "end": {"line": 298, "column": 95}}, "type": "binary-expr", "locations": [{"start": {"line": 298, "column": 48}, "end": {"line": 298, "column": 86}}, {"start": {"line": 298, "column": 90}, "end": {"line": 298, "column": 95}}]}, "13": {"loc": {"start": {"line": 299, "column": 52}, "end": {"line": 299, "column": 103}}, "type": "binary-expr", "locations": [{"start": {"line": 299, "column": 52}, "end": {"line": 299, "column": 94}}, {"start": {"line": 299, "column": 98}, "end": {"line": 299, "column": 103}}]}, "14": {"loc": {"start": {"line": 303, "column": 29}, "end": {"line": 305, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 304, "column": 8}, "end": {"line": 304, "column": 71}}, {"start": {"line": 305, "column": 8}, "end": {"line": 305, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\services\\roleService.ts": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\services\\roleService.ts", "statementMap": {"0": {"start": {"line": 16, "column": 13}, "end": {"line": 16, "column": null}}, "1": {"start": {"line": 245, "column": 0}, "end": {"line": 245, "column": 15}}, "2": {"start": {"line": 242, "column": 13}, "end": {"line": 242, "column": 27}}, "3": {"start": {"line": 2, "column": 46}, "end": {"line": 2, "column": null}}, "4": {"start": {"line": 23, "column": 4}, "end": {"line": 23, "column": null}}, "5": {"start": {"line": 30, "column": 4}, "end": {"line": 30, "column": null}}, "6": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": null}}, "7": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": null}}, "8": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": null}}, "9": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": null}}, "10": {"start": {"line": 72, "column": 4}, "end": {"line": 79, "column": null}}, "11": {"start": {"line": 89, "column": 4}, "end": {"line": 92, "column": null}}, "12": {"start": {"line": 99, "column": 4}, "end": {"line": 103, "column": null}}, "13": {"start": {"line": 113, "column": 4}, "end": {"line": 116, "column": null}}, "14": {"start": {"line": 123, "column": 4}, "end": {"line": 125, "column": null}}, "15": {"start": {"line": 132, "column": 4}, "end": {"line": 135, "column": null}}, "16": {"start": {"line": 142, "column": 4}, "end": {"line": 161, "column": null}}, "17": {"start": {"line": 143, "column": 23}, "end": {"line": 146, "column": null}}, "18": {"start": {"line": 148, "column": 6}, "end": {"line": 150, "column": null}}, "19": {"start": {"line": 149, "column": 8}, "end": {"line": 149, "column": null}}, "20": {"start": {"line": 153, "column": 6}, "end": {"line": 155, "column": null}}, "21": {"start": {"line": 154, "column": 8}, "end": {"line": 154, "column": null}}, "22": {"start": {"line": 154, "column": 43}, "end": {"line": 154, "column": null}}, "23": {"start": {"line": 157, "column": 6}, "end": {"line": 157, "column": null}}, "24": {"start": {"line": 160, "column": 6}, "end": {"line": 160, "column": null}}, "25": {"start": {"line": 171, "column": 4}, "end": {"line": 190, "column": null}}, "26": {"start": {"line": 172, "column": 23}, "end": {"line": 172, "column": null}}, "27": {"start": {"line": 174, "column": 22}, "end": {"line": 174, "column": 41}}, "28": {"start": {"line": 177, "column": 6}, "end": {"line": 179, "column": null}}, "29": {"start": {"line": 178, "column": 8}, "end": {"line": 178, "column": null}}, "30": {"start": {"line": 178, "column": 45}, "end": {"line": 178, "column": null}}, "31": {"start": {"line": 181, "column": 6}, "end": {"line": 184, "column": null}}, "32": {"start": {"line": 186, "column": 6}, "end": {"line": 189, "column": null}}, "33": {"start": {"line": 212, "column": 21}, "end": {"line": 212, "column": null}}, "34": {"start": {"line": 213, "column": 18}, "end": {"line": 213, "column": 37}}, "35": {"start": {"line": 215, "column": 24}, "end": {"line": 215, "column": null}}, "36": {"start": {"line": 215, "column": 45}, "end": {"line": 215, "column": 58}}, "37": {"start": {"line": 216, "column": 26}, "end": {"line": 216, "column": null}}, "38": {"start": {"line": 216, "column": 47}, "end": {"line": 216, "column": 61}}, "39": {"start": {"line": 217, "column": 27}, "end": {"line": 217, "column": null}}, "40": {"start": {"line": 217, "column": 48}, "end": {"line": 217, "column": null}}, "41": {"start": {"line": 219, "column": 24}, "end": {"line": 219, "column": null}}, "42": {"start": {"line": 219, "column": 42}, "end": {"line": 219, "column": 56}}, "43": {"start": {"line": 220, "column": 29}, "end": {"line": 222, "column": null}}, "44": {"start": {"line": 221, "column": 39}, "end": {"line": 221, "column": 48}}, "45": {"start": {"line": 224, "column": 4}, "end": {"line": 237, "column": null}}, "46": {"start": {"line": 17, "column": 19}, "end": {"line": 17, "column": null}}, "47": {"start": {"line": 242, "column": 27}, "end": {"line": 242, "column": null}}, "48": {"start": {"line": 245, "column": 15}, "end": {"line": 245, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 8}}, "loc": {"start": {"line": 22, "column": 70}, "end": {"line": 24, "column": null}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": 8}}, "loc": {"start": {"line": 29, "column": 55}, "end": {"line": 31, "column": null}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": 8}}, "loc": {"start": {"line": 36, "column": 67}, "end": {"line": 38, "column": null}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 8}}, "loc": {"start": {"line": 43, "column": 79}, "end": {"line": 45, "column": null}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 50, "column": 2}, "end": {"line": 50, "column": 8}}, "loc": {"start": {"line": 50, "column": 79}, "end": {"line": 52, "column": null}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 8}}, "loc": {"start": {"line": 57, "column": 59}, "end": {"line": 59, "column": null}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 64, "column": 2}, "end": {"line": 64, "column": 8}}, "loc": {"start": {"line": 71, "column": 5}, "end": {"line": 80, "column": null}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 8}}, "loc": {"start": {"line": 88, "column": 5}, "end": {"line": 93, "column": null}}}, "8": {"name": "(anonymous_12)", "decl": {"start": {"line": 98, "column": 2}, "end": {"line": 98, "column": 8}}, "loc": {"start": {"line": 98, "column": 82}, "end": {"line": 104, "column": null}}}, "9": {"name": "(anonymous_13)", "decl": {"start": {"line": 109, "column": 2}, "end": {"line": 109, "column": 8}}, "loc": {"start": {"line": 112, "column": 31}, "end": {"line": 117, "column": null}}}, "10": {"name": "(anonymous_14)", "decl": {"start": {"line": 122, "column": 2}, "end": {"line": 122, "column": 8}}, "loc": {"start": {"line": 122, "column": 52}, "end": {"line": 126, "column": null}}}, "11": {"name": "(anonymous_15)", "decl": {"start": {"line": 131, "column": 2}, "end": {"line": 131, "column": 8}}, "loc": {"start": {"line": 131, "column": 92}, "end": {"line": 136, "column": null}}}, "12": {"name": "(anonymous_16)", "decl": {"start": {"line": 141, "column": 2}, "end": {"line": 141, "column": 8}}, "loc": {"start": {"line": 141, "column": 80}, "end": {"line": 162, "column": null}}}, "13": {"name": "(anonymous_17)", "decl": {"start": {"line": 154, "column": 35}, "end": {"line": 154, "column": 43}}, "loc": {"start": {"line": 154, "column": 43}, "end": {"line": 154, "column": null}}}, "14": {"name": "(anonymous_18)", "decl": {"start": {"line": 167, "column": 2}, "end": {"line": 167, "column": 8}}, "loc": {"start": {"line": 170, "column": 5}, "end": {"line": 191, "column": null}}}, "15": {"name": "(anonymous_19)", "decl": {"start": {"line": 178, "column": 37}, "end": {"line": 178, "column": 45}}, "loc": {"start": {"line": 178, "column": 45}, "end": {"line": 178, "column": null}}}, "16": {"name": "(anonymous_20)", "decl": {"start": {"line": 196, "column": 2}, "end": {"line": 196, "column": 8}}, "loc": {"start": {"line": 209, "column": 5}, "end": {"line": 238, "column": null}}}, "17": {"name": "(anonymous_21)", "decl": {"start": {"line": 215, "column": 37}, "end": {"line": 215, "column": 45}}, "loc": {"start": {"line": 215, "column": 45}, "end": {"line": 215, "column": 58}}}, "18": {"name": "(anonymous_22)", "decl": {"start": {"line": 216, "column": 39}, "end": {"line": 216, "column": 47}}, "loc": {"start": {"line": 216, "column": 47}, "end": {"line": 216, "column": 61}}}, "19": {"name": "(anonymous_23)", "decl": {"start": {"line": 217, "column": 40}, "end": {"line": 217, "column": 48}}, "loc": {"start": {"line": 217, "column": 48}, "end": {"line": 217, "column": null}}}, "20": {"name": "(anonymous_24)", "decl": {"start": {"line": 219, "column": 34}, "end": {"line": 219, "column": 42}}, "loc": {"start": {"line": 219, "column": 42}, "end": {"line": 219, "column": 56}}}, "21": {"name": "(anonymous_25)", "decl": {"start": {"line": 221, "column": 27}, "end": {"line": 221, "column": 28}}, "loc": {"start": {"line": 221, "column": 39}, "end": {"line": 221, "column": 48}}}}, "branchMap": {"0": {"loc": {"start": {"line": 98, "column": 35}, "end": {"line": 98, "column": 53}}, "type": "default-arg", "locations": [{"start": {"line": 98, "column": 51}, "end": {"line": 98, "column": 53}}]}, "1": {"loc": {"start": {"line": 131, "column": 34}, "end": {"line": 131, "column": 63}}, "type": "default-arg", "locations": [{"start": {"line": 131, "column": 58}, "end": {"line": 131, "column": 63}}]}, "2": {"loc": {"start": {"line": 148, "column": 6}, "end": {"line": 150, "column": null}}, "type": "if", "locations": [{"start": {"line": 148, "column": 6}, "end": {"line": 150, "column": null}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 148, "column": 10}, "end": {"line": 148, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 148, "column": 10}, "end": {"line": 148, "column": 24}}, {"start": {"line": 148, "column": 28}, "end": {"line": 148, "column": 56}}]}, "4": {"loc": {"start": {"line": 153, "column": 6}, "end": {"line": 155, "column": null}}, "type": "if", "locations": [{"start": {"line": 153, "column": 6}, "end": {"line": 155, "column": null}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 174, "column": 22}, "end": {"line": 174, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 174, "column": 22}, "end": {"line": 174, "column": 35}}, {"start": {"line": 174, "column": 39}, "end": {"line": 174, "column": 41}}]}, "6": {"loc": {"start": {"line": 177, "column": 6}, "end": {"line": 179, "column": null}}, "type": "if", "locations": [{"start": {"line": 177, "column": 6}, "end": {"line": 179, "column": null}}, {"start": {}, "end": {}}]}, "7": {"loc": {"start": {"line": 213, "column": 18}, "end": {"line": 213, "column": 37}}, "type": "binary-expr", "locations": [{"start": {"line": 213, "column": 18}, "end": {"line": 213, "column": 31}}, {"start": {"line": 213, "column": 35}, "end": {"line": 213, "column": 37}}]}, "8": {"loc": {"start": {"line": 217, "column": 48}, "end": {"line": 217, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 217, "column": 48}, "end": {"line": 217, "column": 59}}, {"start": {"line": 217, "column": 63}, "end": {"line": 217, "column": null}}]}, "9": {"loc": {"start": {"line": 220, "column": 29}, "end": {"line": 222, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 221, "column": 8}, "end": {"line": 221, "column": 71}}, {"start": {"line": 222, "column": 8}, "end": {"line": 222, "column": null}}]}, "10": {"loc": {"start": {"line": 233, "column": 15}, "end": {"line": 233, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 233, "column": 40}, "end": {"line": 233, "column": 67}}, {"start": {"line": 233, "column": 67}, "end": {"line": 233, "column": null}}]}, "11": {"loc": {"start": {"line": 234, "column": 15}, "end": {"line": 234, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 234, "column": 40}, "end": {"line": 234, "column": 67}}, {"start": {"line": 234, "column": 67}, "end": {"line": 234, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 1, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 1, "47": 1, "48": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0}, "b": {"0": [0], "1": [0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\stores\\permissionStore.ts": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\stores\\permissionStore.ts", "statementMap": {"0": {"start": {"line": 90, "column": 13}, "end": {"line": 90, "column": 34}}, "1": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": null}}, "3": {"start": {"line": 3, "column": 34}, "end": {"line": 3, "column": null}}, "4": {"start": {"line": 72, "column": 21}, "end": {"line": 88, "column": null}}, "5": {"start": {"line": 90, "column": 34}, "end": {"line": 358, "column": null}}, "6": {"start": {"line": 92, "column": 19}, "end": {"line": 355, "column": null}}, "7": {"start": {"line": 96, "column": 8}, "end": {"line": 96, "column": null}}, "8": {"start": {"line": 97, "column": 8}, "end": {"line": 114, "column": null}}, "9": {"start": {"line": 98, "column": 30}, "end": {"line": 98, "column": null}}, "10": {"start": {"line": 99, "column": 27}, "end": {"line": 99, "column": null}}, "11": {"start": {"line": 101, "column": 10}, "end": {"line": 108, "column": null}}, "12": {"start": {"line": 110, "column": 10}, "end": {"line": 113, "column": null}}, "13": {"start": {"line": 118, "column": 8}, "end": {"line": 118, "column": null}}, "14": {"start": {"line": 119, "column": 8}, "end": {"line": 127, "column": null}}, "15": {"start": {"line": 120, "column": 29}, "end": {"line": 120, "column": null}}, "16": {"start": {"line": 121, "column": 10}, "end": {"line": 121, "column": null}}, "17": {"start": {"line": 123, "column": 10}, "end": {"line": 126, "column": null}}, "18": {"start": {"line": 131, "column": 8}, "end": {"line": 131, "column": null}}, "19": {"start": {"line": 132, "column": 8}, "end": {"line": 140, "column": null}}, "20": {"start": {"line": 133, "column": 25}, "end": {"line": 133, "column": null}}, "21": {"start": {"line": 134, "column": 10}, "end": {"line": 134, "column": null}}, "22": {"start": {"line": 136, "column": 10}, "end": {"line": 139, "column": null}}, "23": {"start": {"line": 144, "column": 8}, "end": {"line": 144, "column": null}}, "24": {"start": {"line": 145, "column": 8}, "end": {"line": 153, "column": null}}, "25": {"start": {"line": 146, "column": 28}, "end": {"line": 146, "column": null}}, "26": {"start": {"line": 147, "column": 10}, "end": {"line": 147, "column": null}}, "27": {"start": {"line": 149, "column": 10}, "end": {"line": 152, "column": null}}, "28": {"start": {"line": 157, "column": 8}, "end": {"line": 164, "column": null}}, "29": {"start": {"line": 158, "column": 29}, "end": {"line": 158, "column": null}}, "30": {"start": {"line": 159, "column": 10}, "end": {"line": 159, "column": null}}, "31": {"start": {"line": 161, "column": 10}, "end": {"line": 163, "column": null}}, "32": {"start": {"line": 168, "column": 8}, "end": {"line": 168, "column": null}}, "33": {"start": {"line": 169, "column": 8}, "end": {"line": 186, "column": null}}, "34": {"start": {"line": 170, "column": 32}, "end": {"line": 170, "column": null}}, "35": {"start": {"line": 173, "column": 10}, "end": {"line": 177, "column": null}}, "36": {"start": {"line": 173, "column": 24}, "end": {"line": 177, "column": null}}, "37": {"start": {"line": 179, "column": 10}, "end": {"line": 179, "column": null}}, "38": {"start": {"line": 181, "column": 10}, "end": {"line": 184, "column": null}}, "39": {"start": {"line": 185, "column": 10}, "end": {"line": 185, "column": null}}, "40": {"start": {"line": 190, "column": 8}, "end": {"line": 190, "column": null}}, "41": {"start": {"line": 191, "column": 8}, "end": {"line": 210, "column": null}}, "42": {"start": {"line": 192, "column": 36}, "end": {"line": 192, "column": null}}, "43": {"start": {"line": 195, "column": 10}, "end": {"line": 201, "column": null}}, "44": {"start": {"line": 195, "column": 24}, "end": {"line": 201, "column": null}}, "45": {"start": {"line": 197, "column": 14}, "end": {"line": 197, "column": null}}, "46": {"start": {"line": 203, "column": 10}, "end": {"line": 203, "column": null}}, "47": {"start": {"line": 205, "column": 10}, "end": {"line": 208, "column": null}}, "48": {"start": {"line": 209, "column": 10}, "end": {"line": 209, "column": null}}, "49": {"start": {"line": 214, "column": 8}, "end": {"line": 214, "column": null}}, "50": {"start": {"line": 215, "column": 8}, "end": {"line": 231, "column": null}}, "51": {"start": {"line": 216, "column": 10}, "end": {"line": 216, "column": null}}, "52": {"start": {"line": 219, "column": 10}, "end": {"line": 224, "column": null}}, "53": {"start": {"line": 219, "column": 24}, "end": {"line": 224, "column": null}}, "54": {"start": {"line": 220, "column": 64}, "end": {"line": 220, "column": null}}, "55": {"start": {"line": 226, "column": 10}, "end": {"line": 229, "column": null}}, "56": {"start": {"line": 230, "column": 10}, "end": {"line": 230, "column": null}}, "57": {"start": {"line": 235, "column": 8}, "end": {"line": 235, "column": null}}, "58": {"start": {"line": 236, "column": 8}, "end": {"line": 252, "column": null}}, "59": {"start": {"line": 237, "column": 10}, "end": {"line": 237, "column": null}}, "60": {"start": {"line": 240, "column": 10}, "end": {"line": 245, "column": null}}, "61": {"start": {"line": 240, "column": 24}, "end": {"line": 245, "column": null}}, "62": {"start": {"line": 241, "column": 64}, "end": {"line": 241, "column": null}}, "63": {"start": {"line": 247, "column": 10}, "end": {"line": 250, "column": null}}, "64": {"start": {"line": 251, "column": 10}, "end": {"line": 251, "column": null}}, "65": {"start": {"line": 256, "column": 8}, "end": {"line": 256, "column": null}}, "66": {"start": {"line": 257, "column": 8}, "end": {"line": 274, "column": null}}, "67": {"start": {"line": 258, "column": 33}, "end": {"line": 258, "column": null}}, "68": {"start": {"line": 261, "column": 10}, "end": {"line": 265, "column": null}}, "69": {"start": {"line": 261, "column": 24}, "end": {"line": 265, "column": null}}, "70": {"start": {"line": 267, "column": 10}, "end": {"line": 267, "column": null}}, "71": {"start": {"line": 269, "column": 10}, "end": {"line": 272, "column": null}}, "72": {"start": {"line": 273, "column": 10}, "end": {"line": 273, "column": null}}, "73": {"start": {"line": 278, "column": 8}, "end": {"line": 285, "column": null}}, "74": {"start": {"line": 279, "column": 10}, "end": {"line": 279, "column": null}}, "75": {"start": {"line": 281, "column": 10}, "end": {"line": 283, "column": null}}, "76": {"start": {"line": 284, "column": 10}, "end": {"line": 284, "column": null}}, "77": {"start": {"line": 294, "column": 8}, "end": {"line": 294, "column": null}}, "78": {"start": {"line": 295, "column": 8}, "end": {"line": 314, "column": null}}, "79": {"start": {"line": 296, "column": 30}, "end": {"line": 297, "column": null}}, "80": {"start": {"line": 301, "column": 10}, "end": {"line": 305, "column": null}}, "81": {"start": {"line": 301, "column": 24}, "end": {"line": 305, "column": null}}, "82": {"start": {"line": 307, "column": 10}, "end": {"line": 307, "column": null}}, "83": {"start": {"line": 309, "column": 10}, "end": {"line": 312, "column": null}}, "84": {"start": {"line": 313, "column": 10}, "end": {"line": 313, "column": null}}, "85": {"start": {"line": 318, "column": 8}, "end": {"line": 318, "column": null}}, "86": {"start": {"line": 322, "column": 8}, "end": {"line": 325, "column": null}}, "87": {"start": {"line": 322, "column": 22}, "end": {"line": 325, "column": null}}, "88": {"start": {"line": 329, "column": 8}, "end": {"line": 329, "column": null}}, "89": {"start": {"line": 333, "column": 8}, "end": {"line": 333, "column": null}}, "90": {"start": {"line": 337, "column": 8}, "end": {"line": 337, "column": null}}, "91": {"start": {"line": 341, "column": 8}, "end": {"line": 341, "column": null}}, "92": {"start": {"line": 345, "column": 8}, "end": {"line": 345, "column": null}}, "93": {"start": {"line": 349, "column": 8}, "end": {"line": 349, "column": null}}, "94": {"start": {"line": 353, "column": 8}, "end": {"line": 353, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 92, "column": 4}, "end": {"line": 92, "column": 5}}, "loc": {"start": {"line": 92, "column": 19}, "end": {"line": 355, "column": null}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 95, "column": 24}, "end": {"line": 95, "column": 31}}, "loc": {"start": {"line": 95, "column": 31}, "end": {"line": 115, "column": null}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 117, "column": 27}, "end": {"line": 117, "column": 34}}, "loc": {"start": {"line": 117, "column": 34}, "end": {"line": 128, "column": null}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 130, "column": 29}, "end": {"line": 130, "column": null}}, "loc": {"start": {"line": 130, "column": 29}, "end": {"line": 141, "column": null}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 143, "column": 32}, "end": {"line": 143, "column": null}}, "loc": {"start": {"line": 143, "column": 32}, "end": {"line": 154, "column": null}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 156, "column": 33}, "end": {"line": 156, "column": null}}, "loc": {"start": {"line": 156, "column": 33}, "end": {"line": 165, "column": null}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 167, "column": 24}, "end": {"line": 167, "column": 31}}, "loc": {"start": {"line": 167, "column": 31}, "end": {"line": 187, "column": null}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 173, "column": 14}, "end": {"line": 173, "column": 24}}, "loc": {"start": {"line": 173, "column": 24}, "end": {"line": 177, "column": null}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 189, "column": 24}, "end": {"line": 189, "column": 31}}, "loc": {"start": {"line": 189, "column": 43}, "end": {"line": 211, "column": null}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 195, "column": 14}, "end": {"line": 195, "column": 24}}, "loc": {"start": {"line": 195, "column": 24}, "end": {"line": 201, "column": null}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 196, "column": 47}, "end": {"line": 196, "column": null}}, "loc": {"start": {"line": 197, "column": 14}, "end": {"line": 197, "column": null}}}, "11": {"name": "(anonymous_12)", "decl": {"start": {"line": 213, "column": 24}, "end": {"line": 213, "column": 31}}, "loc": {"start": {"line": 213, "column": 31}, "end": {"line": 232, "column": null}}}, "12": {"name": "(anonymous_13)", "decl": {"start": {"line": 219, "column": 14}, "end": {"line": 219, "column": 24}}, "loc": {"start": {"line": 219, "column": 24}, "end": {"line": 224, "column": null}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 220, "column": 50}, "end": {"line": 220, "column": 64}}, "loc": {"start": {"line": 220, "column": 64}, "end": {"line": 220, "column": null}}}, "14": {"name": "(anonymous_15)", "decl": {"start": {"line": 234, "column": 29}, "end": {"line": 234, "column": 36}}, "loc": {"start": {"line": 234, "column": 36}, "end": {"line": 253, "column": null}}}, "15": {"name": "(anonymous_16)", "decl": {"start": {"line": 240, "column": 14}, "end": {"line": 240, "column": 24}}, "loc": {"start": {"line": 240, "column": 24}, "end": {"line": 245, "column": null}}}, "16": {"name": "(anonymous_17)", "decl": {"start": {"line": 241, "column": 50}, "end": {"line": 241, "column": 64}}, "loc": {"start": {"line": 241, "column": 64}, "end": {"line": 241, "column": null}}}, "17": {"name": "(anonymous_18)", "decl": {"start": {"line": 255, "column": 29}, "end": {"line": 255, "column": 36}}, "loc": {"start": {"line": 255, "column": 36}, "end": {"line": 275, "column": null}}}, "18": {"name": "(anonymous_19)", "decl": {"start": {"line": 261, "column": 14}, "end": {"line": 261, "column": 24}}, "loc": {"start": {"line": 261, "column": 24}, "end": {"line": 265, "column": null}}}, "19": {"name": "(anonymous_20)", "decl": {"start": {"line": 277, "column": 26}, "end": {"line": 277, "column": 33}}, "loc": {"start": {"line": 277, "column": 33}, "end": {"line": 286, "column": null}}}, "20": {"name": "(anonymous_21)", "decl": {"start": {"line": 288, "column": 37}, "end": {"line": 288, "column": null}}, "loc": {"start": {"line": 292, "column": 8}, "end": {"line": 315, "column": null}}}, "21": {"name": "(anonymous_22)", "decl": {"start": {"line": 301, "column": 14}, "end": {"line": 301, "column": 24}}, "loc": {"start": {"line": 301, "column": 24}, "end": {"line": 305, "column": null}}}, "22": {"name": "(anonymous_23)", "decl": {"start": {"line": 317, "column": 29}, "end": {"line": 317, "column": 30}}, "loc": {"start": {"line": 317, "column": 30}, "end": {"line": 319, "column": null}}}, "23": {"name": "(anonymous_24)", "decl": {"start": {"line": 321, "column": 18}, "end": {"line": 321, "column": 19}}, "loc": {"start": {"line": 321, "column": 19}, "end": {"line": 326, "column": null}}}, "24": {"name": "(anonymous_25)", "decl": {"start": {"line": 322, "column": 12}, "end": {"line": 322, "column": 22}}, "loc": {"start": {"line": 322, "column": 22}, "end": {"line": 325, "column": null}}}, "25": {"name": "(anonymous_26)", "decl": {"start": {"line": 328, "column": 15}, "end": {"line": 328, "column": 16}}, "loc": {"start": {"line": 328, "column": 16}, "end": {"line": 330, "column": null}}}, "26": {"name": "(anonymous_27)", "decl": {"start": {"line": 332, "column": 19}, "end": {"line": 332, "column": 20}}, "loc": {"start": {"line": 332, "column": 20}, "end": {"line": 334, "column": null}}}, "27": {"name": "(anonymous_28)", "decl": {"start": {"line": 336, "column": 26}, "end": {"line": 336, "column": 27}}, "loc": {"start": {"line": 336, "column": 27}, "end": {"line": 338, "column": null}}}, "28": {"name": "(anonymous_29)", "decl": {"start": {"line": 340, "column": 27}, "end": {"line": 340, "column": 28}}, "loc": {"start": {"line": 340, "column": 28}, "end": {"line": 342, "column": null}}}, "29": {"name": "(anonymous_30)", "decl": {"start": {"line": 344, "column": 26}, "end": {"line": 344, "column": 27}}, "loc": {"start": {"line": 344, "column": 27}, "end": {"line": 346, "column": null}}}, "30": {"name": "(anonymous_31)", "decl": {"start": {"line": 348, "column": 18}, "end": {"line": 348, "column": null}}, "loc": {"start": {"line": 348, "column": 18}, "end": {"line": 350, "column": null}}}, "31": {"name": "(anonymous_32)", "decl": {"start": {"line": 352, "column": 13}, "end": {"line": 352, "column": null}}, "loc": {"start": {"line": 352, "column": 13}, "end": {"line": 354, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 111, "column": 19}, "end": {"line": 111, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 111, "column": 44}, "end": {"line": 111, "column": 57}}, {"start": {"line": 111, "column": 60}, "end": {"line": 111, "column": null}}]}, "1": {"loc": {"start": {"line": 124, "column": 19}, "end": {"line": 124, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 124, "column": 44}, "end": {"line": 124, "column": 57}}, {"start": {"line": 124, "column": 60}, "end": {"line": 124, "column": null}}]}, "2": {"loc": {"start": {"line": 137, "column": 19}, "end": {"line": 137, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 137, "column": 44}, "end": {"line": 137, "column": 57}}, {"start": {"line": 137, "column": 60}, "end": {"line": 137, "column": null}}]}, "3": {"loc": {"start": {"line": 150, "column": 19}, "end": {"line": 150, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 150, "column": 44}, "end": {"line": 150, "column": 57}}, {"start": {"line": 150, "column": 60}, "end": {"line": 150, "column": null}}]}, "4": {"loc": {"start": {"line": 162, "column": 19}, "end": {"line": 162, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 162, "column": 44}, "end": {"line": 162, "column": 57}}, {"start": {"line": 162, "column": 60}, "end": {"line": 162, "column": null}}]}, "5": {"loc": {"start": {"line": 182, "column": 19}, "end": {"line": 182, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 182, "column": 44}, "end": {"line": 182, "column": 57}}, {"start": {"line": 182, "column": 60}, "end": {"line": 182, "column": null}}]}, "6": {"loc": {"start": {"line": 197, "column": 14}, "end": {"line": 197, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 197, "column": 37}, "end": {"line": 197, "column": 57}}, {"start": {"line": 197, "column": 57}, "end": {"line": 197, "column": null}}]}, "7": {"loc": {"start": {"line": 199, "column": 32}, "end": {"line": 199, "column": 114}}, "type": "cond-expr", "locations": [{"start": {"line": 199, "column": 70}, "end": {"line": 199, "column": 90}}, {"start": {"line": 199, "column": 90}, "end": {"line": 199, "column": 114}}]}, "8": {"loc": {"start": {"line": 206, "column": 19}, "end": {"line": 206, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 206, "column": 44}, "end": {"line": 206, "column": 57}}, {"start": {"line": 206, "column": 60}, "end": {"line": 206, "column": null}}]}, "9": {"loc": {"start": {"line": 221, "column": 32}, "end": {"line": 221, "column": 101}}, "type": "cond-expr", "locations": [{"start": {"line": 221, "column": 70}, "end": {"line": 221, "column": 77}}, {"start": {"line": 221, "column": 77}, "end": {"line": 221, "column": 101}}]}, "10": {"loc": {"start": {"line": 227, "column": 19}, "end": {"line": 227, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 227, "column": 44}, "end": {"line": 227, "column": 57}}, {"start": {"line": 227, "column": 60}, "end": {"line": 227, "column": null}}]}, "11": {"loc": {"start": {"line": 242, "column": 32}, "end": {"line": 242, "column": 114}}, "type": "cond-expr", "locations": [{"start": {"line": 242, "column": 83}, "end": {"line": 242, "column": 90}}, {"start": {"line": 242, "column": 90}, "end": {"line": 242, "column": 114}}]}, "12": {"loc": {"start": {"line": 242, "column": 45}, "end": {"line": 242, "column": 83}}, "type": "binary-expr", "locations": [{"start": {"line": 242, "column": 45}, "end": {"line": 242, "column": 77}}, {"start": {"line": 242, "column": 77}, "end": {"line": 242, "column": 83}}]}, "13": {"loc": {"start": {"line": 248, "column": 19}, "end": {"line": 248, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 248, "column": 44}, "end": {"line": 248, "column": 57}}, {"start": {"line": 248, "column": 60}, "end": {"line": 248, "column": null}}]}, "14": {"loc": {"start": {"line": 270, "column": 19}, "end": {"line": 270, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 270, "column": 44}, "end": {"line": 270, "column": 57}}, {"start": {"line": 270, "column": 60}, "end": {"line": 270, "column": null}}]}, "15": {"loc": {"start": {"line": 282, "column": 19}, "end": {"line": 282, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 282, "column": 44}, "end": {"line": 282, "column": 57}}, {"start": {"line": 282, "column": 60}, "end": {"line": 282, "column": null}}]}, "16": {"loc": {"start": {"line": 310, "column": 19}, "end": {"line": 310, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 310, "column": 44}, "end": {"line": 310, "column": 57}}, {"start": {"line": 310, "column": 60}, "end": {"line": 310, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\stores\\roleStore.ts": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\stores\\roleStore.ts", "statementMap": {"0": {"start": {"line": 67, "column": 13}, "end": {"line": 67, "column": 28}}, "1": {"start": {"line": 1, "column": 23}, "end": {"line": 1, "column": null}}, "2": {"start": {"line": 2, "column": 25}, "end": {"line": 2, "column": null}}, "3": {"start": {"line": 3, "column": 28}, "end": {"line": 3, "column": null}}, "4": {"start": {"line": 53, "column": 21}, "end": {"line": 65, "column": null}}, "5": {"start": {"line": 67, "column": 28}, "end": {"line": 312, "column": null}}, "6": {"start": {"line": 69, "column": 19}, "end": {"line": 309, "column": null}}, "7": {"start": {"line": 73, "column": 8}, "end": {"line": 73, "column": null}}, "8": {"start": {"line": 74, "column": 8}, "end": {"line": 91, "column": null}}, "9": {"start": {"line": 75, "column": 30}, "end": {"line": 75, "column": null}}, "10": {"start": {"line": 76, "column": 27}, "end": {"line": 76, "column": null}}, "11": {"start": {"line": 78, "column": 10}, "end": {"line": 85, "column": null}}, "12": {"start": {"line": 87, "column": 10}, "end": {"line": 90, "column": null}}, "13": {"start": {"line": 95, "column": 8}, "end": {"line": 95, "column": null}}, "14": {"start": {"line": 96, "column": 8}, "end": {"line": 104, "column": null}}, "15": {"start": {"line": 97, "column": 23}, "end": {"line": 97, "column": null}}, "16": {"start": {"line": 98, "column": 10}, "end": {"line": 98, "column": null}}, "17": {"start": {"line": 100, "column": 10}, "end": {"line": 103, "column": null}}, "18": {"start": {"line": 108, "column": 8}, "end": {"line": 108, "column": null}}, "19": {"start": {"line": 109, "column": 8}, "end": {"line": 117, "column": null}}, "20": {"start": {"line": 110, "column": 28}, "end": {"line": 110, "column": null}}, "21": {"start": {"line": 111, "column": 10}, "end": {"line": 111, "column": null}}, "22": {"start": {"line": 113, "column": 10}, "end": {"line": 116, "column": null}}, "23": {"start": {"line": 121, "column": 8}, "end": {"line": 128, "column": null}}, "24": {"start": {"line": 122, "column": 29}, "end": {"line": 122, "column": null}}, "25": {"start": {"line": 123, "column": 10}, "end": {"line": 123, "column": null}}, "26": {"start": {"line": 125, "column": 10}, "end": {"line": 127, "column": null}}, "27": {"start": {"line": 132, "column": 8}, "end": {"line": 132, "column": null}}, "28": {"start": {"line": 133, "column": 8}, "end": {"line": 155, "column": null}}, "29": {"start": {"line": 134, "column": 26}, "end": {"line": 134, "column": null}}, "30": {"start": {"line": 137, "column": 10}, "end": {"line": 141, "column": null}}, "31": {"start": {"line": 137, "column": 24}, "end": {"line": 141, "column": null}}, "32": {"start": {"line": 144, "column": 10}, "end": {"line": 146, "column": null}}, "33": {"start": {"line": 145, "column": 12}, "end": {"line": 145, "column": null}}, "34": {"start": {"line": 148, "column": 10}, "end": {"line": 148, "column": null}}, "35": {"start": {"line": 150, "column": 10}, "end": {"line": 153, "column": null}}, "36": {"start": {"line": 154, "column": 10}, "end": {"line": 154, "column": null}}, "37": {"start": {"line": 159, "column": 8}, "end": {"line": 159, "column": null}}, "38": {"start": {"line": 160, "column": 8}, "end": {"line": 184, "column": null}}, "39": {"start": {"line": 161, "column": 30}, "end": {"line": 161, "column": null}}, "40": {"start": {"line": 164, "column": 10}, "end": {"line": 170, "column": null}}, "41": {"start": {"line": 164, "column": 24}, "end": {"line": 170, "column": null}}, "42": {"start": {"line": 166, "column": 14}, "end": {"line": 166, "column": null}}, "43": {"start": {"line": 173, "column": 10}, "end": {"line": 175, "column": null}}, "44": {"start": {"line": 174, "column": 12}, "end": {"line": 174, "column": null}}, "45": {"start": {"line": 177, "column": 10}, "end": {"line": 177, "column": null}}, "46": {"start": {"line": 179, "column": 10}, "end": {"line": 182, "column": null}}, "47": {"start": {"line": 183, "column": 10}, "end": {"line": 183, "column": null}}, "48": {"start": {"line": 188, "column": 8}, "end": {"line": 188, "column": null}}, "49": {"start": {"line": 189, "column": 8}, "end": {"line": 208, "column": null}}, "50": {"start": {"line": 190, "column": 10}, "end": {"line": 190, "column": null}}, "51": {"start": {"line": 193, "column": 10}, "end": {"line": 198, "column": null}}, "52": {"start": {"line": 193, "column": 24}, "end": {"line": 198, "column": null}}, "53": {"start": {"line": 194, "column": 46}, "end": {"line": 194, "column": null}}, "54": {"start": {"line": 201, "column": 10}, "end": {"line": 201, "column": null}}, "55": {"start": {"line": 203, "column": 10}, "end": {"line": 206, "column": null}}, "56": {"start": {"line": 207, "column": 10}, "end": {"line": 207, "column": null}}, "57": {"start": {"line": 212, "column": 8}, "end": {"line": 212, "column": null}}, "58": {"start": {"line": 213, "column": 8}, "end": {"line": 232, "column": null}}, "59": {"start": {"line": 214, "column": 10}, "end": {"line": 214, "column": null}}, "60": {"start": {"line": 217, "column": 10}, "end": {"line": 222, "column": null}}, "61": {"start": {"line": 217, "column": 24}, "end": {"line": 222, "column": null}}, "62": {"start": {"line": 218, "column": 46}, "end": {"line": 218, "column": null}}, "63": {"start": {"line": 225, "column": 10}, "end": {"line": 225, "column": null}}, "64": {"start": {"line": 227, "column": 10}, "end": {"line": 230, "column": null}}, "65": {"start": {"line": 231, "column": 10}, "end": {"line": 231, "column": null}}, "66": {"start": {"line": 236, "column": 8}, "end": {"line": 236, "column": null}}, "67": {"start": {"line": 237, "column": 8}, "end": {"line": 256, "column": null}}, "68": {"start": {"line": 238, "column": 10}, "end": {"line": 238, "column": null}}, "69": {"start": {"line": 241, "column": 30}, "end": {"line": 241, "column": null}}, "70": {"start": {"line": 243, "column": 10}, "end": {"line": 249, "column": null}}, "71": {"start": {"line": 243, "column": 24}, "end": {"line": 249, "column": null}}, "72": {"start": {"line": 245, "column": 14}, "end": {"line": 245, "column": null}}, "73": {"start": {"line": 251, "column": 10}, "end": {"line": 254, "column": null}}, "74": {"start": {"line": 255, "column": 10}, "end": {"line": 255, "column": null}}, "75": {"start": {"line": 260, "column": 8}, "end": {"line": 260, "column": null}}, "76": {"start": {"line": 261, "column": 8}, "end": {"line": 280, "column": null}}, "77": {"start": {"line": 262, "column": 10}, "end": {"line": 262, "column": null}}, "78": {"start": {"line": 265, "column": 30}, "end": {"line": 265, "column": null}}, "79": {"start": {"line": 267, "column": 10}, "end": {"line": 273, "column": null}}, "80": {"start": {"line": 267, "column": 24}, "end": {"line": 273, "column": null}}, "81": {"start": {"line": 269, "column": 14}, "end": {"line": 269, "column": null}}, "82": {"start": {"line": 275, "column": 10}, "end": {"line": 278, "column": null}}, "83": {"start": {"line": 279, "column": 10}, "end": {"line": 279, "column": null}}, "84": {"start": {"line": 284, "column": 8}, "end": {"line": 284, "column": null}}, "85": {"start": {"line": 288, "column": 8}, "end": {"line": 291, "column": null}}, "86": {"start": {"line": 288, "column": 22}, "end": {"line": 291, "column": null}}, "87": {"start": {"line": 295, "column": 8}, "end": {"line": 295, "column": null}}, "88": {"start": {"line": 299, "column": 8}, "end": {"line": 299, "column": null}}, "89": {"start": {"line": 303, "column": 8}, "end": {"line": 303, "column": null}}, "90": {"start": {"line": 307, "column": 8}, "end": {"line": 307, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 5}}, "loc": {"start": {"line": 69, "column": 19}, "end": {"line": 309, "column": null}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 72, "column": 18}, "end": {"line": 72, "column": 25}}, "loc": {"start": {"line": 72, "column": 25}, "end": {"line": 92, "column": null}}}, "2": {"name": "(anonymous_3)", "decl": {"start": {"line": 94, "column": 21}, "end": {"line": 94, "column": 28}}, "loc": {"start": {"line": 94, "column": 28}, "end": {"line": 105, "column": null}}}, "3": {"name": "(anonymous_4)", "decl": {"start": {"line": 107, "column": 26}, "end": {"line": 107, "column": null}}, "loc": {"start": {"line": 107, "column": 26}, "end": {"line": 118, "column": null}}}, "4": {"name": "(anonymous_5)", "decl": {"start": {"line": 120, "column": 27}, "end": {"line": 120, "column": null}}, "loc": {"start": {"line": 120, "column": 27}, "end": {"line": 129, "column": null}}}, "5": {"name": "(anonymous_6)", "decl": {"start": {"line": 131, "column": 18}, "end": {"line": 131, "column": 25}}, "loc": {"start": {"line": 131, "column": 25}, "end": {"line": 156, "column": null}}}, "6": {"name": "(anonymous_7)", "decl": {"start": {"line": 137, "column": 14}, "end": {"line": 137, "column": 24}}, "loc": {"start": {"line": 137, "column": 24}, "end": {"line": 141, "column": null}}}, "7": {"name": "(anonymous_8)", "decl": {"start": {"line": 158, "column": 18}, "end": {"line": 158, "column": 25}}, "loc": {"start": {"line": 158, "column": 37}, "end": {"line": 185, "column": null}}}, "8": {"name": "(anonymous_9)", "decl": {"start": {"line": 164, "column": 14}, "end": {"line": 164, "column": 24}}, "loc": {"start": {"line": 164, "column": 24}, "end": {"line": 170, "column": null}}}, "9": {"name": "(anonymous_10)", "decl": {"start": {"line": 165, "column": 35}, "end": {"line": 165, "column": null}}, "loc": {"start": {"line": 166, "column": 14}, "end": {"line": 166, "column": null}}}, "10": {"name": "(anonymous_11)", "decl": {"start": {"line": 187, "column": 18}, "end": {"line": 187, "column": 25}}, "loc": {"start": {"line": 187, "column": 25}, "end": {"line": 209, "column": null}}}, "11": {"name": "(anonymous_12)", "decl": {"start": {"line": 193, "column": 14}, "end": {"line": 193, "column": 24}}, "loc": {"start": {"line": 193, "column": 24}, "end": {"line": 198, "column": null}}}, "12": {"name": "(anonymous_13)", "decl": {"start": {"line": 194, "column": 38}, "end": {"line": 194, "column": 46}}, "loc": {"start": {"line": 194, "column": 46}, "end": {"line": 194, "column": null}}}, "13": {"name": "(anonymous_14)", "decl": {"start": {"line": 211, "column": 23}, "end": {"line": 211, "column": 30}}, "loc": {"start": {"line": 211, "column": 30}, "end": {"line": 233, "column": null}}}, "14": {"name": "(anonymous_15)", "decl": {"start": {"line": 217, "column": 14}, "end": {"line": 217, "column": 24}}, "loc": {"start": {"line": 217, "column": 24}, "end": {"line": 222, "column": null}}}, "15": {"name": "(anonymous_16)", "decl": {"start": {"line": 218, "column": 38}, "end": {"line": 218, "column": 46}}, "loc": {"start": {"line": 218, "column": 46}, "end": {"line": 218, "column": null}}}, "16": {"name": "(anonymous_17)", "decl": {"start": {"line": 235, "column": 25}, "end": {"line": 235, "column": 32}}, "loc": {"start": {"line": 235, "column": 48}, "end": {"line": 257, "column": null}}}, "17": {"name": "(anonymous_18)", "decl": {"start": {"line": 243, "column": 14}, "end": {"line": 243, "column": 24}}, "loc": {"start": {"line": 243, "column": 24}, "end": {"line": 249, "column": null}}}, "18": {"name": "(anonymous_19)", "decl": {"start": {"line": 244, "column": 35}, "end": {"line": 244, "column": null}}, "loc": {"start": {"line": 245, "column": 14}, "end": {"line": 245, "column": null}}}, "19": {"name": "(anonymous_20)", "decl": {"start": {"line": 259, "column": 25}, "end": {"line": 259, "column": 32}}, "loc": {"start": {"line": 259, "column": 48}, "end": {"line": 281, "column": null}}}, "20": {"name": "(anonymous_21)", "decl": {"start": {"line": 267, "column": 14}, "end": {"line": 267, "column": 24}}, "loc": {"start": {"line": 267, "column": 24}, "end": {"line": 273, "column": null}}}, "21": {"name": "(anonymous_22)", "decl": {"start": {"line": 268, "column": 35}, "end": {"line": 268, "column": null}}, "loc": {"start": {"line": 269, "column": 14}, "end": {"line": 269, "column": null}}}, "22": {"name": "(anonymous_23)", "decl": {"start": {"line": 283, "column": 23}, "end": {"line": 283, "column": 24}}, "loc": {"start": {"line": 283, "column": 24}, "end": {"line": 285, "column": null}}}, "23": {"name": "(anonymous_24)", "decl": {"start": {"line": 287, "column": 18}, "end": {"line": 287, "column": 19}}, "loc": {"start": {"line": 287, "column": 19}, "end": {"line": 292, "column": null}}}, "24": {"name": "(anonymous_25)", "decl": {"start": {"line": 288, "column": 12}, "end": {"line": 288, "column": 22}}, "loc": {"start": {"line": 288, "column": 22}, "end": {"line": 291, "column": null}}}, "25": {"name": "(anonymous_26)", "decl": {"start": {"line": 294, "column": 15}, "end": {"line": 294, "column": 16}}, "loc": {"start": {"line": 294, "column": 16}, "end": {"line": 296, "column": null}}}, "26": {"name": "(anonymous_27)", "decl": {"start": {"line": 298, "column": 19}, "end": {"line": 298, "column": 20}}, "loc": {"start": {"line": 298, "column": 20}, "end": {"line": 300, "column": null}}}, "27": {"name": "(anonymous_28)", "decl": {"start": {"line": 302, "column": 18}, "end": {"line": 302, "column": null}}, "loc": {"start": {"line": 302, "column": 18}, "end": {"line": 304, "column": null}}}, "28": {"name": "(anonymous_29)", "decl": {"start": {"line": 306, "column": 13}, "end": {"line": 306, "column": null}}, "loc": {"start": {"line": 306, "column": 13}, "end": {"line": 308, "column": null}}}}, "branchMap": {"0": {"loc": {"start": {"line": 88, "column": 19}, "end": {"line": 88, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 88, "column": 44}, "end": {"line": 88, "column": 57}}, {"start": {"line": 88, "column": 60}, "end": {"line": 88, "column": null}}]}, "1": {"loc": {"start": {"line": 101, "column": 19}, "end": {"line": 101, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 101, "column": 44}, "end": {"line": 101, "column": 57}}, {"start": {"line": 101, "column": 60}, "end": {"line": 101, "column": null}}]}, "2": {"loc": {"start": {"line": 114, "column": 19}, "end": {"line": 114, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 114, "column": 44}, "end": {"line": 114, "column": 57}}, {"start": {"line": 114, "column": 60}, "end": {"line": 114, "column": null}}]}, "3": {"loc": {"start": {"line": 126, "column": 19}, "end": {"line": 126, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 126, "column": 44}, "end": {"line": 126, "column": 57}}, {"start": {"line": 126, "column": 60}, "end": {"line": 126, "column": null}}]}, "4": {"loc": {"start": {"line": 144, "column": 10}, "end": {"line": 146, "column": null}}, "type": "if", "locations": [{"start": {"line": 144, "column": 10}, "end": {"line": 146, "column": null}}, {"start": {}, "end": {}}]}, "5": {"loc": {"start": {"line": 151, "column": 19}, "end": {"line": 151, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 151, "column": 44}, "end": {"line": 151, "column": 57}}, {"start": {"line": 151, "column": 60}, "end": {"line": 151, "column": null}}]}, "6": {"loc": {"start": {"line": 166, "column": 14}, "end": {"line": 166, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 166, "column": 31}, "end": {"line": 166, "column": 45}}, {"start": {"line": 166, "column": 45}, "end": {"line": 166, "column": null}}]}, "7": {"loc": {"start": {"line": 168, "column": 26}, "end": {"line": 168, "column": 90}}, "type": "cond-expr", "locations": [{"start": {"line": 168, "column": 58}, "end": {"line": 168, "column": 72}}, {"start": {"line": 168, "column": 72}, "end": {"line": 168, "column": 90}}]}, "8": {"loc": {"start": {"line": 173, "column": 10}, "end": {"line": 175, "column": null}}, "type": "if", "locations": [{"start": {"line": 173, "column": 10}, "end": {"line": 175, "column": null}}, {"start": {}, "end": {}}]}, "9": {"loc": {"start": {"line": 180, "column": 19}, "end": {"line": 180, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 180, "column": 44}, "end": {"line": 180, "column": 57}}, {"start": {"line": 180, "column": 60}, "end": {"line": 180, "column": null}}]}, "10": {"loc": {"start": {"line": 195, "column": 26}, "end": {"line": 195, "column": 83}}, "type": "cond-expr", "locations": [{"start": {"line": 195, "column": 58}, "end": {"line": 195, "column": 65}}, {"start": {"line": 195, "column": 65}, "end": {"line": 195, "column": 83}}]}, "11": {"loc": {"start": {"line": 204, "column": 19}, "end": {"line": 204, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 204, "column": 44}, "end": {"line": 204, "column": 57}}, {"start": {"line": 204, "column": 60}, "end": {"line": 204, "column": null}}]}, "12": {"loc": {"start": {"line": 219, "column": 26}, "end": {"line": 219, "column": 96}}, "type": "cond-expr", "locations": [{"start": {"line": 219, "column": 71}, "end": {"line": 219, "column": 78}}, {"start": {"line": 219, "column": 78}, "end": {"line": 219, "column": 96}}]}, "13": {"loc": {"start": {"line": 219, "column": 39}, "end": {"line": 219, "column": 71}}, "type": "binary-expr", "locations": [{"start": {"line": 219, "column": 39}, "end": {"line": 219, "column": 65}}, {"start": {"line": 219, "column": 65}, "end": {"line": 219, "column": 71}}]}, "14": {"loc": {"start": {"line": 228, "column": 19}, "end": {"line": 228, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 228, "column": 44}, "end": {"line": 228, "column": 57}}, {"start": {"line": 228, "column": 60}, "end": {"line": 228, "column": null}}]}, "15": {"loc": {"start": {"line": 245, "column": 14}, "end": {"line": 245, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 245, "column": 35}, "end": {"line": 245, "column": 49}}, {"start": {"line": 245, "column": 49}, "end": {"line": 245, "column": null}}]}, "16": {"loc": {"start": {"line": 247, "column": 26}, "end": {"line": 247, "column": 94}}, "type": "cond-expr", "locations": [{"start": {"line": 247, "column": 62}, "end": {"line": 247, "column": 76}}, {"start": {"line": 247, "column": 76}, "end": {"line": 247, "column": 94}}]}, "17": {"loc": {"start": {"line": 252, "column": 19}, "end": {"line": 252, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 252, "column": 44}, "end": {"line": 252, "column": 57}}, {"start": {"line": 252, "column": 60}, "end": {"line": 252, "column": null}}]}, "18": {"loc": {"start": {"line": 269, "column": 14}, "end": {"line": 269, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 269, "column": 35}, "end": {"line": 269, "column": 49}}, {"start": {"line": 269, "column": 49}, "end": {"line": 269, "column": null}}]}, "19": {"loc": {"start": {"line": 271, "column": 26}, "end": {"line": 271, "column": 94}}, "type": "cond-expr", "locations": [{"start": {"line": 271, "column": 62}, "end": {"line": 271, "column": 76}}, {"start": {"line": 271, "column": 76}, "end": {"line": 271, "column": 94}}]}, "20": {"loc": {"start": {"line": 276, "column": 19}, "end": {"line": 276, "column": null}}, "type": "cond-expr", "locations": [{"start": {"line": 276, "column": 44}, "end": {"line": 276, "column": 57}}, {"start": {"line": 276, "column": 60}, "end": {"line": 276, "column": null}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0, 0], "15": [0, 0], "16": [0, 0], "17": [0, 0], "18": [0, 0], "19": [0, 0], "20": [0, 0]}}, "C:\\project\\bmad-method\\apps\\frontend\\src\\types\\permission.ts": {"path": "C:\\project\\bmad-method\\apps\\frontend\\src\\types\\permission.ts", "statementMap": {"0": {"start": {"line": 244, "column": 13}, "end": {"line": 244, "column": 34}}, "1": {"start": {"line": 233, "column": 13}, "end": {"line": 233, "column": 35}}, "2": {"start": {"line": 222, "column": 13}, "end": {"line": 222, "column": 34}}, "3": {"start": {"line": 255, "column": 13}, "end": {"line": 255, "column": 36}}, "4": {"start": {"line": 222, "column": 34}, "end": {"line": 231, "column": null}}, "5": {"start": {"line": 233, "column": 35}, "end": {"line": 242, "column": null}}, "6": {"start": {"line": 244, "column": 34}, "end": {"line": 253, "column": null}}, "7": {"start": {"line": 255, "column": 36}, "end": {"line": 261, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {}, "b": {}}}