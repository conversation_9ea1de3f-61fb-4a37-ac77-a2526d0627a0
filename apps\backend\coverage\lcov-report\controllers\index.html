
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for controllers</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> controllers</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">57.31% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>94/164</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70.19% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>73/104</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">81.81% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>18/22</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">57.31% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>94/164</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="permissionController.ts"><a href="permissionController.ts.html">permissionController.ts</a></td>
	<td data-value="53.84" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 53%"></div><div class="cover-empty" style="width: 47%"></div></div>
	</td>
	<td data-value="53.84" class="pct high">53.84%</td>
	<td data-value="91" class="abs high">49/91</td>
	<td data-value="74.19" class="pct high">74.19%</td>
	<td data-value="62" class="abs high">46/62</td>
	<td data-value="75" class="pct high">75%</td>
	<td data-value="12" class="abs high">9/12</td>
	<td data-value="53.84" class="pct high">53.84%</td>
	<td data-value="91" class="abs high">49/91</td>
	</tr>

<tr>
	<td class="file high" data-value="roleController.ts"><a href="roleController.ts.html">roleController.ts</a></td>
	<td data-value="61.64" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 61%"></div><div class="cover-empty" style="width: 39%"></div></div>
	</td>
	<td data-value="61.64" class="pct high">61.64%</td>
	<td data-value="73" class="abs high">45/73</td>
	<td data-value="64.28" class="pct high">64.28%</td>
	<td data-value="42" class="abs high">27/42</td>
	<td data-value="90" class="pct high">90%</td>
	<td data-value="10" class="abs high">9/10</td>
	<td data-value="61.64" class="pct high">61.64%</td>
	<td data-value="73" class="abs high">45/73</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-04T00:02:47.328Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    