/**
 * Integration Tests for Permission Management Workflow
 * Tests end-to-end scenarios for permission creation, matrix management, and bulk operations
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  usePathname: () => '/permissions',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock fetch for API calls
global.fetch = jest.fn();

// Mock permission data
const mockPermissions = [
  {
    id: '1',
    name: 'users.read',
    description: 'Read users',
    module: 'users',
    feature: 'read',
    action: 'read',
    resource: 'user',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'users.write',
    description: 'Write users',
    module: 'users',
    feature: 'write',
    action: 'write',
    resource: 'user',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '3',
    name: 'roles.read',
    description: 'Read roles',
    module: 'roles',
    feature: 'read',
    action: 'read',
    resource: 'role',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

// Permission Matrix Component for testing
const PermissionMatrixPage = () => {
  const [permissions, setPermissions] = React.useState(mockPermissions);
  const [loading, setLoading] = React.useState(false);
  const [showCreateForm, setShowCreateForm] = React.useState(false);
  const [selectedPermissions, setSelectedPermissions] = React.useState<string[]>([]);
  const [matrixView, setMatrixView] = React.useState(false);

  const modules = ['users', 'roles', 'permissions'];
  const actions = ['read', 'write', 'delete', 'manage'];

  const handleCreatePermission = async (permissionData: any) => {
    setLoading(true);
    try {
      const response = await fetch('/api/permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(permissionData),
      });
      
      if (response.ok) {
        const newPermission = await response.json();
        setPermissions(prev => [...prev, newPermission]);
        setShowCreateForm(false);
      }
    } catch (error) {
      console.error('Error creating permission:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkCreate = async (matrixData: any) => {
    setLoading(true);
    try {
      const response = await fetch('/api/permissions/bulk-create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(matrixData),
      });
      
      if (response.ok) {
        const newPermissions = await response.json();
        setPermissions(prev => [...prev, ...newPermissions]);
      }
    } catch (error) {
      console.error('Error bulk creating permissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkDelete = async (permissionIds: string[]) => {
    setLoading(true);
    try {
      const response = await fetch('/api/permissions/bulk-delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ permissionIds }),
      });
      
      if (response.ok) {
        setPermissions(prev => prev.filter(p => !permissionIds.includes(p.id)));
        setSelectedPermissions([]);
      }
    } catch (error) {
      console.error('Error bulk deleting permissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTogglePermission = (permissionId: string) => {
    setSelectedPermissions(prev => 
      prev.includes(permissionId)
        ? prev.filter(id => id !== permissionId)
        : [...prev, permissionId]
    );
  };

  const generateMatrix = () => {
    const matrixData = [];
    modules.forEach(module => {
      actions.forEach(action => {
        matrixData.push({
          name: `${module}.${action}`,
          description: `${action} ${module}`,
          module,
          feature: action,
          action,
          resource: module.slice(0, -1), // remove 's' from plural
          isActive: true,
        });
      });
    });
    return matrixData;
  };

  return (
    <div data-testid="permission-matrix-page">
      <h1>Permission Management</h1>
      
      {loading && <div data-testid="loading">Loading...</div>}
      
      <div data-testid="controls">
        <button 
          onClick={() => setShowCreateForm(true)}
          data-testid="create-permission-btn"
        >
          Create Permission
        </button>
        
        <button 
          onClick={() => setMatrixView(!matrixView)}
          data-testid="toggle-matrix-view"
        >
          {matrixView ? 'List View' : 'Matrix View'}
        </button>
        
        <button 
          onClick={() => handleBulkCreate(generateMatrix())}
          data-testid="generate-matrix-btn"
        >
          Generate Matrix
        </button>
        
        {selectedPermissions.length > 0 && (
          <button 
            onClick={() => handleBulkDelete(selectedPermissions)}
            data-testid="bulk-delete-btn"
          >
            Delete Selected ({selectedPermissions.length})
          </button>
        )}
      </div>

      {showCreateForm && (
        <div data-testid="create-permission-form">
          <h2>Create Permission</h2>
          <form onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.target as HTMLFormElement);
            const module = formData.get('module') as string;
            const action = formData.get('action') as string;
            handleCreatePermission({
              name: `${module}.${action}`,
              description: formData.get('description'),
              module,
              feature: action,
              action,
              resource: formData.get('resource'),
              isActive: true,
            });
          }}>
            <select name="module" required data-testid="module-select">
              <option value="">Select Module</option>
              {modules.map(module => (
                <option key={module} value={module}>{module}</option>
              ))}
            </select>
            
            <select name="action" required data-testid="action-select">
              <option value="">Select Action</option>
              {actions.map(action => (
                <option key={action} value={action}>{action}</option>
              ))}
            </select>
            
            <input name="description" placeholder="Description" required />
            <input name="resource" placeholder="Resource" required />
            
            <button type="submit" data-testid="submit-create-permission">Create</button>
            <button type="button" onClick={() => setShowCreateForm(false)}>Cancel</button>
          </form>
        </div>
      )}

      {matrixView ? (
        <div data-testid="matrix-view">
          <h2>Permission Matrix</h2>
          <table data-testid="permission-matrix">
            <thead>
              <tr>
                <th>Module</th>
                {actions.map(action => (
                  <th key={action}>{action}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {modules.map(module => (
                <tr key={module}>
                  <td>{module}</td>
                  {actions.map(action => {
                    const permission = permissions.find(p => 
                      p.module === module && p.action === action
                    );
                    return (
                      <td key={action}>
                        <input
                          type="checkbox"
                          checked={!!permission}
                          onChange={() => {
                            if (permission) {
                              handleBulkDelete([permission.id]);
                            } else {
                              handleCreatePermission({
                                name: `${module}.${action}`,
                                description: `${action} ${module}`,
                                module,
                                feature: action,
                                action,
                                resource: module.slice(0, -1),
                                isActive: true,
                              });
                            }
                          }}
                          data-testid={`matrix-${module}-${action}`}
                        />
                      </td>
                    );
                  })}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div data-testid="permissions-list">
          {permissions.map(permission => (
            <div key={permission.id} data-testid={`permission-${permission.id}`}>
              <input
                type="checkbox"
                checked={selectedPermissions.includes(permission.id)}
                onChange={() => handleTogglePermission(permission.id)}
                data-testid={`select-${permission.id}`}
              />
              <h3>{permission.name}</h3>
              <p>{permission.description}</p>
              <p>Module: {permission.module}</p>
              <p>Action: {permission.action}</p>
              <p>Status: {permission.isActive ? 'Active' : 'Inactive'}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

describe('Permission Management Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  it('should render permission management page', () => {
    render(<PermissionMatrixPage />);
    
    expect(screen.getByTestId('permission-matrix-page')).toBeInTheDocument();
    expect(screen.getByText('Permission Management')).toBeInTheDocument();
    expect(screen.getByTestId('create-permission-btn')).toBeInTheDocument();
  });

  it('should display existing permissions in list view', () => {
    render(<PermissionMatrixPage />);
    
    expect(screen.getByTestId('permission-1')).toBeInTheDocument();
    expect(screen.getByTestId('permission-2')).toBeInTheDocument();
    expect(screen.getByText('users.read')).toBeInTheDocument();
    expect(screen.getByText('users.write')).toBeInTheDocument();
  });

  it('should toggle between list and matrix view', async () => {
    const user = userEvent.setup();
    render(<PermissionMatrixPage />);

    // Initially in list view
    expect(screen.getByTestId('permissions-list')).toBeInTheDocument();
    expect(screen.queryByTestId('matrix-view')).not.toBeInTheDocument();

    // Toggle to matrix view
    await user.click(screen.getByTestId('toggle-matrix-view'));

    expect(screen.getByTestId('matrix-view')).toBeInTheDocument();
    expect(screen.queryByTestId('permissions-list')).not.toBeInTheDocument();
    expect(screen.getByTestId('permission-matrix')).toBeInTheDocument();
  });

  it('should handle permission creation workflow', async () => {
    const user = userEvent.setup();

    // Mock successful API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        id: '4',
        name: 'permissions.read',
        description: 'Read permissions',
        module: 'permissions',
        feature: 'read',
        action: 'read',
        resource: 'permission',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }),
    });

    render(<PermissionMatrixPage />);

    // Click create permission button
    await user.click(screen.getByTestId('create-permission-btn'));

    // Verify form appears
    expect(screen.getByTestId('create-permission-form')).toBeInTheDocument();

    // Fill form
    await user.selectOptions(screen.getByTestId('module-select'), 'permissions');
    await user.selectOptions(screen.getByTestId('action-select'), 'read');
    await user.type(screen.getByPlaceholderText('Description'), 'Read permissions');
    await user.type(screen.getByPlaceholderText('Resource'), 'permission');

    // Submit form
    await user.click(screen.getByTestId('submit-create-permission'));

    // Verify API call
    expect(global.fetch).toHaveBeenCalledWith('/api/permissions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'permissions.read',
        description: 'Read permissions',
        module: 'permissions',
        feature: 'read',
        action: 'read',
        resource: 'permission',
        isActive: true,
      }),
    });

    // Wait for permission to appear in list
    await waitFor(() => {
      expect(screen.getByText('permissions.read')).toBeInTheDocument();
    });
  });

  it('should handle bulk matrix generation', async () => {
    const user = userEvent.setup();

    // Mock successful API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => Array.from({ length: 12 }, (_, i) => ({
        id: `bulk-${i}`,
        name: `test.permission.${i}`,
        description: `Test permission ${i}`,
        module: 'test',
        feature: 'permission',
        action: 'test',
        resource: 'test',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })),
    });

    render(<PermissionMatrixPage />);

    // Click generate matrix button
    await user.click(screen.getByTestId('generate-matrix-btn'));

    // Verify API call with matrix data
    expect(global.fetch).toHaveBeenCalledWith('/api/permissions/bulk-create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: expect.stringContaining('users.read'),
    });
  });

  it('should handle permission selection and bulk delete', async () => {
    const user = userEvent.setup();

    // Mock successful API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
    });

    render(<PermissionMatrixPage />);

    // Select permissions
    await user.click(screen.getByTestId('select-1'));
    await user.click(screen.getByTestId('select-2'));

    // Verify bulk delete button appears
    expect(screen.getByTestId('bulk-delete-btn')).toBeInTheDocument();
    expect(screen.getByText('Delete Selected (2)')).toBeInTheDocument();

    // Click bulk delete
    await user.click(screen.getByTestId('bulk-delete-btn'));

    // Verify API call
    expect(global.fetch).toHaveBeenCalledWith('/api/permissions/bulk-delete', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ permissionIds: ['1', '2'] }),
    });

    // Wait for permissions to be removed
    await waitFor(() => {
      expect(screen.queryByTestId('permission-1')).not.toBeInTheDocument();
      expect(screen.queryByTestId('permission-2')).not.toBeInTheDocument();
    });
  });

  it('should handle matrix view permission toggling', async () => {
    const user = userEvent.setup();

    // Mock successful API response for creation
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        id: '4',
        name: 'users.delete',
        description: 'delete users',
        module: 'users',
        feature: 'delete',
        action: 'delete',
        resource: 'user',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      }),
    });

    render(<PermissionMatrixPage />);

    // Switch to matrix view
    await user.click(screen.getByTestId('toggle-matrix-view'));

    // Toggle a permission that doesn't exist (should create)
    await user.click(screen.getByTestId('matrix-users-delete'));

    // Verify API call for creation
    expect(global.fetch).toHaveBeenCalledWith('/api/permissions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'users.delete',
        description: 'delete users',
        module: 'users',
        feature: 'delete',
        action: 'delete',
        resource: 'user',
        isActive: true,
      }),
    });
  });

  it('should show loading state during operations', async () => {
    const user = userEvent.setup();

    // Mock delayed API response
    (global.fetch as jest.Mock).mockImplementation(() =>
      new Promise(resolve => setTimeout(() => resolve({ ok: true }), 100))
    );

    render(<PermissionMatrixPage />);

    // Click generate matrix button
    await user.click(screen.getByTestId('generate-matrix-btn'));

    // Verify loading state appears
    expect(screen.getByTestId('loading')).toBeInTheDocument();

    // Wait for operation to complete
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });

  it('should handle matrix view with existing permissions', async () => {
    const user = userEvent.setup();
    render(<PermissionMatrixPage />);

    // Switch to matrix view
    await user.click(screen.getByTestId('toggle-matrix-view'));

    // Check that existing permissions are marked in matrix
    const usersReadCheckbox = screen.getByTestId('matrix-users-read');
    const usersWriteCheckbox = screen.getByTestId('matrix-users-write');

    expect(usersReadCheckbox).toBeChecked();
    expect(usersWriteCheckbox).toBeChecked();

    // Check that non-existing permissions are not marked
    const usersDeleteCheckbox = screen.getByTestId('matrix-users-delete');
    expect(usersDeleteCheckbox).not.toBeChecked();
  });
});
