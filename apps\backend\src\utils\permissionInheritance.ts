/**
 * Optimized permission inheritance calculation for large hierarchies
 */

interface Role {
  id: string;
  name: string;
  hierarchy: number;
  permissions: Permission[];
}

interface Permission {
  id: string;
  name: string;
  module: string;
  feature: string;
  action: string;
  resource: string;
  requiredHierarchy?: number;
}

interface PermissionNode {
  permission: Permission;
  inheritedFrom?: string; // Role ID that granted this permission
  directlyAssigned: boolean;
}

interface InheritanceCache {
  [roleId: string]: {
    permissions: PermissionNode[];
    lastUpdated: number;
    dependencies: string[]; // Role IDs this role depends on
  };
}

// Cache for inheritance calculations
const inheritanceCache: InheritanceCache = {};
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Clear cache for a specific role and its dependents
 */
export const clearInheritanceCache = (roleId?: string): void => {
  if (roleId) {
    // Clear specific role and all roles that depend on it
    const toClear = new Set([roleId]);
    
    // Find all roles that depend on this role
    for (const [id, cache] of Object.entries(inheritanceCache)) {
      if (cache.dependencies.includes(roleId)) {
        toClear.add(id);
      }
    }
    
    // Clear all dependent roles
    for (const id of toClear) {
      delete inheritanceCache[id];
    }
  } else {
    // Clear entire cache
    Object.keys(inheritanceCache).forEach(key => {
      delete inheritanceCache[key];
    });
  }
};

/**
 * Check if cache entry is valid
 */
const isCacheValid = (roleId: string): boolean => {
  const cache = inheritanceCache[roleId];
  if (!cache) return false;
  
  const now = Date.now();
  return (now - cache.lastUpdated) < CACHE_TTL;
};

/**
 * Build role hierarchy tree for efficient traversal
 */
const buildHierarchyTree = (roles: Role[]): Map<number, Role[]> => {
  const hierarchyMap = new Map<number, Role[]>();
  
  for (const role of roles) {
    const hierarchy = role.hierarchy;
    if (!hierarchyMap.has(hierarchy)) {
      hierarchyMap.set(hierarchy, []);
    }
    hierarchyMap.get(hierarchy)!.push(role);
  }
  
  return hierarchyMap;
};

/**
 * Get all roles that a role can inherit from (higher hierarchy levels)
 */
const getInheritableRoles = (role: Role, allRoles: Role[]): Role[] => {
  return allRoles
    .filter(r => r.hierarchy > role.hierarchy && r.id !== role.id)
    .sort((a, b) => b.hierarchy - a.hierarchy); // Sort by hierarchy descending
};

/**
 * Calculate effective permissions for a role with inheritance
 */
export const calculateEffectivePermissions = (
  targetRole: Role,
  allRoles: Role[]
): PermissionNode[] => {
  // Check cache first
  if (isCacheValid(targetRole.id)) {
    return inheritanceCache[targetRole.id].permissions;
  }

  const effectivePermissions = new Map<string, PermissionNode>();
  const dependencies: string[] = [];

  // Add directly assigned permissions
  for (const permission of targetRole.permissions) {
    effectivePermissions.set(permission.id, {
      permission,
      directlyAssigned: true
    });
  }

  // Get roles this role can inherit from
  const inheritableRoles = getInheritableRoles(targetRole, allRoles);
  
  // Process inheritance from higher hierarchy roles
  for (const parentRole of inheritableRoles) {
    dependencies.push(parentRole.id);
    
    // Recursively calculate parent's effective permissions
    const parentPermissions = calculateEffectivePermissions(parentRole, allRoles);
    
    for (const parentPermNode of parentPermissions) {
      const permission = parentPermNode.permission;
      
      // Check if permission can be inherited based on required hierarchy
      if (permission.requiredHierarchy && targetRole.hierarchy < permission.requiredHierarchy) {
        continue; // Skip this permission, role hierarchy too low
      }
      
      // Only inherit if not already directly assigned
      if (!effectivePermissions.has(permission.id)) {
        effectivePermissions.set(permission.id, {
          permission,
          inheritedFrom: parentPermNode.inheritedFrom || parentRole.id,
          directlyAssigned: false
        });
      }
    }
  }

  const result = Array.from(effectivePermissions.values());

  // Cache the result
  inheritanceCache[targetRole.id] = {
    permissions: result,
    lastUpdated: Date.now(),
    dependencies
  };

  return result;
};

/**
 * Batch calculate effective permissions for multiple roles
 */
export const batchCalculateEffectivePermissions = (
  roles: Role[],
  allRoles: Role[]
): Map<string, PermissionNode[]> => {
  const results = new Map<string, PermissionNode[]>();
  
  // Sort roles by hierarchy (highest first) for optimal cache usage
  const sortedRoles = [...roles].sort((a, b) => b.hierarchy - a.hierarchy);
  
  for (const role of sortedRoles) {
    results.set(role.id, calculateEffectivePermissions(role, allRoles));
  }
  
  return results;
};

/**
 * Check if a role has a specific permission (including inherited)
 */
export const hasPermission = (
  role: Role,
  permissionName: string,
  allRoles: Role[]
): boolean => {
  const effectivePermissions = calculateEffectivePermissions(role, allRoles);
  return effectivePermissions.some(p => p.permission.name === permissionName);
};

/**
 * Get permission inheritance chain for debugging
 */
export const getPermissionInheritanceChain = (
  role: Role,
  permissionId: string,
  allRoles: Role[]
): string[] => {
  const effectivePermissions = calculateEffectivePermissions(role, allRoles);
  const permissionNode = effectivePermissions.find(p => p.permission.id === permissionId);
  
  if (!permissionNode) {
    return [];
  }
  
  if (permissionNode.directlyAssigned) {
    return [role.id];
  }
  
  if (permissionNode.inheritedFrom) {
    const parentRole = allRoles.find(r => r.id === permissionNode.inheritedFrom);
    if (parentRole) {
      return [...getPermissionInheritanceChain(parentRole, permissionId, allRoles), role.id];
    }
  }
  
  return [role.id];
};

/**
 * Optimize permission matrix calculation for large datasets
 */
export const calculatePermissionMatrix = (
  roles: Role[],
  permissions: Permission[]
): Map<string, Map<string, PermissionNode | null>> => {
  const matrix = new Map<string, Map<string, PermissionNode | null>>();
  
  // Pre-calculate effective permissions for all roles
  const effectivePermissionsMap = batchCalculateEffectivePermissions(roles, roles);
  
  // Build matrix
  for (const role of roles) {
    const rolePermissions = new Map<string, PermissionNode | null>();
    const effectivePermissions = effectivePermissionsMap.get(role.id) || [];
    
    // Create lookup map for effective permissions
    const effectivePermissionMap = new Map<string, PermissionNode>();
    for (const permNode of effectivePermissions) {
      effectivePermissionMap.set(permNode.permission.id, permNode);
    }
    
    // Check each permission
    for (const permission of permissions) {
      rolePermissions.set(
        permission.id,
        effectivePermissionMap.get(permission.id) || null
      );
    }
    
    matrix.set(role.id, rolePermissions);
  }
  
  return matrix;
};

/**
 * Get cache statistics for monitoring
 */
export const getCacheStats = () => {
  const now = Date.now();
  let validEntries = 0;
  let expiredEntries = 0;
  
  for (const cache of Object.values(inheritanceCache)) {
    if ((now - cache.lastUpdated) < CACHE_TTL) {
      validEntries++;
    } else {
      expiredEntries++;
    }
  }
  
  return {
    totalEntries: Object.keys(inheritanceCache).length,
    validEntries,
    expiredEntries,
    cacheHitRatio: validEntries / (validEntries + expiredEntries) || 0
  };
};

export default {
  calculateEffectivePermissions,
  batchCalculateEffectivePermissions,
  hasPermission,
  getPermissionInheritanceChain,
  calculatePermissionMatrix,
  clearInheritanceCache,
  getCacheStats
};
