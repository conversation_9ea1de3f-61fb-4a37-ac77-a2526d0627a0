import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import RoleForm from '../RoleForm';
import type { Role, Permission } from '@/types/role';

// Mock data
const mockPermissions: Permission[] = [
  {
    id: '1',
    name: 'users.read',
    description: 'Read users',
    module: 'users',
    feature: 'read',
    action: 'read',
    resource: 'user',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'users.write',
    description: 'Write users',
    module: 'users',
    feature: 'write',
    action: 'write',
    resource: 'user',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

const mockRole: Role = {
  id: '1',
  name: 'Admin',
  description: 'Administrator role',
  hierarchy: 1,
  isActive: true,
  permissions: mockPermissions,
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

const defaultProps = {
  onSubmit: jest.fn(),
  onCancel: jest.fn(),
  availablePermissions: mockPermissions,
};

describe('RoleForm Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Create Mode', () => {
    it('renders create form correctly', () => {
      render(<RoleForm {...defaultProps} />);
      
      expect(screen.getByLabelText(/nama role/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/deskripsi/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/level hierarki/i)).toBeInTheDocument();
      expect(screen.getByText(/simpan/i)).toBeInTheDocument();
      expect(screen.getByText(/batal/i)).toBeInTheDocument();
    });

    it('submits form with correct data', async () => {
      const user = userEvent.setup();
      render(<RoleForm {...defaultProps} />);
      
      // Fill form
      await user.type(screen.getByLabelText(/nama role/i), 'Test Role');
      await user.type(screen.getByLabelText(/deskripsi/i), 'Test Description');
      await user.type(screen.getByLabelText(/level hierarki/i), '5');
      
      // Select permissions
      const permissionCheckboxes = screen.getAllByRole('checkbox');
      await user.click(permissionCheckboxes[0]); // Select first permission
      
      // Submit form
      await user.click(screen.getByText(/simpan/i));
      
      await waitFor(() => {
        expect(defaultProps.onSubmit).toHaveBeenCalledWith({
          name: 'Test Role',
          description: 'Test Description',
          hierarchy: 5,
          isActive: true,
          permissionIds: [mockPermissions[0].id],
        });
      });
    });

    it('validates required fields', async () => {
      const user = userEvent.setup();
      render(<RoleForm {...defaultProps} />);
      
      // Try to submit without filling required fields
      await user.click(screen.getByText(/simpan/i));
      
      expect(screen.getByText(/nama role wajib diisi/i)).toBeInTheDocument();
      expect(screen.getByText(/level hierarki wajib diisi/i)).toBeInTheDocument();
    });

    it('validates hierarchy level range', async () => {
      const user = userEvent.setup();
      render(<RoleForm {...defaultProps} />);
      
      // Enter invalid hierarchy level
      await user.type(screen.getByLabelText(/level hierarki/i), '0');
      await user.click(screen.getByText(/simpan/i));
      
      expect(screen.getByText(/level hierarki harus antara 1-10/i)).toBeInTheDocument();
    });

    it('validates name length', async () => {
      const user = userEvent.setup();
      render(<RoleForm {...defaultProps} />);
      
      // Enter name that's too short
      await user.type(screen.getByLabelText(/nama role/i), 'ab');
      await user.click(screen.getByText(/simpan/i));
      
      expect(screen.getByText(/nama role minimal 3 karakter/i)).toBeInTheDocument();
    });
  });

  describe('Edit Mode', () => {
    it('renders edit form with existing data', () => {
      render(<RoleForm {...defaultProps} role={mockRole} />);
      
      expect(screen.getByDisplayValue('Admin')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Administrator role')).toBeInTheDocument();
      expect(screen.getByDisplayValue('1')).toBeInTheDocument();
    });

    it('pre-selects existing permissions', () => {
      render(<RoleForm {...defaultProps} role={mockRole} />);
      
      // Check that permissions are pre-selected
      const permissionCheckboxes = screen.getAllByRole('checkbox');
      expect(permissionCheckboxes[0]).toBeChecked();
      expect(permissionCheckboxes[1]).toBeChecked();
    });

    it('updates role data correctly', async () => {
      const user = userEvent.setup();
      render(<RoleForm {...defaultProps} role={mockRole} />);
      
      // Update name
      const nameInput = screen.getByDisplayValue('Admin');
      await user.clear(nameInput);
      await user.type(nameInput, 'Super Admin');
      
      // Submit form
      await user.click(screen.getByText(/simpan/i));
      
      await waitFor(() => {
        expect(defaultProps.onSubmit).toHaveBeenCalledWith({
          name: 'Super Admin',
          description: 'Administrator role',
          hierarchy: 1,
          isActive: true,
          permissionIds: [mockPermissions[0].id, mockPermissions[1].id],
        });
      });
    });
  });

  describe('Permission Management', () => {
    it('allows selecting and deselecting permissions', async () => {
      const user = userEvent.setup();
      render(<RoleForm {...defaultProps} />);
      
      const permissionCheckboxes = screen.getAllByRole('checkbox');
      
      // Select first permission
      await user.click(permissionCheckboxes[0]);
      expect(permissionCheckboxes[0]).toBeChecked();
      
      // Deselect first permission
      await user.click(permissionCheckboxes[0]);
      expect(permissionCheckboxes[0]).not.toBeChecked();
    });

    it('shows permission details', () => {
      render(<RoleForm {...defaultProps} />);
      
      expect(screen.getByText('users.read')).toBeInTheDocument();
      expect(screen.getByText('Read users')).toBeInTheDocument();
      expect(screen.getByText('users.write')).toBeInTheDocument();
      expect(screen.getByText('Write users')).toBeInTheDocument();
    });

    it('filters permissions by search', async () => {
      const user = userEvent.setup();
      render(<RoleForm {...defaultProps} />);
      
      // Search for specific permission
      const searchInput = screen.getByPlaceholderText(/cari permission/i);
      await user.type(searchInput, 'read');
      
      // Should show only read permission
      expect(screen.getByText('users.read')).toBeInTheDocument();
      expect(screen.queryByText('users.write')).not.toBeInTheDocument();
    });

    it('groups permissions by module', () => {
      render(<RoleForm {...defaultProps} />);
      
      // Should show module grouping
      expect(screen.getByText(/users/i)).toBeInTheDocument();
    });
  });

  describe('Form Actions', () => {
    it('calls onCancel when cancel button is clicked', async () => {
      const user = userEvent.setup();
      render(<RoleForm {...defaultProps} />);
      
      await user.click(screen.getByText(/batal/i));
      
      expect(defaultProps.onCancel).toHaveBeenCalled();
    });

    it('shows loading state during submission', async () => {
      const user = userEvent.setup();
      const slowSubmit = jest.fn(() => new Promise(resolve => setTimeout(resolve, 100)));
      
      render(<RoleForm {...defaultProps} onSubmit={slowSubmit} />);
      
      // Fill required fields
      await user.type(screen.getByLabelText(/nama role/i), 'Test Role');
      await user.type(screen.getByLabelText(/level hierarki/i), '1');
      
      // Submit form
      await user.click(screen.getByText(/simpan/i));
      
      // Should show loading state
      expect(screen.getByText(/menyimpan/i)).toBeInTheDocument();
    });

    it('handles submission errors', async () => {
      const user = userEvent.setup();
      const failingSubmit = jest.fn(() => Promise.reject(new Error('Submission failed')));
      
      render(<RoleForm {...defaultProps} onSubmit={failingSubmit} />);
      
      // Fill required fields
      await user.type(screen.getByLabelText(/nama role/i), 'Test Role');
      await user.type(screen.getByLabelText(/level hierarki/i), '1');
      
      // Submit form
      await user.click(screen.getByText(/simpan/i));
      
      await waitFor(() => {
        expect(screen.getByText(/terjadi kesalahan/i)).toBeInTheDocument();
      });
    });
  });

  describe('Status Toggle', () => {
    it('toggles active status', async () => {
      const user = userEvent.setup();
      render(<RoleForm {...defaultProps} />);
      
      const statusToggle = screen.getByRole('checkbox', { name: /status aktif/i });
      
      // Should be active by default
      expect(statusToggle).toBeChecked();
      
      // Toggle to inactive
      await user.click(statusToggle);
      expect(statusToggle).not.toBeChecked();
    });
  });
});
