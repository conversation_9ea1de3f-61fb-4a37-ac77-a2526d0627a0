import { NextApiRequest, NextApiResponse } from 'next';

/**
 * Rate limiting middleware for API endpoints
 */

interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  message?: string; // Custom error message
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
}

interface RequestRecord {
  count: number;
  resetTime: number;
}

// In-memory store for rate limiting (in production, use Redis)
const requestStore = new Map<string, RequestRecord>();

/**
 * Generate a unique key for rate limiting based on IP and endpoint
 */
const generateKey = (req: NextApiRequest): string => {
  const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress || 'unknown';
  const endpoint = req.url || 'unknown';
  return `${ip}:${endpoint}`;
};

/**
 * Clean up expired entries from the store
 */
const cleanupExpiredEntries = (): void => {
  const now = Date.now();
  for (const [key, record] of requestStore.entries()) {
    if (now > record.resetTime) {
      requestStore.delete(key);
    }
  }
};

/**
 * Create rate limiter middleware
 */
export const createRateLimiter = (config: RateLimitConfig) => {
  return (req: NextApiRequest, res: NextApiResponse, next: () => void) => {
    // Clean up expired entries periodically
    if (Math.random() < 0.01) { // 1% chance to cleanup
      cleanupExpiredEntries();
    }

    const key = generateKey(req);
    const now = Date.now();
    const resetTime = now + config.windowMs;

    let record = requestStore.get(key);

    if (!record || now > record.resetTime) {
      // Create new record or reset expired record
      record = {
        count: 1,
        resetTime
      };
      requestStore.set(key, record);
    } else {
      // Increment existing record
      record.count++;
    }

    // Set rate limit headers
    res.setHeader('X-RateLimit-Limit', config.maxRequests);
    res.setHeader('X-RateLimit-Remaining', Math.max(0, config.maxRequests - record.count));
    res.setHeader('X-RateLimit-Reset', Math.ceil(record.resetTime / 1000));

    if (record.count > config.maxRequests) {
      // Rate limit exceeded
      const retryAfter = Math.ceil((record.resetTime - now) / 1000);
      res.setHeader('Retry-After', retryAfter);
      
      return res.status(429).json({
        success: false,
        error: config.message || 'Too many requests, please try again later.',
        retryAfter
      });
    }

    // Continue to next middleware
    next();
  };
};

/**
 * Standard rate limiter for general API endpoints
 */
export const standardRateLimit = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 100, // 100 requests per 15 minutes
  message: 'Too many requests from this IP, please try again later.'
});

/**
 * Strict rate limiter for bulk operations
 */
export const bulkOperationRateLimit = createRateLimiter({
  windowMs: 5 * 60 * 1000, // 5 minutes
  maxRequests: 10, // 10 bulk operations per 5 minutes
  message: 'Too many bulk operations, please wait before trying again.'
});

/**
 * Authentication rate limiter
 */
export const authRateLimit = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 5, // 5 login attempts per 15 minutes
  message: 'Too many authentication attempts, please try again later.'
});

/**
 * Search rate limiter
 */
export const searchRateLimit = createRateLimiter({
  windowMs: 1 * 60 * 1000, // 1 minute
  maxRequests: 30, // 30 searches per minute
  message: 'Too many search requests, please slow down.'
});

/**
 * Apply rate limiting to Next.js API handler
 */
export const withRateLimit = (
  handler: (req: NextApiRequest, res: NextApiResponse) => Promise<void> | void,
  rateLimiter = standardRateLimit
) => {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    return new Promise<void>((resolve, reject) => {
      rateLimiter(req, res, () => {
        try {
          const result = handler(req, res);
          if (result instanceof Promise) {
            result.then(resolve).catch(reject);
          } else {
            resolve();
          }
        } catch (error) {
          reject(error);
        }
      });
    });
  };
};

/**
 * Rate limiter specifically for role bulk operations
 */
export const roleBulkRateLimit = createRateLimiter({
  windowMs: 10 * 60 * 1000, // 10 minutes
  maxRequests: 5, // 5 bulk role operations per 10 minutes
  message: 'Too many bulk role operations, please wait before trying again.'
});

/**
 * Rate limiter specifically for permission bulk operations
 */
export const permissionBulkRateLimit = createRateLimiter({
  windowMs: 10 * 60 * 1000, // 10 minutes
  maxRequests: 5, // 5 bulk permission operations per 10 minutes
  message: 'Too many bulk permission operations, please wait before trying again.'
});

/**
 * Rate limiter for audit log queries
 */
export const auditLogRateLimit = createRateLimiter({
  windowMs: 5 * 60 * 1000, // 5 minutes
  maxRequests: 20, // 20 audit log queries per 5 minutes
  message: 'Too many audit log requests, please wait before trying again.'
});

export default {
  createRateLimiter,
  standardRateLimit,
  bulkOperationRateLimit,
  authRateLimit,
  searchRateLimit,
  withRateLimit,
  roleBulkRateLimit,
  permissionBulkRateLimit,
  auditLogRateLimit
};
