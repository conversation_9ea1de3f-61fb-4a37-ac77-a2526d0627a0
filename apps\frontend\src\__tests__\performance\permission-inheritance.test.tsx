/**
 * Performance Tests for Permission Inheritance
 * Tests complex permission inheritance calculations and hierarchy traversal
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  usePathname: () => '/permissions',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock fetch for API calls
global.fetch = jest.fn();

// Generate complex role hierarchy
const generateRoleHierarchy = (levels: number, rolesPerLevel: number) => {
  const roles = [];
  let roleId = 1;
  
  for (let level = 1; level <= levels; level++) {
    for (let i = 0; i < rolesPerLevel; i++) {
      const role = {
        id: `role-${roleId}`,
        name: `Level ${level} Role ${i + 1}`,
        description: `Role at hierarchy level ${level}`,
        hierarchy: level,
        parentRoleId: level > 1 ? `role-${Math.floor((roleId - rolesPerLevel - 1) / rolesPerLevel) * rolesPerLevel + 1}` : null,
        directPermissions: Array.from({ length: 5 + level * 2 }, (_, j) => `perm-${level}-${i}-${j}`),
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      roles.push(role);
      roleId++;
    }
  }
  
  return roles;
};

// Permission inheritance calculator
const calculateInheritedPermissions = (roleId: string, roles: any[]) => {
  const visited = new Set<string>();
  const inheritedPermissions = new Set<string>();
  
  const traverseHierarchy = (currentRoleId: string) => {
    if (visited.has(currentRoleId)) return;
    visited.add(currentRoleId);
    
    const role = roles.find(r => r.id === currentRoleId);
    if (!role) return;
    
    // Add direct permissions
    role.directPermissions.forEach((perm: string) => inheritedPermissions.add(perm));
    
    // Traverse parent roles (inheritance from higher hierarchy)
    const parentRoles = roles.filter(r => 
      r.hierarchy < role.hierarchy && 
      r.isActive
    );
    
    parentRoles.forEach(parent => traverseHierarchy(parent.id));
  };
  
  traverseHierarchy(roleId);
  return Array.from(inheritedPermissions);
};

// Performance test component for permission inheritance
const PermissionInheritanceComponent = ({ hierarchyLevels, rolesPerLevel }: { 
  hierarchyLevels: number; 
  rolesPerLevel: number; 
}) => {
  const [roles, setRoles] = React.useState(() => generateRoleHierarchy(hierarchyLevels, rolesPerLevel));
  const [selectedRole, setSelectedRole] = React.useState<string>('');
  const [inheritedPermissions, setInheritedPermissions] = React.useState<string[]>([]);
  const [calculationTime, setCalculationTime] = React.useState<number>(0);
  const [loading, setLoading] = React.useState(false);

  // Performance-critical inheritance calculation
  const calculatePermissions = React.useCallback((roleId: string) => {
    const startTime = performance.now();
    
    const permissions = calculateInheritedPermissions(roleId, roles);
    setInheritedPermissions(permissions);
    
    const endTime = performance.now();
    const timeTaken = endTime - startTime;
    setCalculationTime(timeTaken);
    
    console.log(`Permission inheritance calculation for ${roleId} took ${timeTaken} milliseconds`);
    console.log(`Calculated ${permissions.length} inherited permissions`);
  }, [roles]);

  // Bulk inheritance calculation
  const calculateBulkInheritance = async () => {
    setLoading(true);
    const startTime = performance.now();
    
    const results = new Map<string, string[]>();
    
    // Calculate inheritance for all roles
    roles.forEach(role => {
      const permissions = calculateInheritedPermissions(role.id, roles);
      results.set(role.id, permissions);
    });
    
    const endTime = performance.now();
    const totalTime = endTime - startTime;
    
    console.log(`Bulk inheritance calculation for ${roles.length} roles took ${totalTime} milliseconds`);
    console.log(`Average time per role: ${totalTime / roles.length} milliseconds`);
    
    setLoading(false);
    return results;
  };

  // Simulate permission matrix calculation
  const calculatePermissionMatrix = async () => {
    setLoading(true);
    const startTime = performance.now();
    
    const matrix = new Map<string, Map<string, boolean>>();
    
    // For each role, calculate which permissions it has
    roles.forEach(role => {
      const rolePermissions = calculateInheritedPermissions(role.id, roles);
      const permissionMap = new Map<string, boolean>();
      
      // Create a comprehensive permission list
      const allPermissions = new Set<string>();
      roles.forEach(r => r.directPermissions.forEach((p: string) => allPermissions.add(p)));
      
      allPermissions.forEach(permission => {
        permissionMap.set(permission, rolePermissions.includes(permission));
      });
      
      matrix.set(role.id, permissionMap);
    });
    
    const endTime = performance.now();
    const matrixTime = endTime - startTime;
    
    console.log(`Permission matrix calculation took ${matrixTime} milliseconds`);
    console.log(`Matrix size: ${matrix.size} roles x ${matrix.values().next().value?.size || 0} permissions`);
    
    setLoading(false);
    return matrix;
  };

  // Hierarchy validation
  const validateHierarchy = () => {
    const startTime = performance.now();
    
    const issues = [];
    
    roles.forEach(role => {
      // Check for circular dependencies
      const visited = new Set<string>();
      let current = role.id;
      
      while (current) {
        if (visited.has(current)) {
          issues.push(`Circular dependency detected for role ${role.id}`);
          break;
        }
        visited.add(current);
        
        const currentRole = roles.find(r => r.id === current);
        current = currentRole?.parentRoleId || '';
      }
      
      // Check hierarchy consistency
      if (role.parentRoleId) {
        const parent = roles.find(r => r.id === role.parentRoleId);
        if (parent && parent.hierarchy >= role.hierarchy) {
          issues.push(`Invalid hierarchy: ${role.id} has parent with same or lower hierarchy`);
        }
      }
    });
    
    const endTime = performance.now();
    console.log(`Hierarchy validation took ${endTime - startTime} milliseconds`);
    console.log(`Found ${issues.length} hierarchy issues`);
    
    return issues;
  };

  return (
    <div data-testid="permission-inheritance-component">
      <h1>Permission Inheritance Test</h1>
      <p>Hierarchy: {hierarchyLevels} levels, {rolesPerLevel} roles per level</p>
      <p>Total Roles: {roles.length}</p>
      
      {loading && <div data-testid="loading">Loading...</div>}
      
      <div data-testid="controls">
        <select
          value={selectedRole}
          onChange={(e) => {
            setSelectedRole(e.target.value);
            if (e.target.value) {
              calculatePermissions(e.target.value);
            }
          }}
          data-testid="role-select"
        >
          <option value="">Select a role</option>
          {roles.map(role => (
            <option key={role.id} value={role.id}>
              {role.name} (Level {role.hierarchy})
            </option>
          ))}
        </select>
        
        <button
          onClick={calculateBulkInheritance}
          data-testid="bulk-calculate-btn"
        >
          Calculate All Inheritance
        </button>
        
        <button
          onClick={calculatePermissionMatrix}
          data-testid="matrix-calculate-btn"
        >
          Calculate Permission Matrix
        </button>
        
        <button
          onClick={validateHierarchy}
          data-testid="validate-hierarchy-btn"
        >
          Validate Hierarchy
        </button>
      </div>

      {selectedRole && (
        <div data-testid="inheritance-results">
          <h3>Inheritance Results for {selectedRole}</h3>
          <p>Calculation Time: {calculationTime.toFixed(2)}ms</p>
          <p>Inherited Permissions: {inheritedPermissions.length}</p>
          <div data-testid="permissions-list">
            {inheritedPermissions.slice(0, 20).map(permission => (
              <div key={permission} data-testid={`permission-${permission}`}>
                {permission}
              </div>
            ))}
            {inheritedPermissions.length > 20 && (
              <div data-testid="more-permissions">
                ... and {inheritedPermissions.length - 20} more
              </div>
            )}
          </div>
        </div>
      )}

      <div data-testid="hierarchy-stats">
        <h3>Hierarchy Statistics</h3>
        {Array.from({ length: hierarchyLevels }, (_, i) => {
          const level = i + 1;
          const levelRoles = roles.filter(r => r.hierarchy === level);
          const avgPermissions = levelRoles.reduce((sum, role) => 
            sum + calculateInheritedPermissions(role.id, roles).length, 0
          ) / levelRoles.length;
          
          return (
            <div key={level} data-testid={`level-${level}-stats`}>
              Level {level}: {levelRoles.length} roles, avg {avgPermissions.toFixed(1)} permissions
            </div>
          );
        })}
      </div>
    </div>
  );
};

describe('Permission Inheritance Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  it('should handle 5-level hierarchy efficiently', async () => {
    const startTime = performance.now();
    
    render(<PermissionInheritanceComponent hierarchyLevels={5} rolesPerLevel={10} />);
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    expect(screen.getByTestId('permission-inheritance-component')).toBeInTheDocument();
    expect(screen.getByText('Total Roles: 50')).toBeInTheDocument();
    
    // Performance assertion: rendering should be under 1000ms
    expect(renderTime).toBeLessThan(1000);
    console.log(`Rendering 5-level hierarchy took ${renderTime} milliseconds`);
  });

  it('should calculate single role inheritance efficiently', async () => {
    const user = userEvent.setup();
    render(<PermissionInheritanceComponent hierarchyLevels={4} rolesPerLevel={8} />);
    
    const roleSelect = screen.getByTestId('role-select');
    
    const startTime = performance.now();
    await user.selectOptions(roleSelect, 'role-32'); // Bottom level role
    const endTime = performance.now();
    
    const calculationTime = endTime - startTime;
    
    // Performance assertion: single inheritance calculation should be under 1000ms
    expect(calculationTime).toBeLessThan(1000);
    console.log(`Single inheritance calculation took ${calculationTime} milliseconds`);
    
    // Verify results are displayed
    expect(screen.getByTestId('inheritance-results')).toBeInTheDocument();
    expect(screen.getByText(/Inherited Permissions:/)).toBeInTheDocument();
  });

  it('should handle bulk inheritance calculation efficiently', async () => {
    const user = userEvent.setup();
    render(<PermissionInheritanceComponent hierarchyLevels={3} rolesPerLevel={15} />);
    
    const bulkBtn = screen.getByTestId('bulk-calculate-btn');
    
    const startTime = performance.now();
    await user.click(bulkBtn);
    
    // Wait for calculation to complete
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
    
    const endTime = performance.now();
    const bulkTime = endTime - startTime;
    
    // Performance assertion: bulk calculation should be under 3000ms
    expect(bulkTime).toBeLessThan(3000);
    console.log(`Bulk inheritance calculation took ${bulkTime} milliseconds`);
  });

  it('should calculate permission matrix efficiently', async () => {
    const user = userEvent.setup();
    render(<PermissionInheritanceComponent hierarchyLevels={3} rolesPerLevel={10} />);
    
    const matrixBtn = screen.getByTestId('matrix-calculate-btn');
    
    const startTime = performance.now();
    await user.click(matrixBtn);
    
    // Wait for calculation to complete
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
    
    const endTime = performance.now();
    const matrixTime = endTime - startTime;
    
    // Performance assertion: matrix calculation should be under 2500ms
    expect(matrixTime).toBeLessThan(2500);
    console.log(`Permission matrix calculation took ${matrixTime} milliseconds`);
  });

  it('should validate hierarchy efficiently', async () => {
    const user = userEvent.setup();
    render(<PermissionInheritanceComponent hierarchyLevels={6} rolesPerLevel={5} />);
    
    const validateBtn = screen.getByTestId('validate-hierarchy-btn');
    
    const startTime = performance.now();
    await user.click(validateBtn);
    const endTime = performance.now();
    
    const validationTime = endTime - startTime;
    
    // Performance assertion: hierarchy validation should be under 600ms
    expect(validationTime).toBeLessThan(600);
    console.log(`Hierarchy validation took ${validationTime} milliseconds`);
  });

  it('should display hierarchy statistics correctly', () => {
    render(<PermissionInheritanceComponent hierarchyLevels={4} rolesPerLevel={6} />);
    
    expect(screen.getByTestId('hierarchy-stats')).toBeInTheDocument();
    expect(screen.getByTestId('level-1-stats')).toBeInTheDocument();
    expect(screen.getByTestId('level-4-stats')).toBeInTheDocument();
    
    // Check that statistics show correct role counts
    expect(screen.getByText(/Level 1: 6 roles/)).toBeInTheDocument();
    expect(screen.getByText(/Level 4: 6 roles/)).toBeInTheDocument();
  });

  it('should handle deep hierarchy levels efficiently', async () => {
    const user = userEvent.setup();
    render(<PermissionInheritanceComponent hierarchyLevels={8} rolesPerLevel={3} />);
    
    // Select a role from the deepest level
    const roleSelect = screen.getByTestId('role-select');
    await user.selectOptions(roleSelect, 'role-24'); // Last role
    
    // Verify inheritance calculation completes
    await waitFor(() => {
      expect(screen.getByTestId('inheritance-results')).toBeInTheDocument();
    });
    
    // Check that deep inheritance is calculated
    expect(screen.getByText(/Inherited Permissions:/)).toBeInTheDocument();
  });
});
