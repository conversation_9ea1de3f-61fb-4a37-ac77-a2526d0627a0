import React from 'react';
import { render, screen } from '@testing-library/react';

// Simple Permission component for testing
interface Permission {
  id: string;
  name: string;
  description: string;
  module: string;
  feature: string;
  action: string;
  resource: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface PermissionListProps {
  permissions: Permission[];
  loading: boolean;
  onEdit: (permission: Permission) => void;
  onDelete: (id: string) => void;
  onView: (permission: Permission) => void;
  onBulkDelete: (ids: string[]) => void;
  onFiltersChange: (filters: Record<string, unknown>) => void;
}

const PermissionList: React.FC<PermissionListProps> = ({
  permissions,
  loading,
  onEdit,
  onDelete,
  onView,
  onBulkDelete,
  onFiltersChange,
}) => {
  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="bg-gray-200 h-8 rounded mb-4"></div>
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="bg-gray-200 h-12 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (permissions.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Tidak ada permission yang ditemukan</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">
        Daftar Permission ({permissions.length})
      </h3>
      
      <div className="grid gap-4">
        {permissions.map((permission) => (
          <div key={permission.id} className="border rounded-lg p-4">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-medium">{permission.name}</h4>
                <p className="text-gray-600 text-sm">{permission.description}</p>
                <div className="flex gap-2 mt-2 text-xs">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    Modul: {permission.module}
                  </span>
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                    Fitur: {permission.feature}
                  </span>
                  <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">
                    Aksi: {permission.action}
                  </span>
                </div>
              </div>
              <div className="flex flex-col items-end gap-2">
                <span className={`px-2 py-1 rounded-full text-xs ${
                  permission.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {permission.isActive ? 'Aktif' : 'Tidak Aktif'}
                </span>
                <div className="flex gap-1">
                  <button
                    onClick={() => onView(permission)}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    Lihat
                  </button>
                  <button
                    onClick={() => onEdit(permission)}
                    className="text-green-600 hover:text-green-800 text-sm"
                  >
                    Edit
                  </button>
                  <button
                    onClick={() => onDelete(permission.id)}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Hapus
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Mock data
const mockPermissions: Permission[] = [
  {
    id: '1',
    name: 'users.read',
    description: 'Read users',
    module: 'users',
    feature: 'read',
    action: 'read',
    resource: 'user',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'users.write',
    description: 'Write users',
    module: 'users',
    feature: 'write',
    action: 'write',
    resource: 'user',
    isActive: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

const defaultProps = {
  permissions: mockPermissions,
  loading: false,
  onEdit: jest.fn(),
  onDelete: jest.fn(),
  onView: jest.fn(),
  onBulkDelete: jest.fn(),
  onFiltersChange: jest.fn(),
};

describe('PermissionList Simple Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<PermissionList {...defaultProps} />);
    expect(screen.getByText('users.read')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    render(<PermissionList {...defaultProps} loading={true} />);
    expect(document.querySelector('.animate-pulse')).toBeInTheDocument();
  });

  it('shows empty state when no permissions', () => {
    render(<PermissionList {...defaultProps} permissions={[]} />);
    expect(screen.getByText(/tidak ada permission yang ditemukan/i)).toBeInTheDocument();
  });

  it('displays permission information', () => {
    render(<PermissionList {...defaultProps} />);

    expect(screen.getByText('users.read')).toBeInTheDocument();
    expect(screen.getByText('Read users')).toBeInTheDocument();
    expect(screen.getAllByText(/Modul: users/)).toHaveLength(2);
    expect(screen.getByText('Fitur: read')).toBeInTheDocument();
    expect(screen.getByText('Aksi: read')).toBeInTheDocument();
  });

  it('shows permission count in header', () => {
    render(<PermissionList {...defaultProps} />);
    expect(screen.getByText(/daftar permission \(2\)/i)).toBeInTheDocument();
  });

  it('displays permission status correctly', () => {
    render(<PermissionList {...defaultProps} />);

    expect(screen.getAllByText('Aktif')).toHaveLength(1);
    expect(screen.getAllByText('Tidak Aktif')).toHaveLength(1);
  });

  it('shows action buttons', () => {
    render(<PermissionList {...defaultProps} />);
    
    expect(screen.getAllByText('Lihat')).toHaveLength(2);
    expect(screen.getAllByText('Edit')).toHaveLength(2);
    expect(screen.getAllByText('Hapus')).toHaveLength(2);
  });
});
