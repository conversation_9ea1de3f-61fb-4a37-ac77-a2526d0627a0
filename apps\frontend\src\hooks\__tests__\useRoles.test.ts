import { renderHook, act } from '@testing-library/react';
import { useRoles, useRoleHierarchy, useRoleStatistics } from '../useRoles';
import { useRoleStore } from '@/stores/roleStore';
import type { Role } from '@/types/role';

// Mock the role store
jest.mock('@/stores/roleStore');
const mockUseRoleStore = useRoleStore as jest.MockedFunction<typeof useRoleStore>;

// Mock data
const mockRole: Role = {
  id: '1',
  name: 'Admin',
  description: 'Administrator role',
  hierarchy: 1,
  isActive: true,
  permissions: [],
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

const mockRoles: Role[] = [mockRole];

const mockHierarchy = [
  {
    id: '1',
    name: 'Admin',
    level: 1,
    children: [],
  },
];

const mockStatistics = {
  totalRoles: 1,
  activeRoles: 1,
  inactiveRoles: 0,
  rolesByLevel: { '1': 1 },
};

const mockStoreState = {
  roles: mockRoles,
  selectedRole: null,
  roleHierarchy: mockHierarchy,
  roleStatistics: mockStatistics,
  loading: false,
  error: null,
  currentPage: 1,
  totalPages: 1,
  totalItems: 1,
  pageSize: 10,
  filters: {},
  fetchRoles: jest.fn(),
  fetchRoleById: jest.fn(),
  fetchRoleHierarchy: jest.fn(),
  fetchRoleStatistics: jest.fn(),
  createRole: jest.fn(),
  updateRole: jest.fn(),
  deleteRole: jest.fn(),
  bulkDeleteRoles: jest.fn(),
  setSelectedRole: jest.fn(),
  setFilters: jest.fn(),
  setPage: jest.fn(),
  setPageSize: jest.fn(),
  clearError: jest.fn(),
  reset: jest.fn(),
};

describe('useRoles', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRoleStore.mockReturnValue(mockStoreState);
  });

  it('returns role store state and actions', () => {
    const { result } = renderHook(() => useRoles());

    expect(result.current.roles).toEqual(mockRoles);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(result.current.currentPage).toBe(1);
    expect(result.current.totalPages).toBe(1);
    expect(result.current.totalItems).toBe(1);
    expect(result.current.pageSize).toBe(10);
    expect(result.current.filters).toEqual({});
    
    // Check actions are available
    expect(typeof result.current.fetchRoles).toBe('function');
    expect(typeof result.current.createRole).toBe('function');
    expect(typeof result.current.updateRole).toBe('function');
    expect(typeof result.current.deleteRole).toBe('function');
  });

  it('fetches roles on mount', () => {
    renderHook(() => useRoles());

    expect(mockStoreState.fetchRoles).toHaveBeenCalledTimes(1);
  });

  it('does not fetch roles on mount when autoFetch is false', () => {
    jest.clearAllMocks();
    renderHook(() => useRoles({ autoFetch: false }));

    expect(mockStoreState.fetchRoles).not.toHaveBeenCalled();
  });

  it('refetches roles when dependencies change', () => {
    const { rerender } = renderHook(
      ({ page, pageSize, filters }) => useRoles({ page, pageSize, filters }),
      {
        initialProps: { page: 1, pageSize: 10, filters: {} },
      }
    );

    expect(mockStoreState.fetchRoles).toHaveBeenCalledTimes(1);

    // Change page
    rerender({ page: 2, pageSize: 10, filters: {} });
    expect(mockStoreState.setPage).toHaveBeenCalledWith(2);
    expect(mockStoreState.fetchRoles).toHaveBeenCalledTimes(2);

    // Change page size
    rerender({ page: 2, pageSize: 20, filters: {} });
    expect(mockStoreState.setPageSize).toHaveBeenCalledWith(20);
    expect(mockStoreState.fetchRoles).toHaveBeenCalledTimes(3);

    // Change filters
    rerender({ page: 2, pageSize: 20, filters: { search: 'admin' } });
    expect(mockStoreState.setFilters).toHaveBeenCalledWith({ search: 'admin' });
    expect(mockStoreState.fetchRoles).toHaveBeenCalledTimes(4);
  });

  it('provides helper functions', () => {
    const { result } = renderHook(() => useRoles());

    expect(typeof result.current.refreshRoles).toBe('function');
    expect(typeof result.current.clearError).toBe('function');
  });

  it('refreshRoles calls fetchRoles', () => {
    const { result } = renderHook(() => useRoles());

    act(() => {
      result.current.refreshRoles();
    });

    expect(mockStoreState.fetchRoles).toHaveBeenCalledTimes(2); // Once on mount, once on refresh
  });

  it('clearError calls store clearError', () => {
    const { result } = renderHook(() => useRoles());

    act(() => {
      result.current.clearError();
    });

    expect(mockStoreState.clearError).toHaveBeenCalledTimes(1);
  });

  it('handles loading state correctly', () => {
    mockUseRoleStore.mockReturnValue({
      ...mockStoreState,
      loading: true,
    });

    const { result } = renderHook(() => useRoles());

    expect(result.current.loading).toBe(true);
  });

  it('handles error state correctly', () => {
    const errorMessage = 'Failed to fetch roles';
    mockUseRoleStore.mockReturnValue({
      ...mockStoreState,
      error: errorMessage,
    });

    const { result } = renderHook(() => useRoles());

    expect(result.current.error).toBe(errorMessage);
  });
});

describe('useRoleHierarchy', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRoleStore.mockReturnValue(mockStoreState);
  });

  it('returns hierarchy data and actions', () => {
    const { result } = renderHook(() => useRoleHierarchy());

    expect(result.current.hierarchy).toEqual(mockHierarchy);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(typeof result.current.fetchHierarchy).toBe('function');
    expect(typeof result.current.refreshHierarchy).toBe('function');
  });

  it('fetches hierarchy on mount', () => {
    renderHook(() => useRoleHierarchy());

    expect(mockStoreState.fetchRoleHierarchy).toHaveBeenCalledTimes(1);
  });

  it('does not fetch hierarchy on mount when autoFetch is false', () => {
    jest.clearAllMocks();
    renderHook(() => useRoleHierarchy({ autoFetch: false }));

    expect(mockStoreState.fetchRoleHierarchy).not.toHaveBeenCalled();
  });

  it('refreshHierarchy calls fetchRoleHierarchy', () => {
    const { result } = renderHook(() => useRoleHierarchy());

    act(() => {
      result.current.refreshHierarchy();
    });

    expect(mockStoreState.fetchRoleHierarchy).toHaveBeenCalledTimes(2);
  });
});

describe('useRoleStatistics', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseRoleStore.mockReturnValue(mockStoreState);
  });

  it('returns statistics data and actions', () => {
    const { result } = renderHook(() => useRoleStatistics());

    expect(result.current.statistics).toEqual(mockStatistics);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBe(null);
    expect(typeof result.current.fetchStatistics).toBe('function');
    expect(typeof result.current.refreshStatistics).toBe('function');
  });

  it('fetches statistics on mount', () => {
    renderHook(() => useRoleStatistics());

    expect(mockStoreState.fetchRoleStatistics).toHaveBeenCalledTimes(1);
  });

  it('does not fetch statistics on mount when autoFetch is false', () => {
    jest.clearAllMocks();
    renderHook(() => useRoleStatistics({ autoFetch: false }));

    expect(mockStoreState.fetchRoleStatistics).not.toHaveBeenCalled();
  });

  it('refreshStatistics calls fetchRoleStatistics', () => {
    const { result } = renderHook(() => useRoleStatistics());

    act(() => {
      result.current.refreshStatistics();
    });

    expect(mockStoreState.fetchRoleStatistics).toHaveBeenCalledTimes(2);
  });

  it('provides computed values', () => {
    const { result } = renderHook(() => useRoleStatistics());

    expect(result.current.totalRoles).toBe(1);
    expect(result.current.activeRoles).toBe(1);
    expect(result.current.inactiveRoles).toBe(0);
    expect(result.current.rolesByLevel).toEqual({ '1': 1 });
  });

  it('handles null statistics', () => {
    mockUseRoleStore.mockReturnValue({
      ...mockStoreState,
      roleStatistics: null,
    });

    const { result } = renderHook(() => useRoleStatistics());

    expect(result.current.statistics).toBe(null);
    expect(result.current.totalRoles).toBe(0);
    expect(result.current.activeRoles).toBe(0);
    expect(result.current.inactiveRoles).toBe(0);
    expect(result.current.rolesByLevel).toEqual({});
  });
});
