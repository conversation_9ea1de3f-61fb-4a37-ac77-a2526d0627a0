
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">26.9% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>141/524</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">33.45% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>89/266</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">25.84% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>23/89</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">27.85% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>139/499</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="controllers"><a href="controllers/index.html">controllers</a></td>
	<td data-value="57.31" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 57%"></div><div class="cover-empty" style="width: 43%"></div></div>
	</td>
	<td data-value="57.31" class="pct high">57.31%</td>
	<td data-value="164" class="abs high">94/164</td>
	<td data-value="70.19" class="pct high">70.19%</td>
	<td data-value="104" class="abs high">73/104</td>
	<td data-value="81.81" class="pct high">81.81%</td>
	<td data-value="22" class="abs high">18/22</td>
	<td data-value="57.31" class="pct high">57.31%</td>
	<td data-value="164" class="abs high">94/164</td>
	</tr>

<tr>
	<td class="file low" data-value="services"><a href="services/index.html">services</a></td>
	<td data-value="10.17" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10.17" class="pct low">10.17%</td>
	<td data-value="334" class="abs low">34/334</td>
	<td data-value="7.18" class="pct low">7.18%</td>
	<td data-value="153" class="abs low">11/153</td>
	<td data-value="4.83" class="pct low">4.83%</td>
	<td data-value="62" class="abs low">3/62</td>
	<td data-value="10.93" class="pct low">10.93%</td>
	<td data-value="311" class="abs low">34/311</td>
	</tr>

<tr>
	<td class="file high" data-value="utils"><a href="utils/index.html">utils</a></td>
	<td data-value="50" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 50%"></div><div class="cover-empty" style="width: 50%"></div></div>
	</td>
	<td data-value="50" class="pct high">50%</td>
	<td data-value="26" class="abs high">13/26</td>
	<td data-value="55.55" class="pct high">55.55%</td>
	<td data-value="9" class="abs high">5/9</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="5" class="abs low">2/5</td>
	<td data-value="45.83" class="pct low">45.83%</td>
	<td data-value="24" class="abs low">11/24</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-08-04T00:02:47.328Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    