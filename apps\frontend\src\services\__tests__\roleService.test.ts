import { roleService } from '../roleService';
import { ApiService } from '../api';
import type { Role, RoleFormData, RoleQueryParams } from '@/types/role';

// Mock the base API service
jest.mock('../api');
const MockedApiService = ApiService as jest.MockedClass<typeof ApiService>;

// Mock data
const mockRole: Role = {
  id: '1',
  name: 'Admin',
  description: 'Administrator role',
  hierarchy: 1,
  isActive: true,
  permissions: [],
  createdAt: new Date('2024-01-01'),
  updatedAt: new Date('2024-01-01'),
};

const mockRoles: Role[] = [mockRole];

const mockPaginatedResponse = {
  data: mockRoles,
  page: 1,
  totalPages: 1,
  total: 1,
  pageSize: 10,
};

const mockHierarchy = [
  {
    id: '1',
    name: 'Admin',
    level: 1,
    children: [],
  },
];

const mockStatistics = {
  totalRoles: 1,
  activeRoles: 1,
  inactiveRoles: 0,
  rolesByLevel: { '1': 1 },
};

describe('roleService', () => {
  let mockApiInstance: jest.Mocked<ApiService>;

  beforeEach(() => {
    jest.clearAllMocks();
    mockApiInstance = new MockedApiService() as jest.Mocked<ApiService>;
    (roleService as any).api = mockApiInstance;
  });

  describe('getRoles', () => {
    it('fetches roles with default parameters', async () => {
      mockApiInstance.get.mockResolvedValue(mockPaginatedResponse);

      const result = await roleService.getRoles();

      expect(mockApiInstance.get).toHaveBeenCalledWith('/roles', {
        params: {
          page: 1,
          pageSize: 10,
          sortBy: 'name',
          sortOrder: 'asc',
        },
      });
      expect(result).toEqual(mockPaginatedResponse);
    });

    it('fetches roles with custom parameters', async () => {
      const params: RoleQueryParams = {
        page: 2,
        pageSize: 20,
        search: 'admin',
        isActive: true,
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      mockApiInstance.get.mockResolvedValue(mockPaginatedResponse);

      const result = await roleService.getRoles(params);

      expect(mockApiInstance.get).toHaveBeenCalledWith('/roles', {
        params: {
          page: 2,
          pageSize: 20,
          search: 'admin',
          isActive: true,
          sortBy: 'createdAt',
          sortOrder: 'desc',
        },
      });
      expect(result).toEqual(mockPaginatedResponse);
    });

    it('handles API errors', async () => {
      const errorMessage = 'Failed to fetch roles';
      mockApiInstance.get.mockRejectedValue(new Error(errorMessage));

      await expect(roleService.getRoles()).rejects.toThrow(errorMessage);
    });
  });

  describe('getRoleById', () => {
    it('fetches role by id successfully', async () => {
      mockApiInstance.get.mockResolvedValue(mockRole);

      const result = await roleService.getRoleById('1');

      expect(mockApiInstance.get).toHaveBeenCalledWith('/roles/1');
      expect(result).toEqual(mockRole);
    });

    it('handles role not found error', async () => {
      mockApiInstance.get.mockRejectedValue(new Error('Role not found'));

      await expect(roleService.getRoleById('999')).rejects.toThrow('Role not found');
    });
  });

  describe('createRole', () => {
    const roleFormData: RoleFormData = {
      name: 'New Role',
      description: 'New role description',
      hierarchy: 2,
      isActive: true,
      permissionIds: ['perm1', 'perm2'],
    };

    it('creates role successfully', async () => {
      const newRole = { ...mockRole, id: '2', name: 'New Role' };
      mockApiInstance.post.mockResolvedValue(newRole);

      const result = await roleService.createRole(roleFormData);

      expect(mockApiInstance.post).toHaveBeenCalledWith('/roles', roleFormData);
      expect(result).toEqual(newRole);
    });

    it('handles validation errors', async () => {
      mockApiInstance.post.mockRejectedValue(new Error('Validation failed'));

      await expect(roleService.createRole(roleFormData)).rejects.toThrow('Validation failed');
    });
  });

  describe('updateRole', () => {
    const roleFormData: RoleFormData = {
      name: 'Updated Role',
      description: 'Updated description',
      hierarchy: 1,
      isActive: true,
      permissionIds: ['perm1'],
    };

    it('updates role successfully', async () => {
      const updatedRole = { ...mockRole, name: 'Updated Role' };
      mockApiInstance.put.mockResolvedValue(updatedRole);

      const result = await roleService.updateRole('1', roleFormData);

      expect(mockApiInstance.put).toHaveBeenCalledWith('/roles/1', roleFormData);
      expect(result).toEqual(updatedRole);
    });

    it('handles update errors', async () => {
      mockApiInstance.put.mockRejectedValue(new Error('Update failed'));

      await expect(roleService.updateRole('1', roleFormData)).rejects.toThrow('Update failed');
    });
  });

  describe('deleteRole', () => {
    it('deletes role successfully', async () => {
      mockApiInstance.delete.mockResolvedValue(undefined);

      await roleService.deleteRole('1');

      expect(mockApiInstance.delete).toHaveBeenCalledWith('/roles/1');
    });

    it('handles delete errors', async () => {
      mockApiInstance.delete.mockRejectedValue(new Error('Delete failed'));

      await expect(roleService.deleteRole('1')).rejects.toThrow('Delete failed');
    });
  });

  describe('bulkDeleteRoles', () => {
    it('bulk deletes roles successfully', async () => {
      mockApiInstance.delete.mockResolvedValue(undefined);

      await roleService.bulkDeleteRoles(['1', '2', '3']);

      expect(mockApiInstance.delete).toHaveBeenCalledWith('/roles/bulk', {
        data: { ids: ['1', '2', '3'] },
      });
    });

    it('handles bulk delete errors', async () => {
      mockApiInstance.delete.mockRejectedValue(new Error('Bulk delete failed'));

      await expect(roleService.bulkDeleteRoles(['1', '2'])).rejects.toThrow('Bulk delete failed');
    });
  });

  describe('getRoleHierarchy', () => {
    it('fetches role hierarchy successfully', async () => {
      mockApiInstance.get.mockResolvedValue(mockHierarchy);

      const result = await roleService.getRoleHierarchy();

      expect(mockApiInstance.get).toHaveBeenCalledWith('/roles/hierarchy');
      expect(result).toEqual(mockHierarchy);
    });

    it('handles hierarchy fetch errors', async () => {
      mockApiInstance.get.mockRejectedValue(new Error('Hierarchy fetch failed'));

      await expect(roleService.getRoleHierarchy()).rejects.toThrow('Hierarchy fetch failed');
    });
  });

  describe('getRoleStatistics', () => {
    it('fetches role statistics successfully', async () => {
      mockApiInstance.get.mockResolvedValue(mockStatistics);

      const result = await roleService.getRoleStatistics();

      expect(mockApiInstance.get).toHaveBeenCalledWith('/roles/statistics');
      expect(result).toEqual(mockStatistics);
    });

    it('handles statistics fetch errors', async () => {
      mockApiInstance.get.mockRejectedValue(new Error('Statistics fetch failed'));

      await expect(roleService.getRoleStatistics()).rejects.toThrow('Statistics fetch failed');
    });
  });

  describe('assignPermissions', () => {
    it('assigns permissions to role successfully', async () => {
      mockApiInstance.post.mockResolvedValue(undefined);

      await roleService.assignPermissions('1', ['perm1', 'perm2']);

      expect(mockApiInstance.post).toHaveBeenCalledWith('/roles/1/permissions', {
        permissionIds: ['perm1', 'perm2'],
      });
    });

    it('handles permission assignment errors', async () => {
      mockApiInstance.post.mockRejectedValue(new Error('Assignment failed'));

      await expect(roleService.assignPermissions('1', ['perm1'])).rejects.toThrow('Assignment failed');
    });
  });

  describe('removePermissions', () => {
    it('removes permissions from role successfully', async () => {
      mockApiInstance.delete.mockResolvedValue(undefined);

      await roleService.removePermissions('1', ['perm1', 'perm2']);

      expect(mockApiInstance.delete).toHaveBeenCalledWith('/roles/1/permissions', {
        data: { permissionIds: ['perm1', 'perm2'] },
      });
    });

    it('handles permission removal errors', async () => {
      mockApiInstance.delete.mockRejectedValue(new Error('Removal failed'));

      await expect(roleService.removePermissions('1', ['perm1'])).rejects.toThrow('Removal failed');
    });
  });
});
