/**
 * Performance Tests for Large Dataset Handling
 * Tests system performance with large numbers of roles and permissions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  usePathname: () => '/roles',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock fetch for API calls
global.fetch = jest.fn();

// Generate large dataset
const generateLargeRoleDataset = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: `role-${i + 1}`,
    name: `Role ${i + 1}`,
    description: `Description for role ${i + 1}`,
    hierarchy: Math.floor(i / 100) + 1, // Group roles by hierarchy
    isActive: i % 10 !== 0, // 90% active roles
    permissions: Array.from({ length: Math.floor(Math.random() * 20) }, (_, j) => `perm-${j}`),
    createdAt: new Date(2024, 0, 1 + i),
    updatedAt: new Date(2024, 0, 1 + i),
  }));
};

const generateLargePermissionDataset = (count: number) => {
  const modules = ['users', 'roles', 'permissions', 'audit', 'settings', 'reports'];
  const actions = ['read', 'write', 'delete', 'manage', 'export', 'import'];
  
  return Array.from({ length: count }, (_, i) => {
    const module = modules[i % modules.length];
    const action = actions[Math.floor(i / modules.length) % actions.length];
    return {
      id: `perm-${i + 1}`,
      name: `${module}.${action}.${Math.floor(i / (modules.length * actions.length))}`,
      description: `${action} ${module} permission ${i + 1}`,
      module,
      feature: action,
      action,
      resource: module.slice(0, -1),
      isActive: i % 20 !== 0, // 95% active permissions
      createdAt: new Date(2024, 0, 1 + i),
      updatedAt: new Date(2024, 0, 1 + i),
    };
  });
};

// Performance test component for large datasets
const LargeDatasetComponent = ({ dataSize }: { dataSize: number }) => {
  const [roles, setRoles] = React.useState(() => generateLargeRoleDataset(dataSize));
  const [permissions, setPermissions] = React.useState(() => generateLargePermissionDataset(dataSize * 2));
  const [loading, setLoading] = React.useState(false);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [filteredRoles, setFilteredRoles] = React.useState(roles);
  const [selectedRoles, setSelectedRoles] = React.useState<string[]>([]);

  // Performance-critical filtering
  React.useEffect(() => {
    const startTime = performance.now();
    
    const filtered = roles.filter(role => 
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    setFilteredRoles(filtered);
    
    const endTime = performance.now();
    console.log(`Filtering ${roles.length} roles took ${endTime - startTime} milliseconds`);
  }, [roles, searchTerm]);

  // Performance-critical bulk operations
  const handleBulkOperation = async (operation: string, roleIds: string[]) => {
    const startTime = performance.now();
    setLoading(true);
    
    try {
      // Simulate API call with large payload
      const response = await fetch('/api/roles/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ operation, roleIds }),
      });
      
      if (response.ok) {
        // Simulate processing large response
        const result = await response.json();
        
        if (operation === 'delete') {
          setRoles(prev => prev.filter(role => !roleIds.includes(role.id)));
        } else if (operation === 'activate') {
          setRoles(prev => prev.map(role => 
            roleIds.includes(role.id) ? { ...role, isActive: true } : role
          ));
        }
        
        setSelectedRoles([]);
      }
    } catch (error) {
      console.error('Bulk operation failed:', error);
    } finally {
      setLoading(false);
      const endTime = performance.now();
      console.log(`Bulk ${operation} on ${roleIds.length} roles took ${endTime - startTime} milliseconds`);
    }
  };

  // Performance-critical permission assignment
  const handleMassPermissionAssignment = async (roleIds: string[], permissionIds: string[]) => {
    const startTime = performance.now();
    setLoading(true);
    
    try {
      const response = await fetch('/api/roles/mass-assign-permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ roleIds, permissionIds }),
      });
      
      if (response.ok) {
        // Simulate updating roles with new permissions
        setRoles(prev => prev.map(role => 
          roleIds.includes(role.id) 
            ? { ...role, permissions: [...role.permissions, ...permissionIds] }
            : role
        ));
      }
    } catch (error) {
      console.error('Mass permission assignment failed:', error);
    } finally {
      setLoading(false);
      const endTime = performance.now();
      console.log(`Mass permission assignment took ${endTime - startTime} milliseconds`);
    }
  };

  const handleSelectAll = () => {
    const startTime = performance.now();
    
    if (selectedRoles.length === filteredRoles.length) {
      setSelectedRoles([]);
    } else {
      setSelectedRoles(filteredRoles.map(role => role.id));
    }
    
    const endTime = performance.now();
    console.log(`Select all operation took ${endTime - startTime} milliseconds`);
  };

  return (
    <div data-testid="large-dataset-component">
      <h1>Performance Test - {dataSize} Roles</h1>
      
      {loading && <div data-testid="loading">Loading...</div>}
      
      <div data-testid="controls">
        <input
          type="text"
          placeholder="Search roles..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          data-testid="search-input"
        />
        
        <button
          onClick={handleSelectAll}
          data-testid="select-all-btn"
        >
          {selectedRoles.length === filteredRoles.length ? 'Deselect All' : 'Select All'}
        </button>
        
        {selectedRoles.length > 0 && (
          <>
            <button
              onClick={() => handleBulkOperation('delete', selectedRoles)}
              data-testid="bulk-delete-btn"
            >
              Delete Selected ({selectedRoles.length})
            </button>
            
            <button
              onClick={() => handleBulkOperation('activate', selectedRoles)}
              data-testid="bulk-activate-btn"
            >
              Activate Selected ({selectedRoles.length})
            </button>
            
            <button
              onClick={() => handleMassPermissionAssignment(
                selectedRoles, 
                permissions.slice(0, 10).map(p => p.id)
              )}
              data-testid="mass-assign-btn"
            >
              Assign Permissions to Selected
            </button>
          </>
        )}
      </div>

      <div data-testid="stats">
        <p>Total Roles: {roles.length}</p>
        <p>Filtered Roles: {filteredRoles.length}</p>
        <p>Selected Roles: {selectedRoles.length}</p>
        <p>Total Permissions: {permissions.length}</p>
      </div>

      <div data-testid="roles-list">
        {filteredRoles.slice(0, 100).map((role) => (
          <div key={role.id} data-testid={`role-${role.id}`}>
            <input
              type="checkbox"
              checked={selectedRoles.includes(role.id)}
              onChange={() => {
                setSelectedRoles(prev => 
                  prev.includes(role.id)
                    ? prev.filter(id => id !== role.id)
                    : [...prev, role.id]
                );
              }}
            />
            <span>{role.name}</span>
            <span>({role.permissions.length} permissions)</span>
            <span>{role.isActive ? 'Active' : 'Inactive'}</span>
          </div>
        ))}
        {filteredRoles.length > 100 && (
          <div data-testid="pagination-info">
            Showing 100 of {filteredRoles.length} roles
          </div>
        )}
      </div>
    </div>
  );
};

describe('Large Dataset Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
    
    // Mock successful API responses
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ success: true }),
    });
  });

  it('should handle 1000 roles efficiently', async () => {
    const startTime = performance.now();
    
    render(<LargeDatasetComponent dataSize={1000} />);
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    expect(screen.getByTestId('large-dataset-component')).toBeInTheDocument();
    expect(screen.getByText('Performance Test - 1000 Roles')).toBeInTheDocument();
    expect(screen.getByText('Total Roles: 1000')).toBeInTheDocument();
    expect(screen.getByText('Total Permissions: 2000')).toBeInTheDocument();
    
    // Performance assertion: rendering should be under 2000ms
    expect(renderTime).toBeLessThan(2000);
    console.log(`Rendering 1000 roles took ${renderTime} milliseconds`);
  });

  it('should handle search filtering efficiently', async () => {
    const user = userEvent.setup();
    render(<LargeDatasetComponent dataSize={5000} />);
    
    const searchInput = screen.getByTestId('search-input');
    
    const startTime = performance.now();
    await user.type(searchInput, 'Role 1');
    const endTime = performance.now();
    
    const searchTime = endTime - startTime;
    
    // Performance assertion: search should be under 1000ms
    expect(searchTime).toBeLessThan(1000);
    console.log(`Search filtering 5000 roles took ${searchTime} milliseconds`);
    
    // Verify search results
    expect(screen.getByText(/Filtered Roles:/)).toBeInTheDocument();
  });

  it('should handle bulk selection efficiently', async () => {
    const user = userEvent.setup();
    render(<LargeDatasetComponent dataSize={2000} />);
    
    const selectAllBtn = screen.getByTestId('select-all-btn');
    
    const startTime = performance.now();
    await user.click(selectAllBtn);
    const endTime = performance.now();
    
    const selectionTime = endTime - startTime;
    
    // Performance assertion: bulk selection should be under 500ms
    expect(selectionTime).toBeLessThan(500);
    console.log(`Bulk selection of 2000 roles took ${selectionTime} milliseconds`);
    
    // Verify selection
    expect(screen.getByText('Selected Roles: 2000')).toBeInTheDocument();
  });

  it('should handle bulk operations efficiently', async () => {
    const user = userEvent.setup();
    render(<LargeDatasetComponent dataSize={1000} />);
    
    // Select all roles first
    await user.click(screen.getByTestId('select-all-btn'));
    
    const startTime = performance.now();
    await user.click(screen.getByTestId('bulk-delete-btn'));
    
    // Wait for operation to complete
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
    
    const endTime = performance.now();
    const operationTime = endTime - startTime;
    
    // Performance assertion: bulk operation should be under 2000ms
    expect(operationTime).toBeLessThan(2000);
    console.log(`Bulk delete of 1000 roles took ${operationTime} milliseconds`);
    
    // Verify API call
    expect(global.fetch).toHaveBeenCalledWith('/api/roles/bulk', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: expect.stringContaining('delete'),
    });
  });

  it('should handle mass permission assignment efficiently', async () => {
    const user = userEvent.setup();
    render(<LargeDatasetComponent dataSize={500} />);
    
    // Select all roles
    await user.click(screen.getByTestId('select-all-btn'));
    
    const startTime = performance.now();
    await user.click(screen.getByTestId('mass-assign-btn'));
    
    // Wait for operation to complete
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
    
    const endTime = performance.now();
    const assignmentTime = endTime - startTime;
    
    // Performance assertion: mass assignment should be under 1500ms
    expect(assignmentTime).toBeLessThan(1500);
    console.log(`Mass permission assignment took ${assignmentTime} milliseconds`);
    
    // Verify API call
    expect(global.fetch).toHaveBeenCalledWith('/api/roles/mass-assign-permissions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: expect.stringContaining('roleIds'),
    });
  });
});
