/**
 * Integration Tests for Role Management Workflow
 * Tests end-to-end scenarios for role creation, editing, and management
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Mock Next.js router
const mockPush = jest.fn();
const mockReplace = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: mockReplace,
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    prefetch: jest.fn(),
  }),
  usePathname: () => '/roles',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock fetch for API calls
global.fetch = jest.fn();

// Mock role data
const mockRoles = [
  {
    id: '1',
    name: 'Admin',
    description: 'Administrator role',
    hierarchy: 1,
    isActive: true,
    permissions: [],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'User',
    description: 'Regular user role',
    hierarchy: 2,
    isActive: true,
    permissions: [],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

const mockPermissions = [
  {
    id: '1',
    name: 'users.read',
    description: 'Read users',
    module: 'users',
    feature: 'read',
    action: 'read',
    resource: 'user',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: '2',
    name: 'users.write',
    description: 'Write users',
    module: 'users',
    feature: 'write',
    action: 'write',
    resource: 'user',
    isActive: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

// Simple Role Management Component for testing
const RoleManagementPage = () => {
  const [roles, setRoles] = React.useState(mockRoles);
  const [permissions, setPermissions] = React.useState(mockPermissions);
  const [loading, setLoading] = React.useState(false);
  const [showCreateForm, setShowCreateForm] = React.useState(false);
  const [editingRole, setEditingRole] = React.useState(null);

  const handleCreateRole = async (roleData: any) => {
    setLoading(true);
    try {
      // Simulate API call
      const response = await fetch('/api/roles', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(roleData),
      });
      
      if (response.ok) {
        const newRole = await response.json();
        setRoles(prev => [...prev, newRole]);
        setShowCreateForm(false);
      }
    } catch (error) {
      console.error('Error creating role:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditRole = async (roleId: string, roleData: any) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/roles/${roleId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(roleData),
      });
      
      if (response.ok) {
        const updatedRole = await response.json();
        setRoles(prev => prev.map(role => 
          role.id === roleId ? updatedRole : role
        ));
        setEditingRole(null);
      }
    } catch (error) {
      console.error('Error updating role:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteRole = async (roleId: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/roles/${roleId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        setRoles(prev => prev.filter(role => role.id !== roleId));
      }
    } catch (error) {
      console.error('Error deleting role:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAssignPermissions = async (roleId: string, permissionIds: string[]) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/roles/${roleId}/permissions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ permissionIds }),
      });
      
      if (response.ok) {
        const updatedRole = await response.json();
        setRoles(prev => prev.map(role => 
          role.id === roleId ? updatedRole : role
        ));
      }
    } catch (error) {
      console.error('Error assigning permissions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkDelete = async (roleIds: string[]) => {
    setLoading(true);
    try {
      const response = await fetch('/api/roles/bulk-delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ roleIds }),
      });
      
      if (response.ok) {
        setRoles(prev => prev.filter(role => !roleIds.includes(role.id)));
      }
    } catch (error) {
      console.error('Error bulk deleting roles:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div data-testid="role-management-page">
      <h1>Role Management</h1>
      
      {loading && <div data-testid="loading">Loading...</div>}
      
      <button 
        onClick={() => setShowCreateForm(true)}
        data-testid="create-role-btn"
      >
        Create New Role
      </button>

      {showCreateForm && (
        <div data-testid="create-role-form">
          <h2>Create Role</h2>
          <form onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.target as HTMLFormElement);
            handleCreateRole({
              name: formData.get('name'),
              description: formData.get('description'),
              hierarchy: parseInt(formData.get('hierarchy') as string),
              isActive: true,
            });
          }}>
            <input name="name" placeholder="Role Name" required />
            <input name="description" placeholder="Description" required />
            <input name="hierarchy" type="number" placeholder="Hierarchy Level" required />
            <button type="submit" data-testid="submit-create-role">Create</button>
            <button type="button" onClick={() => setShowCreateForm(false)}>Cancel</button>
          </form>
        </div>
      )}

      <div data-testid="roles-list">
        {roles.map(role => (
          <div key={role.id} data-testid={`role-${role.id}`}>
            <h3>{role.name}</h3>
            <p>{role.description}</p>
            <p>Level: {role.hierarchy}</p>
            <p>Status: {role.isActive ? 'Active' : 'Inactive'}</p>
            
            <button 
              onClick={() => setEditingRole(role)}
              data-testid={`edit-role-${role.id}`}
            >
              Edit
            </button>
            
            <button 
              onClick={() => handleDeleteRole(role.id)}
              data-testid={`delete-role-${role.id}`}
            >
              Delete
            </button>
            
            <button 
              onClick={() => handleAssignPermissions(role.id, ['1', '2'])}
              data-testid={`assign-permissions-${role.id}`}
            >
              Assign Permissions
            </button>
          </div>
        ))}
      </div>

      <div data-testid="bulk-actions">
        <button 
          onClick={() => handleBulkDelete(['1', '2'])}
          data-testid="bulk-delete-btn"
        >
          Bulk Delete Selected
        </button>
      </div>

      {editingRole && (
        <div data-testid="edit-role-form">
          <h2>Edit Role</h2>
          <form onSubmit={(e) => {
            e.preventDefault();
            const formData = new FormData(e.target as HTMLFormElement);
            handleEditRole(editingRole.id, {
              name: formData.get('name'),
              description: formData.get('description'),
              hierarchy: parseInt(formData.get('hierarchy') as string),
              isActive: formData.get('isActive') === 'on',
            });
          }}>
            <input name="name" defaultValue={editingRole.name} required />
            <input name="description" defaultValue={editingRole.description} required />
            <input name="hierarchy" type="number" defaultValue={editingRole.hierarchy} required />
            <input name="isActive" type="checkbox" defaultChecked={editingRole.isActive} />
            <button type="submit" data-testid="submit-edit-role">Update</button>
            <button type="button" onClick={() => setEditingRole(null)}>Cancel</button>
          </form>
        </div>
      )}
    </div>
  );
};

describe('Role Management Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (global.fetch as jest.Mock).mockClear();
  });

  it('should render role management page', () => {
    render(<RoleManagementPage />);
    
    expect(screen.getByTestId('role-management-page')).toBeInTheDocument();
    expect(screen.getByText('Role Management')).toBeInTheDocument();
    expect(screen.getByTestId('create-role-btn')).toBeInTheDocument();
  });

  it('should display existing roles', () => {
    render(<RoleManagementPage />);

    expect(screen.getByTestId('role-1')).toBeInTheDocument();
    expect(screen.getByTestId('role-2')).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument();
    expect(screen.getByText('User')).toBeInTheDocument();
  });

  it('should handle role creation workflow', async () => {
    const user = userEvent.setup();

    // Mock successful API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        id: '3',
        name: 'Manager',
        description: 'Manager role',
        hierarchy: 3,
        isActive: true,
        permissions: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      }),
    });

    render(<RoleManagementPage />);

    // Click create role button
    await user.click(screen.getByTestId('create-role-btn'));

    // Verify form appears
    expect(screen.getByTestId('create-role-form')).toBeInTheDocument();

    // Fill form
    await user.type(screen.getByPlaceholderText('Role Name'), 'Manager');
    await user.type(screen.getByPlaceholderText('Description'), 'Manager role');
    await user.type(screen.getByPlaceholderText('Hierarchy Level'), '3');

    // Submit form
    await user.click(screen.getByTestId('submit-create-role'));

    // Verify API call
    expect(global.fetch).toHaveBeenCalledWith('/api/roles', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Manager',
        description: 'Manager role',
        hierarchy: 3,
        isActive: true,
      }),
    });

    // Wait for role to appear in list
    await waitFor(() => {
      expect(screen.getByText('Manager')).toBeInTheDocument();
    });
  });

  it('should handle role editing workflow', async () => {
    const user = userEvent.setup();

    // Mock successful API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        id: '1',
        name: 'Super Admin',
        description: 'Updated admin role',
        hierarchy: 1,
        isActive: true,
        permissions: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      }),
    });

    render(<RoleManagementPage />);

    // Click edit button for first role
    await user.click(screen.getByTestId('edit-role-1'));

    // Verify edit form appears
    expect(screen.getByTestId('edit-role-form')).toBeInTheDocument();

    // Update form fields
    const nameInput = screen.getByDisplayValue('Admin');
    await user.clear(nameInput);
    await user.type(nameInput, 'Super Admin');

    const descInput = screen.getByDisplayValue('Administrator role');
    await user.clear(descInput);
    await user.type(descInput, 'Updated admin role');

    // Submit form
    await user.click(screen.getByTestId('submit-edit-role'));

    // Verify API call
    expect(global.fetch).toHaveBeenCalledWith('/api/roles/1', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Super Admin',
        description: 'Updated admin role',
        hierarchy: 1,
        isActive: true,
      }),
    });

    // Wait for updated role to appear
    await waitFor(() => {
      expect(screen.getByText('Super Admin')).toBeInTheDocument();
    });
  });

  it('should handle role deletion workflow', async () => {
    const user = userEvent.setup();

    // Mock successful API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
    });

    render(<RoleManagementPage />);

    // Verify role exists
    expect(screen.getByTestId('role-1')).toBeInTheDocument();

    // Click delete button
    await user.click(screen.getByTestId('delete-role-1'));

    // Verify API call
    expect(global.fetch).toHaveBeenCalledWith('/api/roles/1', {
      method: 'DELETE',
    });

    // Wait for role to be removed
    await waitFor(() => {
      expect(screen.queryByTestId('role-1')).not.toBeInTheDocument();
    });
  });

  it('should handle permission assignment workflow', async () => {
    const user = userEvent.setup();

    // Mock successful API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        id: '1',
        name: 'Admin',
        description: 'Administrator role',
        hierarchy: 1,
        isActive: true,
        permissions: mockPermissions,
        createdAt: new Date(),
        updatedAt: new Date(),
      }),
    });

    render(<RoleManagementPage />);

    // Click assign permissions button
    await user.click(screen.getByTestId('assign-permissions-1'));

    // Verify API call
    expect(global.fetch).toHaveBeenCalledWith('/api/roles/1/permissions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ permissionIds: ['1', '2'] }),
    });
  });

  it('should handle bulk delete workflow', async () => {
    const user = userEvent.setup();

    // Mock successful API response
    (global.fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
    });

    render(<RoleManagementPage />);

    // Verify roles exist
    expect(screen.getByTestId('role-1')).toBeInTheDocument();
    expect(screen.getByTestId('role-2')).toBeInTheDocument();

    // Click bulk delete button
    await user.click(screen.getByTestId('bulk-delete-btn'));

    // Verify API call
    expect(global.fetch).toHaveBeenCalledWith('/api/roles/bulk-delete', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ roleIds: ['1', '2'] }),
    });

    // Wait for roles to be removed
    await waitFor(() => {
      expect(screen.queryByTestId('role-1')).not.toBeInTheDocument();
      expect(screen.queryByTestId('role-2')).not.toBeInTheDocument();
    });
  });

  it('should show loading state during operations', async () => {
    const user = userEvent.setup();

    // Mock delayed API response
    (global.fetch as jest.Mock).mockImplementation(() =>
      new Promise(resolve => setTimeout(() => resolve({ ok: true }), 100))
    );

    render(<RoleManagementPage />);

    // Click delete button
    await user.click(screen.getByTestId('delete-role-1'));

    // Verify loading state appears
    expect(screen.getByTestId('loading')).toBeInTheDocument();

    // Wait for operation to complete
    await waitFor(() => {
      expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
    });
  });
});
